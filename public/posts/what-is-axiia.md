---
title: 'Axiia周弋涵在嘉程创业流水席的分享:人类究竟能否用好AI？'
date: '2024/10/09'
author: 'Yihan'
public: true
---

**一、项目背景**

我们的项目 Axiia 旨在解决“很多人用不好 AI”这个问题。

为什么这件事很重要呢？

- 未来所有还需要人类参与的工作都需要与 AI 协作，每个人、每个岗位都需要掌握使用 AI 的技能。
- 很多人不擅长使用 AI，在一个公司里，有些人使用 AI 非常频繁，提高效率很多，而有些人觉得 AI 没有什么用处，这种差距是客观存在的。

**二、解决方向**

我们希望从两个方向解决这个问题：

1. 从人的方向解决；
2. 从 AI 的方向解决。

**三、Axiia 产品矩阵**

Axiia 现在有一个简单的产品矩阵：

1. **人的方向**：评估提示词技巧以及核心素养。我们先进行评估而不是教育，因为评估操作性地定义了教育的可衡量的目标，也因为我个人认为 AI+教育的形态还有很大的不确定性。
2. **AI 方向**：结构化访谈。很多人用不好 AI 是因为无法清晰表达自己想让 AI 做什么，也不清楚 AI 能为他们做什么，一个好的提问 Agent 可以引导用户表达自己的需求，也可以提供关于 AI 能力边界的信息。高质量提问本身需要专门的优化，并不是 LLM 本身擅长的事情。
3. **两者结合**：实时地给用户使用任何 AI 产品一个轻度的评估反馈，让用户可以实时调整自己的使用方式，然后根据用户意图，提出恰当的问题，帮助用户更好地表达。比如，用户在用 GPT 写 Terminal command 的时候，如果要求太模糊，Axiia 可以实时地指出，然后询问用户要实现的目标。

**四、提示词工程重要性**

回到具体的分享，先沿着产品的思路。我们的第一个产品是评估提示词工程，很多人会问，提示词工程这个能力是不是很快就不重要了？是不是随着大语言模型的发展，这个事情就不需要了？我个人并不认同这个观点，但这个问题确实一直存在。这次在旧金山，我见到了一位 Anthropic 资深的全职提示词工程师。Anthropic 内部非常重视提示词工程师这个岗位，他们需要开发一些内部产品，也要测试不同提示词在模型上的表达效果。

我们就此讨论了一些内容，把工程能力分成两类：

- 一类能力是将来可能没有用的，这类能力主要是用来弥补当前模型不够强的。例如，现在大部分模型都做不好 24 点的题目，但通过使用复杂的提示词可以解决这个问题。这属于弥补模型本身能力的范畴，但随着大模型的增强，这种能力可能就没有用了。
- 还有一些能力是无论模型多么强大都需要的。
  - 总体来说，要成为一个好的写作者，写出的内容应该清晰易懂。
  - 要有能力提出对目标有足够代表性的例子。在提示词中，few shot 是一个常见的做法，但 few shot 本身并不能保证提示词的效果，这取决于例子的质量。例子的质量包括其清晰程度，以及是否能代表你想要表达的各种可能性，而写好例子在长远来看一直都可以提升效果。
  - 能否总是提供足够相关的上下文。这里的关键点是不要提供过多的上下文。很多时候，我们会给模型很多很长的上下文，认为模型可以处理这么长的内容。但实际上，给它过长的上下文时，它更容易出现幻觉，从某种意义上来说是在分散模型解决问题的注意力。因此，提示词工程师需要判断哪些内容是最相关的。不仅是与 AI 交流时如此，与人交流时也是如此。如果总是说对方已经知道的内容，对方也会听不进去，所以这样的模型表现会更可信。

**五、国内外 Agent 落地行业**

接下来回答第二个问题：目前国内外 Agent 落地的行业有哪些？

我个人感觉，至少在北美有几个明确的落地方向，包括教育、法律、代码生成，RAG 领域中现在也有非常多的 Agent。我分别简单介绍一下。首先，这些领域都有一个共同特点，它们现在都是纯文本的，肯定不是多模态的。多模态例如涉及视频和具身的应用，目前还不是特别落地。

1. **法律方面**：美国有很多专门的 AI+法律公司，包括生成垂直场景的文档以及垂直场景法律的问答。这些工作做得还挺多的，但很多项目并不是创业公司在做，而是法律咨询公司内部的 AI 团队在进行。他们拥有大量数据来支持这些工作。我确实没有看到法律领域有特别多由创业公司主导的项目。
2. **教育方面**：创业公司还是蛮多的。不论是教英语、绘本讲故事，还是针对儿童教育，这些领域本身难度不高，容错率较高，所以做得比较多。
3. **代码生成方面**：这是一个非常大的领域，GitHub 也在做。目前的趋势是，AI 肯定能做好短代码片段的生成，但现在大家想解决的是长项目层面的生成。例如，对于一个已经有十几个文件的前端项目，给它提一个需求，AI 能否自动分析该修改代码的哪个部分，并将其添加到哪个文件中。这可能是目前正在落地的代码生成方向。GitHub 正在做这个事情，可能很快就会上线，叫做 GitHub Workspace。
4. **RAG 方面**：如果做最简单的 RAG，把问题变成一个向量，去找向量数据库中的相似向量，然后检索出来，用语言模型回答。复杂的 RAG 则是将其做得像 Agent 一样，给 RAG 本身增加一些推理能力。例如，这个过程需要判断要检索什么内容，检索完之后，判断检索的内容是否足够，如果不够，再判断需要检索什么内容。整个 RAG 过程可以是循环的、有推理能力的，并且是可以迭代的，这能够显著提升 RAG 的效果。

以上就是我认为一些 Agent 落地的行业。

**六、GPT-4o 加速 Agent 赛道**

第三个问题：GPT-4o 多模态模型问世对 Agent 的赛道有加速作用吗？

1. 我前天听了一个内部项目分享，他们以前有一个 workflow，本来工作得很好，但那些 prompt 都是为 GPT-4 设计的。当模型升级到 GPT-4o 时，发现整体表现下降了大约 10 个百分点。他们发现，为 GPT-4 优化的 prompt 在 GPT-4o 上表现更差了。所以提出了一个观点，如果对 prompt 进行深度优化，可能会被具体的模型锁住，因为当 prompt 优化到一定程度时，就开始为特定模型优化，而不是做通用优化。最后的解决方案是，把他们的 prompt 版本往前回退了一下，发现更早的、没有深度优化的 prompt 在 GPT-4o 上表现得更好。因为 GPT-4o 毕竟便宜，为了 GPT-4o 的价格，他们肯定还是要切换的。为了切换，反而效果下降，做了一些额外的工作，把之前对 GPT-4 的一些优化去掉了。这可能不是加速作用，是短期的减速作用，但长期肯定还是加速。
2. GPT-4o 显然在多模态能力上有很大提升。前几天，GPT-4o 发布了一个视频，展示了 GPT-4o 对视频的实时理解。Demo 展示可以让 GPT-4o 共享屏幕，实时理解屏幕里的内容。这会有很多想象力发挥的空间，可以做出新的东西。这个 Demo 的效果很好，但实际使用时的延迟可能会是一个问题。

**七、工程化解决模型能力不足的问题**

第四个问题：如何工程化地解决模型能力暂且不足的问题？

这个问题在大部分的大模型厂商或者很多人看来，最好的办法是不解决。如果问 Anthropic 或 OpenAI 的人，他们肯定建议不要解决这些问题。因为解决这些问题是很浪费的，可能半年后你的解决方案就白费了。至少我们作为创业者，如果眼前有一个需求，大模型满足不了，会想把它自动化地解决。当然，如果真的是这样，可能说明现在不该做这个产品，如果实在想做这个产品，最好不要做非常系统化的解决方案。能靠更长的提示词解决，就不要微调，因为当模型一旦变强，你的解决方法就变得没有价值了，所以尽量不解决。

还有一种解决方式是靠人来解决。比如有一些 AI+HR 的公司，他们会用大量的人力来支持 AI 做不到的事情。我觉得这也没什么问题，因为 AI 变强之后，这些人就可以被取代掉。但现在 AI 不够强，他们有一些流程还是让人在做，雇猎头来做一些流程。这也没有问题，因为只要把 PMF 跑通了，AI 化反而可能是更简单的事情。

所以，关于如何工程化地解决模型能力暂时不足的问题，首先，如果工程化的话，尽可能简单地工程化。其次，也不一定非要工程化，可以用人力解决。最后，也可以选择不解决。

**八、AI 系统评估的重要性和方法**

最后再讲一个大的点，现在关于 Agent 和 AI 的讨论中，至少从工程角度来看，大家讲得最多的就是评估，即对 AI 系统的评价。不论是做 RAG，还是更复杂的 Agent 工作流。我的感受是，相比去年，大家很多时候写提示词是凭感觉进行开发。那样做一般来说稳定性很差，可能做成一个看似能用的东西，但在生产环境中就不敢用，也不知道如何迭代，因为没有一个明确的指标。

所以现在一般来说，先定义系统的表现目标，比开发系统更重要，也更优先。当然，定义的方式有很多，比如至少要有一组高质量的样本，这些样本不是用来训练的，而是用来评估你的系统是否达到了目标，并且样本本身要不断积累。

比如之前有一个团队，他们为企业内部做 Web 系统，但企业反馈他们的 Chatbot 在一些特定情况下，对用户的发言过于不礼貌，或者让用户感觉被冒犯。他们就收集了所有让用户觉得冒犯的发言背后的输入，把输入和发言的最终输出加入评估样本中。确保在之前所有出问题的样本上，新的系统都不能再出问题。评估肯定要不断迭代，刚开始就应该有足够好的样本集，并根据之后的用户反馈不断在样本集中添加内容。这个项目最有价值的东西之一可能就是样本集，因为它可以用来保证整个系统的正确方向。

另外，可能需要自动打分的功能。显然，样本不可能覆盖所有情况，样本只是有限的一些案例。但在生产环境中，或者在其他的训练环境中，肯定会有一些用户的输入输出不在样本集中，所以至少要有一些自动化的打分系统。最简单的打分系统可以用大语言模型本身去打分，但这种打分本身可能也有一定的偏差，但比没有要好很多。

如果有更传统的方法打分就更好。举个例子，比如在 RAG 中，如果想评估 RAG 的质量，可以看最终回答这个问题时是否用到了上下文。例如，用 RAG 做问答，最后 AI 回答了一个问题，这个回答是否用到了检索的上下文。这个事情大语言模型判断一般不会出错。这就属于评估过程，如果发现你的回答总是用不到检索的内容，说明检索没有派上用场，要么是没有必要，要么就是检索错了。但整体来看，设置一些算法自动化的打分系统是非常重要的。

最后，当然也需要一些人工打分。应该把 Agent 工作流中的一些部分，先用大语言模型判断是否比较模糊，有可能出问题的部分，定期拿出一些数据让人来评估。这样才能发现问题，人做完评估后可以把它加回样本集，或者改进大语言模型或算法，这也是非常重要的。人除了做评估，还需要经常查看整体 Agent 系统的数据，比如工作流中，每步传递给下一步的是什么样的文字，下一步是怎么处理的。当然，如何让系统帮助选择那些最值得被查看的内容，降低查看数据的人的工作量，是个工程问题。但整体来说，人的判断目前对开发一个好的 Agent 还是必不可少的，现在需要经常做，也有助于公司员工的提升和成长。

以上是我关于 LLM 的分享，谢谢。

**九、Q&A**

1. **席友**：怎么看 AI Agent+RPA 这个方向？
   - **周弋涵**：AI Agent+RPA 的方向，我在美国这边倒没怎么听说，但我在国内的一些活动里经常听到。这里可以展开一个点，AI 开发有两派，一类人喜欢端到端解决问题，另一类人喜欢在中间加形式化的语言来解决问题。那些最偏好端到端的人甚至觉得自然语言都是不必要的，直接参数对参数，比如在两个模型之间沟通一下参数，可能就能解决问题。
   - 首先，我自己比较偏向于用形式化语言来解决问题的一派，我个人并不觉得纯粹的端到端是好的。为什么与 RPA 相关？因为传统的 RPA 一般就是写代码，用代码标准化流程，自动化就是通过代码实现的。我觉得这些代码其实还是挺必要的，首先保证了稳定性，确保执行不会出错。
   - 其次，这些代码需要人来写，可能会很花功夫。但现在大语言模型可以写，它们也可以理解和修改这些代码。包括刚才提到的代码生成领域，这个领域一般指的是写 Python 代码或者传统代码，但让它写 RPA 代码，当然也是完全没有问题的。
   - 所以我个人非常看好 Agent+RPA 方向，甚至将来可能都不需要 RPA 公司来做。以前需要 RPA 公司，是因为 RPA 开发很难，需要找人开发。以后可能只需描述一下你的流程，如果描述得足够清楚，RPA 代码就可以被写出来，不需要专门的 RPA 公司，不需要有人开发 RPA 的语言，或者开发整个 RPA 对 AI 的框架，RPA 的商业形式会发生一些变化。
2. **席友**：RAG 如何解决超长上下文？
   - **周弋涵**：首先，我自己的项目不用 RAG，这也是我选择这个方向的一个理由。我觉得如何解决长上下文这个问题，目前还没有明确的共识。然而，RAG 是一个好的方向。但在评估这个问题时，我们绕开了长上下文，因为与一个人的语境大约只有一个小时，无论是让它做题，还是与它面试，文本也就几万字。在这个尺度上，不太需要使用 RAG。因此，我们现在的项目完全不使用任何 RAG，这会省去很多工程层面的麻烦。
   - 但是，如果使用 RAG 的话，在一些自己的试手项目里，我比较喜欢 DSPy 这个框架，它能自己帮你优化 prompt。
   - 这里补充一下，prompt 这个事情，显然将来的目标也不是让人手写所有的 prompt，prompt 本身肯定是 AI 可以写的。但即使 AI 可以写 prompt，你仍然需要操作这些能写 AI 的 prompt 来指挥它们写 prompt。指挥本身也并不简单，至少作为工程团队，应该学会如何使用 AI，用代码帮助自己生成足够好的 prompt。我比较推荐 DSPy 框架。
