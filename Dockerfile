# Build stage
FROM node:20.17.0 AS build-stage
WORKDIR /usr/src/app
COPY ["package.json", "pnpm-lock.yaml", "./"]

RUN npm install -g corepack@latest

RUN corepack enable
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
RUN pnpm prisma generate
RUN ./scripts/save_prisma_client.sh

# Run stage
FROM node:20.17.0-slim
RUN apt-get update -y && apt-get install -y openssl ffmpeg
WORKDIR /usr/src/app
COPY package.json ./package.json
COPY pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=build-stage /usr/src/app/build ./build
COPY --from=build-stage /usr/src/app/locales ./locales

RUN npm install -g corepack@latest

RUN corepack enable && pnpm install --prod
COPY --from=build-stage /usr/src/app/tmp-prisma-client ./tmp-prisma-client
COPY scripts ./scripts
RUN ./scripts/restore_prisma_client.sh
RUN rm -r ./tmp-prisma-client
RUN rm -r ./scripts
CMD ["pnpm", "remix-serve", "./build/main/server/index.js"]