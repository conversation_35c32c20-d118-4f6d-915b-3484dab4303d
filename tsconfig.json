{
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/.server/**/*.ts",
    "src/**/.server/**/*.tsx",
    "src/**/.client/**/*.ts",
    "src/**/.client/**/*.tsx"
  ],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": [
      "@remix-run/node", 
      "vite/client",
      "vitest/globals",
      "@modyfi/vite-plugin-yaml/modules",
    ],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@deps/*": ["./src/common/deps.server/*"],
      "@admin/*": ["./src/admin/*"],
      "@main/*": ["./src/main/*"],
      "@common/*": ["./src/common/*"],
      "@axiia-ui/*": ["./src/axiia-ui/*"]
    },

    // Vite takes care of building everything, not tsc.
    "noEmit": true,
    "plugins": [
      {
        "name": "typescript-remix-routes-plugin"
      },
      {
        "name": "typescript-plugin-css-modules",
        "options": { "classnameTransform": "camelCaseOnly" }
      },
    ]
  }
}
