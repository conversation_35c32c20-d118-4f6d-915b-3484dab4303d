{"name": "axiia-website", "private": true, "sideEffects": false, "type": "module", "scripts": {"build:admin": "cross-env PROJECT=admin remix vite:build --config=admin.vite.config.ts", "build:main": "cross-env PROJECT=main remix vite:build --config=main.vite.config.ts", "build": "pnpm build:admin && pnpm build:main", "dev:admin": "cross-env PROJECT=admin remix vite:dev --config=admin.vite.config.ts --port 5189", "dev:main": "cross-env PROJECT=main remix vite:dev --config=main.vite.config.ts --port 5188", "start:admin": "remix-serve ./build/admin/server/index.js", "start:main": "remix-serve ./build/main/server/index.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint --ignore-path .gitignore --fix .", "typecheck": "tsc", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "add-shadcn": "pnpx shadcn-ui@latest add", "prepare": "if [[ -x \"$(command -v husky)\" ]]; then  husky install; fi"}, "dependencies": {"@aws-sdk/client-kms": "^3.844.0", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/client-secrets-manager": "^3.844.0", "@hono/node-server": "^1.15.0", "@hono/zod-openapi": "^0.17.1", "@hookform/resolvers": "^3.10.0", "@prisma/adapter-pg": "^6.11.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@remix-run/node": "^2.16.8", "@remix-run/react": "^2.16.8", "@remix-run/serve": "^2.16.8", "@remix-sse/server": "^1.0.1", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fast-deep-equal": "^3.1.3", "gray-matter": "^4.0.3", "hono": "^4.8.4", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.2.0", "i18next-fs-backend": "^2.6.0", "i18next-http-backend": "^2.7.3", "immer": "^10.1.1", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "isbot": "^4.4.0", "jotai": "^2.12.5", "jotai-immer": "^0.4.1", "lucide-react": "^0.408.0", "nodemailer": "^6.10.1", "nunjucks": "^3.2.4", "openai": "^4.104.0", "pg": "^8.16.3", "pino": "^9.7.0", "pino-pretty": "^11.3.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-i18next": "^14.1.3", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "remix-i18next": "^6.4.1", "remix-routes": "^1.7.7", "remix-utils": "^7.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "zod": "^3.25.76"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@eslint/js": "^9.30.1", "@modyfi/vite-plugin-yaml": "^1.1.1", "@remix-run/dev": "^2.16.8", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/test": "^8.6.14", "@types/eslint__js": "^8.42.3", "@types/nodemailer": "^6.4.17", "@types/nunjucks": "^3.2.6", "@types/pg": "^8.15.4", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "dotenv": "^16.6.1", "esbuild": "^0.24.2", "esbuild-plugin-pino": "^2.2.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.11.1", "prisma-json-types-generator": "^3.5.1", "storybook": "^8.6.14", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "typescript-plugin-css-modules": "^5.1.0", "typescript-remix-routes-plugin": "^1.0.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9"}, "engines": {"node": ">=20.0.0"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "prisma": {"schema": "./prisma/schema"}}