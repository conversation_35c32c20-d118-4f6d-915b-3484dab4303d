import { defineConfig } from 'vite'
import { vitePlugin as remix } from '@remix-run/dev'
import tsconfigPaths from 'vite-tsconfig-paths'
import { remixRoutes } from 'remix-routes/vite'
import ViteYaml from '@modyfi/vite-plugin-yaml'

export default defineConfig({
  plugins: [
    remix({
      appDirectory: 'src/main',
      buildDirectory: 'build/main',
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
    remixRoutes({
      outDir: 'src/main/types',
    }),
    ViteYaml(),
  ],
})
