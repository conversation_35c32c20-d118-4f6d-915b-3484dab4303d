import { vitePlugin as remix } from '@remix-run/dev'
import { defineConfig } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import { remixRoutes } from 'remix-routes/vite'

export default defineConfig({
  plugins: [
    remix({
      appDirectory: 'src/admin',
      buildDirectory: 'build/admin',
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
    remixRoutes({
      outDir: 'src/admin/types',
    }),
  ],
})
