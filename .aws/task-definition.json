{"family": "axiia-app", "containerDefinitions": [{"name": "main", "image": "767398125647.dkr.ecr.us-west-2.amazonaws.com/axiia-app:{{IMAGE_TAG}}", "cpu": 0, "memory": 512, "portMappings": [{"name": "main-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "command": ["pnpm", "remix-serve", "./build/main/server/index.js"], "environment": [{"name": "STACK", "value": "prod"}, {"name": "REDIS_HOST", "value": "localhost"}, {"name": "AWS_REGION", "value": "us-west-2"}, {"name": "TABLE_NAME", "value": "perspective-users"}, {"name": "REDIS_PORT", "value": "6379"}, {"name": "PORT", "value": "3000"}, {"name": "ROOT_KEY_ID", "value": "ad1cc230-da73-4dfc-8c2e-941d9e193978"}, {"name": "EMAIL_SMTP_ARN", "value": "arn:aws:secretsmanager:us-west-2:767398125647:secret:axiia-website-email-compat-zQd15L"}, {"name": "DATABASE_USE_IAM_AUTH", "value": "0"}, {"name": "MAIN_WEBSITE_URL", "value": "https://axiia.ai"}, {"name": "API_WEBSITE_URL", "value": "https://api.axiia.ai"}, {"name": "ADMIN_WEBSITE_URL", "value": "https://admin.axiia.ai"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "JWT_SECRET_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/JWT_SECRET_KEY"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/OPENAI_API_KEY"}, {"name": "HELICONE_API_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/HELICONE_API_KEY"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/DATABASE_URL"}], "workingDirectory": "/usr/src/app", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/axiia-app", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "main"}}, "systemControls": []}, {"name": "admin", "image": "767398125647.dkr.ecr.us-west-2.amazonaws.com/axiia-app:{{IMAGE_TAG}}", "cpu": 0, "memory": 512, "portMappings": [{"name": "admin-3001-tcp", "containerPort": 3001, "hostPort": 3001, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "command": ["pnpm", "remix-serve", "./build/admin/server/index.js"], "environment": [{"name": "STACK", "value": "prod"}, {"name": "REDIS_HOST", "value": "localhost"}, {"name": "AWS_REGION", "value": "us-west-2"}, {"name": "TABLE_NAME", "value": "perspective-users"}, {"name": "REDIS_PORT", "value": "6379"}, {"name": "PORT", "value": "3001"}, {"name": "ROOT_KEY_ID", "value": "ad1cc230-da73-4dfc-8c2e-941d9e193978"}, {"name": "EMAIL_SMTP_ARN", "value": "arn:aws:secretsmanager:us-west-2:767398125647:secret:axiia-website-email-compat-zQd15L"}, {"name": "DATABASE_USE_IAM_AUTH", "value": "0"}, {"name": "MAIN_WEBSITE_URL", "value": "https://axiia.ai"}, {"name": "API_WEBSITE_URL", "value": "https://api.axiia.ai"}, {"name": "ADMIN_WEBSITE_URL", "value": "https://admin.axiia.ai"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "JWT_SECRET_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/JWT_SECRET_KEY"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/OPENAI_API_KEY"}, {"name": "HELICONE_API_KEY", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/HELICONE_API_KEY"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:us-west-2:767398125647:parameter/axiia/prod/DATABASE_URL"}], "workingDirectory": "/usr/src/app", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/axiia-app", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "admin"}}, "systemControls": []}, {"name": "redis", "image": "redis:7-alpine", "cpu": 0, "memory": 512, "portMappings": [{"containerPort": 6379, "hostPort": 6379, "protocol": "tcp"}], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/axiia-app", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "redis"}}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::767398125647:role/axiia-v6-ecs-task", "executionRoleArn": "arn:aws:iam::767398125647:role/axiia-v6-ecs-task-execution", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}}