import logger from '@deps/logger'
import { json, LoaderFunctionArgs } from '@remix-run/node'
import { loadTestStaticContent } from 'src/demo2/database.server/test-static-content'
import invariant from 'tiny-invariant'

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { id } = params
  invariant(id)
  logger.info(`start loading static content for test ${id}`)
  const data = await loadTestStaticContent(id)
  logger.info(`finished loading static content for test ${id}`)
  return json(data)
}
