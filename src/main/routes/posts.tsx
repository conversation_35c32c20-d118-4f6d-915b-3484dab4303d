import { getPosts } from '@main/services/post.server'
import { json } from '@remix-run/node'
import { Link, Outlet, useLoaderData, useLocation } from '@remix-run/react'

export const loader = async () => {
  const posts = await getPosts()
  return json({ posts })
}

export default function BlogPosts() {
  const { posts } = useLoaderData<typeof loader>()
  const location = useLocation()
  const isPostList = location.pathname === '/posts'

  return (
    <div className="min-h-screen flex flex-col mt-16 grainy-light">
      {isPostList ? (
        <div className="overflow-y-auto">
          <ul className="space-y-8 max-w-3xl mx-auto px-4 py-8 mb-20">
            {posts.length ? (
              posts.map(
                (post) =>
                  post && (
                    <li key={post.slug} className="border-b pb-8 last:border-b-0">
                      <h2 className="text-2xl font-bold mb-2 truncate hover:text-primary">
                        <Link
                          to={`/posts/${post.slug}`}
                          prefetch="intent"
                          className="block overflow-hidden text-ellipsis whitespace-nowrap"
                        >
                          {post.title}
                        </Link>
                      </h2>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <span>{post.author}</span>
                        <span>{post.date}</span>
                      </div>
                    </li>
                  ),
              )
            ) : (
              <div>No Article</div>
            )}
          </ul>
        </div>
      ) : (
        <Outlet />
      )}
    </div>
  )
}
