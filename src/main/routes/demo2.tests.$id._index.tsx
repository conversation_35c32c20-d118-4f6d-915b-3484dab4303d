import { useRouteLoaderData } from '@remix-run/react'
import { TestStaticContent } from 'src/demo2/models/test-static-content'
import DemoDone from './demo.done'
import DemoStart from 'src/demo2/components/DemoStart'

export const handle = {
  i18n: 'pea',
}

export default function Demo2Home() {
  const test = useRouteLoaderData<TestStaticContent>('routes/demo2.tests.$id')
  return test!.isComplete ? <DemoDone /> : <DemoStart />
}
