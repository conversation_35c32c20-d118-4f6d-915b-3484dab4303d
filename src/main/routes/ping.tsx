import { json } from '@remix-run/node'
import type { LoaderFunction } from '@remix-run/node'
import logger from '@deps/logger'
import getPrismaClient from '@deps/prisma-client'

export const loader: LoaderFunction = async () => {
  logger.info('/ping accessed!')

  const client = await getPrismaClient()
  const count = await client.perspectiveUser.count()
  return json({ message: `Hello, there are ${count} perspective users.` })
}
