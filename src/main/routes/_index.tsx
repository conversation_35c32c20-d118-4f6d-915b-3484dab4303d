import type { MetaFunction } from '@remix-run/node'
import { useRef } from 'react'
import { Footer } from '@main/components/home/<USER>'
import { TestimonialsSection } from '@main/components/home/<USER>'
import { TrustedBySection } from '@main/components/home/<USER>'
import { BenefitsSection } from '@main/components/home/<USER>'
import { FeatureSection } from '@main/components/home/<USER>'
import { Hero } from '@main/components/home/<USER>'

export const meta: MetaFunction = () => {
  return [
    { title: 'Axiia.ai - Unlock Your AI Potential' },
    { name: 'description', content: 'Evaluation as a Service for AI collaboration skills' },
  ]
}

export const handle = {
  i18n: 'home',
}

export default function Home() {
  const benefitsSectionRef = useRef<HTMLElement>(null)

  const scrollToBenefits = () => {
    benefitsSectionRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <main className="flex-grow overflow-auto pt-16 grainy-light">
      <Hero onLearnMoreClick={scrollToBenefits} />
      <BenefitsSection benefitsSectionRef={benefitsSectionRef} />
      <FeatureSection />
      <TrustedBySection />
      <TestimonialsSection />
      <Footer />
    </main>
  )
}
