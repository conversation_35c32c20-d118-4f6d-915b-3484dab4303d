import { assertWithHttpError } from '@common/utils'
import { ActionFunctionArgs, json, LoaderFunctionArgs, redirect } from '@remix-run/node'
import { useLoaderData, useRouteLoaderData } from '@remix-run/react'
import { z } from 'zod'
import MainPanel from 'src/demo2/components/MainPanel'
import { ProblemContext } from 'src/demo2/components/ProblemContext'
import { endTest } from 'src/demo2/database.server/test'
import {
  createChatRequestForProblem,
  createCustomChat,
  renameCustomChat,
  resetProblemConfigChat,
  uploadSubmission,
} from 'src/demo2/database.server/problem-progress'
import {
  loadProblemDynamicContent,
  runLabelPredTask,
} from 'src/demo2/database.server/test-dynamic-content'
import { getTestProblemIndex, useProblemStaticContent } from 'src/demo2/components/common'
import { createAndWaitForFeedback } from 'src/demo2/database.server/feedback'
import logger from '@deps/logger'
import { useState } from 'react'
import { atom, PrimitiveAtom } from 'jotai'
import { getServerTiming } from '@main/timing.server'
import { TestStaticContent } from 'src/demo2/models/test-static-content'
import CountdownTimer from 'src/demo2/components/CountdownTimer'

export const handle = {
  i18n: 'pea',
}

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { time, getServerTimingHeader } = getServerTiming()
  const testProblemIndex = getTestProblemIndex(params)
  const debugName = `${testProblemIndex.testId}:${testProblemIndex.n}`
  logger.info(`start loading dynamic content for problem ${debugName}`)
  const result = await time('loadTestDynamicContent', loadProblemDynamicContent(testProblemIndex))
  logger.info(`finished loading dynamic content for problem ${debugName}`)
  return json(result, {
    headers: getServerTimingHeader(),
  })
}

const actionSchema = z.discriminatedUnion('action', [
  z.object({
    action: z.literal('submit'),
    submission: z.string(),
    requestFeedback: z.string().optional(),
    newProblemIndex: z.string().optional(),
  }),
  z.object({
    action: z.literal('run-label-pred-task'),
    submission: z.string(),
    groupName: z.string(),
    pairNames: z.string(),
  }),
  z.object({
    action: z.literal('create-chat-request'),
    chatId: z.string(),
    input: z.string(),
  }),
  z.object({
    action: z.literal('create-bot'),
    prompt: z.string().optional(),
    sourcePresetId: z.string().optional(),
    title: z.string(),
  }),
  z.object({
    action: z.literal('rename-bot'),
    topic: z.string(),
    id: z.string(),
  }),
  z.object({
    action: z.literal('reset-chat'),
    chatId: z.string(),
  }),
  z.object({
    action: z.literal('final-submit'),
  }),
])

export const action = async ({ params, request }: ActionFunctionArgs) => {
  const testProblemIndex = getTestProblemIndex(params)
  const formData = await request.formData()
  const parseResult = actionSchema.safeParse(Object.fromEntries(formData.entries()))
  if (!parseResult.success) {
    console.log(Object.fromEntries(formData.entries()))
    console.log(parseResult.error)
  }
  assertWithHttpError(parseResult.success, 400)

  logger.info('test action parsed')

  switch (parseResult.data.action) {
    case 'submit': {
      const { submission, requestFeedback, newProblemIndex } = parseResult.data
      await uploadSubmission(testProblemIndex, submission)
      if (requestFeedback === 'true') {
        await createAndWaitForFeedback(testProblemIndex)
      }
      if (newProblemIndex !== undefined) {
        return redirect(`/demo2/tests/${testProblemIndex.testId}/problems/${newProblemIndex}`)
      }
      break
    }

    case 'run-label-pred-task': {
      const { submission, groupName, pairNames } = parseResult.data
      await uploadSubmission(testProblemIndex, submission)
      await runLabelPredTask(
        testProblemIndex,
        groupName,
        pairNames.split(',').map((x) => x.trim()),
      )
      break
    }

    case 'create-chat-request': {
      const { chatId, input } = parseResult.data
      await createChatRequestForProblem(testProblemIndex, chatId, input)
      break
    }

    case 'create-bot': {
      await createCustomChat(testProblemIndex, parseResult.data)
      break
    }

    case 'rename-bot': {
      const { id, topic } = parseResult.data
      await renameCustomChat(testProblemIndex, id, topic)
      break
    }

    case 'reset-chat': {
      const { chatId } = parseResult.data
      await resetProblemConfigChat(testProblemIndex, chatId)
      break
    }

    case 'final-submit':
      try {
        await endTest(testProblemIndex.testId)
        return redirect(`/demo2/tests/${testProblemIndex.testId}`)
      } catch (error) {
        logger.error(error, 'An error occurred when stepping the test.')
      }
      break
  }

  logger.info('test action processed')
  return null
}

export default function Demo2Problem() {
  const { progressId } = useLoaderData<typeof loader>()
  return <Demo2ProblemActual key={progressId} />
}

function Demo2ProblemActual() {
  const test = useRouteLoaderData<TestStaticContent>('routes/demo2.tests.$id')
  const dynamicContent = useLoaderData<typeof loader>()
  const staticContent = useProblemStaticContent()
  const [solutionAtom] = useState<PrimitiveAtom<string>>(() =>
    atom(dynamicContent.latestSubmission),
  )

  return (
    <ProblemContext.Provider
      value={{
        testId: staticContent.testId,
        problem: staticContent.problemVersion,
        solutionAtom: solutionAtom,
        allProblems: test?.allProblems || [],
      }}
    >
      {test?.problemSet?.expireMinutes && test?.firstProgressCreatedAt && (
        <CountdownTimer
          testId={staticContent.testId}
          startTime={test.firstProgressCreatedAt}
          expireMinutes={test.problemSet.expireMinutes}
        />
      )}
      <MainPanel staticContent={staticContent} dynamicContent={dynamicContent} />
    </ProblemContext.Provider>
  )
}
