import fs from 'node:fs/promises'
import { resolve } from 'node:path'
import { LoaderFunctionArgs } from '@remix-run/node'
import invariant from 'tiny-invariant'

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { fingerprint, lng, ns } = params
  invariant(fingerprint && lng && ns)

  try {
    invariant(fingerprint === 'development')
    const filepath = resolve(`./locales/${lng}/${ns}.json`)

    await fs.access(filepath)
    const fileContent = await fs.readFile(filepath, 'utf-8')
    return new Response(fileContent, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch {
    throw new Response(null, {
      status: 404,
      statusText: 'Not Found',
    })
  }
}
