import { LoaderFunctionArgs } from '@remix-run/node'
import invariant from 'tiny-invariant'
import {
  loadChatRequestContent,
  getChatRequestStreamingValue,
} from 'src/demo2/database.server/chat'
import getPrismaClient from '@deps/prisma-client'
import { assertWithHttpError } from '@common/utils'
import { EventStream } from '@remix-sse/server'
import logger from '@common/deps.server/logger'
import { ChatStreamingMessage } from 'src/demo2/models/chat'

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const { id: testId, chatId, requestId } = params
  invariant(testId && chatId && requestId, 'Missing required parameters')

  const client = await getPrismaClient()

  // Validate input and load chat request content
  const chatRequestContent = await client.$transaction(async (tx) => {
    const [chatBelongsToTest, chatRequestBelongsToChat] = await Promise.all([
      tx.peaProblemProgress.findFirst({
        where: {
          testId,
          OR: [{ axiiaChatId: chatId }, { customChats: { some: { id: chatId } } }],
        },
      }),
      tx.peaChatRequest.findFirst({
        where: {
          id: requestId,
          chatId: chatId,
        },
      }),
    ])

    assertWithHttpError(chatBelongsToTest, 404, 'Chat not found for this test')
    assertWithHttpError(chatRequestBelongsToChat, 404, 'Chat request not found for this chat')

    return loadChatRequestContent(requestId, tx)
  })

  assertWithHttpError(chatRequestContent, 404, 'Chat request content not found')

  if (chatRequestContent.type !== 'in-progress' || !chatRequestContent.streamingId) {
    throw new Response('Chat request is not in progress or streaming', { status: 400 })
  }

  let isDone = false
  const streamingId = chatRequestContent.streamingId

  async function observe(send: (event: ChatStreamingMessage) => void) {
    invariant(requestId)
    let previousContent = ''

    while (!isDone) {
      try {
        const result = await getChatRequestStreamingValue(requestId, streamingId, true)

        if (!result) {
          send({ type: 'error', message: 'Streaming data not found' })
          return
        }

        if (result.type === 'complete') {
          logger.info('Chat request SSE complete! Now sending the final message.')
          send({ type: 'complete', value: result.value })
          return
        }

        const newContent = result.value

        if (!newContent.startsWith(previousContent)) {
          send({ type: 'error', message: 'Inconsistent streaming data' })
          return
        }

        if (newContent.length > previousContent.length) {
          const patch = newContent.slice(previousContent.length)
          send({ type: 'chunk', patch })
          previousContent = newContent
        }

        await new Promise((resolve) => setTimeout(resolve, 150)) // Poll every 150ms
      } catch (error) {
        logger.error(error, `An error occurred when streaming chat request ${requestId}`)
        return
      }
    }
  }

  return new EventStream(request, (send) => {
    observe((event) => send(JSON.stringify(event)))
    return () => {
      isDone = true
    }
  })
}
