import logger from '@deps/logger'
import { json, LoaderFunctionArgs } from '@remix-run/node'
import { loadProblemStaticContent } from 'src/demo2/database.server/test-static-content'
import invariant from 'tiny-invariant'

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { id, n: nAsString } = params
  invariant(id)
  const n = parseInt(nAsString || 'p')
  invariant(!isNaN(n))
  logger.info(`start loading static content for problem ${id}:${n}`)
  const data = await loadProblemStaticContent(id, n)
  logger.info(`finished loading static content for problem ${id}:${n}`)
  return json(data)
}
