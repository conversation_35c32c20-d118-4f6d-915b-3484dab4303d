import { getPost } from '@main/services/post.server'
import { json, LoaderFunction } from '@remix-run/node'
import { useLoaderData, Link } from '@remix-run/react'
import { StyledMarkdown } from '@main/components/demo/StyledMarkdown'

export const loader: LoaderFunction = async ({ params }) => {
  if (!params.slug) {
    throw new Response('Not Found', { status: 404 })
  }
  const post = await getPost(params.slug)
  if (!post) {
    throw new Response('Not Found', { status: 404 })
  }
  return json({ post })
}

export default function PostSlug() {
  const { post } = useLoaderData<typeof loader>()

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col min-h-screen">
      <Link to="/posts" className="hover:underline py-4 text-sm sm:text-base">
        &larr; Back
      </Link>
      <div className="overflow-y-auto flex-grow mb-24 no-scrollbar ">
        <h1 className="text-3xl sm:text-4xl font-bold mb-2 sm:mb-4">{post.title}</h1>
        <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-8">
          {post.date} {post.author}
        </p>
        <StyledMarkdown content={post.content} />
      </div>
    </div>
  )
}
