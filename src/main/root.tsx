import { useChangeLanguage } from 'remix-i18next/react'
import { useTranslation } from 'react-i18next'
import i18next from './i18next.server'

import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useRouteError,
  useRouteLoaderData,
} from '@remix-run/react'
import { LinksFunction, LoaderFunctionArgs, json } from '@remix-run/node'
import stylesheet from './main.css?url'
import globalStyles from '@axiia-ui/globals.css?url'
import { Toaster } from '@axiia-ui/components/toaster'
import NavbarManager from './components/home/<USER>'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request)
  return json({ locale })
}

export const handle = {
  i18n: 'common',
}

export const links: LinksFunction = () => [
  { rel: 'stylesheet', href: stylesheet },
  { rel: 'stylesheet', href: globalStyles },
]

export function Layout({ children }: { children: React.ReactNode }) {
  const data = useRouteLoaderData<typeof loader>('root')
  const error = useRouteError()

  const locale = data?.locale || 'en'
  const { i18n } = useTranslation()
  useChangeLanguage(locale)

  let child: React.ReactNode
  if (data) {
    child = children
  } else if (isRouteErrorResponse(error)) {
    if (error.status === 404) {
      child = <div>This page does not exist!</div>
    }

    if (error.status === 401) {
      child = <div>You are not authorized to see this</div>
    }
  } else {
    child = <div>Something went wrong</div>
  }

  return (
    <html lang={locale} dir={i18n.dir()}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="overflow-hidden">
        {child}
        <Toaster />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  return (
    <NavbarManager>
      <Outlet />
    </NavbarManager>
  )
}
