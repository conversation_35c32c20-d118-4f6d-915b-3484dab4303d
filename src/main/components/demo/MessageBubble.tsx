import { StyledMarkdown } from './StyledMarkdown'
import Axiia from './assets/AxiiaChatAvatar.svg'
import ToolBar from './Toolbar'
import { Loader2 } from 'lucide-react'

export interface ChatMessage {
  id: string
  isUser: boolean
  content: string
  isGenerating: boolean
}

export const MessageBubble = ({ content, isUser, isGenerating }: ChatMessage) => {
  if (!isUser) {
    return <AssistantMessageBubble content={content} isGenerating={isGenerating} />
  } else {
    return <UserMessageBubble content={content} />
  }
}

const AssistantMessageBubble = ({
  content,
  isGenerating,
}: {
  content: string
  isGenerating: boolean
}) => {
  let text = content
  if (isGenerating) {
    text += '&nbsp;●'
  }

  const avatarStyles = {
    background: 'rgba(255, 255, 255, 0.40)',
    boxShadow: '1px 2px 2px 0px rgba(170, 170, 170, 0.25)',
    backdropFilter: 'blur(2px)',
  }

  return (
    <div className="w-ful flex gap-[1.5em]">
      <div className="bg-5 h-[2em] w-[2em] rounded-full" style={avatarStyles}>
        <img src={Axiia} alt="Axiia"></img>
      </div>
      <div className="flex-1 overflow-auto">
        {content ? (
          <ToolBar text={text}>
            <StyledMarkdown content={text} />
          </ToolBar>
        ) : (
          <Loader2 className="mr-2 h-6 w-6 animate-spin text-primary" />
        )}
      </div>
    </div>
  )
}

const UserMessageBubble = ({ content }: { content: string }) => {
  return (
    <div className="ml-auto mr-0 backdrop-blur-sm flex w-fit max-w-[70%]">
      <ToolBar text={content} position="right">
        <p className="bg-primary/80 rounded-[1em] break-all px-[1em] py-[0.75em] text-white whitespace-pre-line shadow-md">
          {content}
        </p>
      </ToolBar>
    </div>
  )
}
