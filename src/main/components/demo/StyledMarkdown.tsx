import Markdown, { Components } from 'react-markdown'
import styles from './StyledMarkdown.module.css'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { CopyButton } from './Toolbar'
import { prism } from 'react-syntax-highlighter/dist/cjs/styles/prism'
import remarkGfm from 'remark-gfm'

interface StyledMarkdownProps {
  content: string
}

const baseComponents: Components = {
  h1: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h1 className="text-4xl font-bold mt-8 mb-4" {...props}>
      {props.children}
    </h1>
  ),
  h2: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h2 className="text-3xl font-bold mt-8 mb-4" {...props}>
      {props.children}
    </h2>
  ),
  h3: (props: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h3 className="text-2xl font-bold mt-6 mb-3" {...props}>
      {props.children}
    </h3>
  ),
  p: (props: React.HTMLAttributes<HTMLParagraphElement>) => <p className="leading-7" {...props} />,
  a: (props: React.HTMLAttributes<HTMLAnchorElement>) => (
    <a
      className="text-blue-600 hover:text-blue-800 hover:underline"
      target="_blank"
      rel="noopener noreferrer"
      {...props}
    >
      {props.children}
    </a>
  ),
  ul: (props: React.HTMLAttributes<HTMLUListElement>) => (
    <ul className="ml-6 list-disc" {...props} />
  ),
  ol: (props: React.HTMLAttributes<HTMLOListElement>) => (
    <ol className="ml-6 list-decimal" {...props} />
  ),
  li: (props: React.HTMLAttributes<HTMLLIElement>) => (
    <li className="my-1 [&>*]:mt-4 first:[&>*]:mt-0" {...props} />
  ),
  blockquote: (props: React.HTMLAttributes<HTMLQuoteElement>) => {
    const getTextContent = (children: React.ReactNode): string => {
      if (typeof children === 'string') return children
      if (Array.isArray(children)) {
        return children.map((child) => getTextContent(child)).join('')
      }
      if (children && typeof children === 'object' && 'props' in children) {
        return getTextContent(children.props.children)
      }
      return ''
    }

    const content = getTextContent(props.children)
    const isUser = content.includes('User:')
    const isAI = content.includes('AI:')

    return (
      <blockquote
        className={`border-l-4 rounded-lg px-3 py-2 my-2 ${
          isUser
            ? 'border-blue-500 bg-blue-50'
            : isAI
              ? 'border-green-500 bg-green-50'
              : 'border-primary text-gray-700'
        }`}
        {...props}
      />
    )
  },
  pre: (props: React.HTMLAttributes<HTMLPreElement>) => <pre {...props} />,
  table: (props: React.HTMLAttributes<HTMLTableElement>) => (
    <div className="my-4 overflow-x-auto">
      <table className="min-w-full border-collapse border border-gray-200" {...props} />
    </div>
  ),
  thead: (props: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <thead className="bg-gray-50" {...props} />
  ),
  tbody: (props: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody className="bg-white divide-y divide-gray-200" {...props} />
  ),
  tr: (props: React.HTMLAttributes<HTMLTableRowElement>) => (
    <tr className="hover:bg-gray-50" {...props} />
  ),
  th: (props: React.HTMLAttributes<HTMLTableCellElement>) => (
    <th
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border border-gray-200"
      {...props}
    />
  ),
  td: (props: React.HTMLAttributes<HTMLTableCellElement>) => (
    <td
      className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border border-gray-200"
      {...props}
    />
  ),
  code: ({ className, children }: React.HTMLAttributes<HTMLPreElement>) => {
    if (!className) {
      return (
        <code className="px-1.5 py-0.5 mx-0.5 rounded bg-[#f4f2f0] text-primary font-mono text-sm whitespace-pre-wrap break-words">
          {String(children)}
        </code>
      )
    }

    const match = /language-(\w+)/.exec(className || '')
    const language = match ? match[1] : ''

    return (
      <div className="relative border border-gray-200 rounded-md">
        <div className="absolute left-0 right-0 top-0 flex items-center justify-between px-4 py-2 bg-white rounded-t">
          <span className="text-xs text-gray-600">{language}</span>
          <CopyButton text={String(children).trim()} className="w-4 h-4" />
        </div>
        <SyntaxHighlighter
          language={language}
          style={prism}
          lineProps={{ style: { wordBreak: 'break-word', whiteSpace: 'pre-wrap' } }}
          wrapLines={true}
          customStyle={{ paddingTop: '2rem', marginBottom: '0' }}
        >
          {String(children).trim()}
        </SyntaxHighlighter>
      </div>
    )
  },
}

export const StyledMarkdown = ({ content }: StyledMarkdownProps) => {
  return (
    <div className={styles.markdown}>
      <Markdown components={baseComponents} remarkPlugins={[remarkGfm]}>
        {content}
      </Markdown>
    </div>
  )
}
