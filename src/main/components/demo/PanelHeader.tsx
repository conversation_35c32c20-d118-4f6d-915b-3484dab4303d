import { Badge } from '@axiia-ui/components/badge'
import { SimpleTabItem } from 'src/demo2/models/tabs'
import { clsx } from 'clsx'
import { PanelLeftOpen, PanelRightOpen, X } from 'lucide-react'
import { MouseEventHandler } from 'react'

interface PanelHeaderProps {
  tabs: SimpleTabItem[]
  curTabID: string
  isLeft: boolean
  onTabChange: (tabID: string) => void
  onRemove: (id: string) => void
  onSwitch: (position: 'left' | 'right') => void
}

export default function PanelHeader({
  tabs,
  curTabID,
  isLeft,
  onTabChange,
  onRemove,
  onSwitch,
}: PanelHeaderProps) {
  return (
    <div className="h-14 flex  border-b">
      {!isLeft && (
        <SwitchButton
          position="left"
          disable={tabs.length <= 1}
          onClick={() => onSwitch('right')}
        />
      )}
      <div className="overflow-x-auto overflow-y-hidden flex-1 flex items-center space-x-2 px-5 py-3">
        {tabs.map((tag: SimpleTabItem) => (
          <div key={tag.id || 'default'}>
            <Badge
              onClick={() => onTabChange(tag.id)}
              className={clsx(
                'h-7 bg-white text-primary border-primary whitespace-nowrap hover:text-white',
                curTabID === tag.id && 'bg-primary text-white',
              )}
            >
              {tag.name}
              {tabs.length > 1 && (
                <X
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemove(tag.id)
                  }}
                  className={clsx(
                    'h-4 w-4 cursor-pointer hover:text-white',
                    curTabID === tag.id && 'text-white',
                  )}
                />
              )}
            </Badge>
          </div>
        ))}
      </div>
      {isLeft && (
        <SwitchButton
          position="right"
          disable={tabs.length <= 1}
          onClick={() => onSwitch('left')}
        />
      )}
    </div>
  )
}

interface SwitchButtonProps {
  disable: boolean
  position: 'left' | 'right'
  onClick: () => void
}

function SwitchButton({ position, disable, onClick }: SwitchButtonProps) {
  const className = disable ? 'text-gray-400' : 'hover:text-primary'

  const handleClick: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation()
    onClick()
  }

  return (
    <button className="px-2" onClick={handleClick} disabled={disable}>
      {position === 'left' ? (
        <PanelRightOpen className={className} />
      ) : (
        <PanelLeftOpen className={className} />
      )}
    </button>
  )
}
