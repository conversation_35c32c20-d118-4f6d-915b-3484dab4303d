import { Loader2 } from 'lucide-react'
import React from 'react'

type GetStartedButtonProps = {
  title?: string
  isDisabled?: boolean
  isLoading: boolean
  onClick?: () => void
} & React.ButtonHTMLAttributes<HTMLButtonElement>

export const GetStartedButton = (props: GetStartedButtonProps) => {
  const title = props.title || 'Get Started'

  const copy: Record<string, unknown> = { ...props }
  delete copy.isDisabled
  delete copy.isLoading

  return (
    <button
      {...copy}
      onClick={props.onClick}
      className="bg-primary self-start rounded-lg px-4 py-2 text-white shadow-lg"
      disabled={props.isDisabled || props.isLoading}
      style={{ opacity: props.isDisabled || props.isLoading ? 0.4 : 1 }}
    >
      <div className="flex items-center gap-[0.75em]">
        {props.isLoading && <Loader2 className="h-5 w-5 animate-spin text-white" />} <p>{title}</p>
      </div>
    </button>
  )
}
