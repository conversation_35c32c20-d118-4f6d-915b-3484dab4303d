import { cn } from '@axiia-ui/lib/utils'
import { Check, Copy } from 'lucide-react'
import { ReactNode, useEffect, useState } from 'react'

interface ToolBarProps {
  children: ReactNode
  text: string
  position?: 'left' | 'right'
}
export default function ToolBar({ children, text, position = 'left' }: ToolBarProps) {
  const [isHovered, setIsHovered] = useState(false)

  const handleMouseEnter = () => setIsHovered(true)
  const handleMouseLeave = () => setIsHovered(false)

  return (
    <div className="relative group" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <div className="pb-6">{children}</div>
      <div
        className={cn(
          'absolute bottom-0 flex space-x-2 transition-opacity',
          isHovered ? 'opacity-100' : 'opacity-0',
          position === 'left' ? 'left-0' : 'right-0',
        )}
      >
        <CopyButton text={text} />
      </div>
    </div>
  )
}

interface CopyButtonProps {
  text: string
  className?: string
}

export function CopyButton({ text, className }: CopyButtonProps) {
  const [isCopy, setIsCopy] = useState(false)

  useEffect(() => {
    let timeout: NodeJS.Timeout

    if (isCopy) {
      timeout = setTimeout(() => setIsCopy(false), 2000)
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
    }
  }, [isCopy])

  const handleCopy = async () => {
    await navigator.clipboard.writeText(text)
    setIsCopy(true)
  }

  return (
    <button onClick={handleCopy} className="text-gray-400 hover:text-gray-800 cursor-pointer">
      {isCopy ? (
        <Check className={cn('w-4 h-4', className)} />
      ) : (
        <Copy className={cn('w-4 h-4', className)} />
      )}
    </button>
  )
}
