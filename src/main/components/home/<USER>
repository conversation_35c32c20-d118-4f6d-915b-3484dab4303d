import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@axiia-ui/components/dropdown-menu'
import { Link } from '@remix-run/react'
import { ChevronDown, Menu } from 'lucide-react'
import { useState } from 'react'
import Axiia from './assets/axiia-ai.svg'

type MenuItem = {
  to: string
  label: string
}

const MenuLink = ({ to, label, onClick }: MenuItem & { onClick: () => void }) => (
  <li>
    <Link to={to} className="block px-4 py-2 text-gray-600 hover:bg-gray-100" onClick={onClick}>
      {label}
    </Link>
  </li>
)

export const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const menuItems: MenuItem[] = [
    { to: '/', label: 'Home' },
    { to: '/', label: 'Services' },
    { to: '/', label: 'Contact Us' },
    { to: '/posts', label: 'Blog Posts' },
    { to: '/', label: 'FAQ Section' },
  ]

  return (
    <header className="fixed top-0 left-0 right-0 bg-white z-10 shadow-md">
      <div className="container mx-auto flex justify-between items-center p-4">
        <img src={Axiia} alt="Axiia Logo" className="w-20 md:w-24" />
        <nav className="hidden md:block">
          <ul className="flex space-x-4 items-center">
            <li>
              <Link to="/" className="text-gray-600 hover:text-primary">
                Home
              </Link>
            </li>
            <li>
              <Link to="/posts" className="text-gray-600 hover:text-primary">
                Blog Posts
              </Link>
            </li>
            <li>
              <DropdownMenu>
                <DropdownMenuTrigger className="text-gray-600 hover:text-primary flex items-center">
                  Get Started <ChevronDown className="ml-1" size={16} />
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>
                    <Link to="/" className="block w-full">
                      Contact Us
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Link to="/" className="block w-full">
                      Services
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Link to="/" className="block w-full">
                      FAQ Section
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </li>
          </ul>
        </nav>
        <button className="md:hidden" onClick={toggleMenu}>
          <Menu />
        </button>
      </div>
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-md">
          <ul className="py-2">
            {menuItems.map((item, index) => (
              <MenuLink key={index} {...item} onClick={() => setIsMenuOpen(false)} />
            ))}
          </ul>
        </div>
      )}
    </header>
  )
}
