export const BenefitsSection = ({
  benefitsSectionRef,
}: {
  benefitsSectionRef: React.RefObject<HTMLElement>
}) => (
  <section
    ref={benefitsSectionRef}
    className="flex flex-col justify-center min-h-[calc(100vh-64px)] md:max-h-[700px] lg:max-h-[800px] px-4 md:px-8 lg:px-16 pt-16 md:pt-24 lg:pt-48 pb-16 md:pb-24 lg:pb-36"
  >
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-4">
        Unlock Your AI Potential with <span className="text-primary">Axiia</span>
      </h2>
      <p className="text-center mb-8 md:mb-12">
        Axiia Technologies offers a cutting-edge AI assessment tool designed to evaluate your AI
        competency accurately. Experience personalized feedback that helps you grow and excel in the
        AI landscape.
      </p>
    </div>
    <div className="flex flex-col md:flex-row justify-between space-y-8 md:space-y-0 md:space-x-8">
      {[
        'Accurate AI Competency Evaluation',
        'Personalized Feedback for Continuous Improvement',
        'Advanced Analytics for In-Depth Insights',
      ].map((title, index) => (
        <div key={index} className="flex-1">
          <div className="bg-gray-200 h-48 md:h-64 mb-4"></div>
          <h3 className="text-xl md:text-2xl lg:text-3xl text-center font-bold mb-2">{title}</h3>
          <p className="text-center">
            Our tool provides precise evaluation to measure your AI skills.
          </p>
        </div>
      ))}
    </div>
  </section>
)
