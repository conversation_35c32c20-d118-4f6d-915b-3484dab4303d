import { Button } from '@axiia-ui/components/button'
import { useEffect, useRef, useState } from 'react'

export const Hero = ({ onLearnMoreClick }: { onLearnMoreClick: () => void }) => {
  const [isVisible, setIsVisible] = useState(true)
  const heroRef = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1,
      },
    )

    if (heroRef.current) {
      observer.observe(heroRef.current)
    }

    return () => {
      if (heroRef.current) {
        observer.unobserve(heroRef.current)
      }
    }
  }, [])

  return (
    <section
      ref={heroRef}
      className={`
        bg-gray-700 text-white
        flex items-center
        min-h-[calc(100vh-64px)] 
        max-h-[600px]
        md:max-h-[700px]
        lg:max-h-[800px]
        transition-opacity duration-500
        ${isVisible ? 'opacity-100' : 'opacity-0'}
        px-4 md:px-8 lg:px-16
      `}
    >
      <div className="container mx-auto py-8 md:py-16">
        <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 md:mb-6 leading-tight">
          Unlock Your AI
          <br />
          Potential with <span className="text-primary">Axiia</span>
        </h1>
        <p className="text-base md:text-lg mb-6 md:mb-8 max-w-2xl">
          Discover Axiia Technologies, the innovative AI assessment tool designed to evaluate your
          proficiency in utilizing artificial intelligence. Register now for exclusive access to our
          groundbreaking platform and be among the first to experience its capabilities.
        </p>
        <div className="space-x-4">
          <Button className="bg-white text-gray-800 hover:bg-gray-200" onClick={onLearnMoreClick}>
            Learn More
          </Button>
        </div>
      </div>
    </section>
  )
}
