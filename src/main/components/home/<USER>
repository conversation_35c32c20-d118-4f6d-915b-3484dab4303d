import { Link } from '@remix-run/react'
import { Facebook, Instagram, Linkedin, Twitter, Youtube } from 'lucide-react'
import Axiia from './assets/axiia-ai.svg'

export const Footer = () => (
  <footer className="bg-gray-200 p-4 md:p-8 lg:p-16">
    <div className="container mx-auto">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8">
        <img src={Axiia} alt="Axiia Logo" className="w-20 md:w-24 mb-4 md:mb-0" />
        <nav className="mb-4 md:mb-0">
          <ul className="flex flex-wrap justify-center md:justify-start space-x-4 md:space-x-6">
            <li>
              <Link to="/" className="text-gray-600 hover:text-gray-900">
                About Us
              </Link>
            </li>
            <li>
              <Link to="/" className="text-gray-600 hover:text-gray-900">
                Contact Us
              </Link>
            </li>
            <li>
              <Link to="/" className="text-gray-600 hover:text-gray-900">
                FAQs
              </Link>
            </li>
            <li>
              <Link to="/" className="text-gray-600 hover:text-gray-900">
                Support
              </Link>
            </li>
            <li>
              <Link to="/posts" className="text-gray-600 hover:text-gray-900">
                Blog
              </Link>
            </li>
          </ul>
        </nav>
        <div className="flex space-x-4">
          <Link to="/" className="text-gray-600 hover:text-gray-900">
            <Facebook size={24} />
          </Link>
          <Link to="/" className="text-gray-600 hover:text-gray-900">
            <Instagram size={24} />
          </Link>
          <Link to="/" className="text-gray-600 hover:text-gray-900">
            <Twitter size={24} />
          </Link>
          <Link to="/" className="text-gray-600 hover:text-gray-900">
            <Linkedin size={24} />
          </Link>
          <Link to="/" className="text-gray-600 hover:text-gray-900">
            <Youtube size={24} />
          </Link>
        </div>
      </div>
      <hr className="border-gray-200 mb-8" />
      <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
        <p className="mb-4 md:mb-0">&copy; 2024 Axiia Technologies. All rights reserved.</p>
        <div className="flex flex-wrap justify-center space-x-4 md:space-x-6">
          <Link to="/" className="hover:text-gray-900">
            Privacy Policy
          </Link>
          <Link to="/" className="hover:text-gray-900">
            Terms of Service
          </Link>
          <Link to="/" className="hover:text-gray-900">
            Cookie Settings
          </Link>
        </div>
      </div>
    </div>
  </footer>
)
