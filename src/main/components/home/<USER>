import { useState, useEffect } from 'react'
import { useLocation } from '@remix-run/react'
import { Header } from './Header'

interface NavbarManagerProps {
  children: React.ReactNode
}

export default function NavbarManager({ children }: NavbarManagerProps) {
  const location = useLocation()
  const [showNavbar, setShowNavbar] = useState(false)

  useEffect(() => {
    const shouldShowNavbar = location.pathname === '/' || location.pathname.startsWith('/posts')
    setShowNavbar(shouldShowNavbar)
  }, [location])

  return showNavbar ? (
    <div className="h-screen flex flex-col">
      <Header />
      {children}
    </div>
  ) : (
    children
  )
}
