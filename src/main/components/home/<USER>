import { Avatar, AvatarFallback, AvatarImage } from '@axiia-ui/components/avatar'

const feedbacks = [
  {
    name: '<PERSON>',
    background: 'Volunteer of the Honghu Charity Program, China Youth of Tomorrow ',
    imgPath: 'user-1.jpg',
    feedback:
      'This is my first time taking an AI capability assessment, and it feels really novel. Although I have been using ChatGPT regularly, my approach to using AI has been quite elementary. The methods this AI assessment introduces for using AI are truly distinct, beyond my previous understanding. Mastering the use of AI tools will likely be a future trend, and I am really eager to continue learning!',
  },
  {
    name: '<PERSON>',
    background: 'Tutor of the Honghu Charity Program, China Youth of Tomorrow',
    imgPath: 'user-2.jpg',
    feedback:
      'In the future, working with AI will just be a part of everyday life and professional endeavors. It might even be that knowing how to ask the right questions will matter more than knowing how to answer them. Being able to ask good questions will turn into a real skill, maybe even an art. As AI becomes more common and sophisticated, we all need to sharpen our logical thinking to ask better and more insightful questions.',
  },
  {
    name: '<PERSON>',
    background: 'Leader of the Building Bridges volunteer teaching project',
    imgPath: 'user-3.png',
    feedback:
      "I personally feel that the AI's questions during the interview were pretty helpful. They built on each other, digging deeper into certain points from my previous answers, which made the process feel similar to being interviewed by a real person.This test made me realize that I’m not as strong in that area as I thought, but it’s also pushed me to keep improving my logical skills for working with AI.",
  },
]

export const TestimonialsSection = () => (
  <section className="p-4 md:p-8 lg:p-16">
    <div className="max-w-4xl mx-auto mb-8 md:mb-12">
      <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Customer testimonials</h2>
    </div>
    <div className="flex flex-col md:flex-row justify-between space-y-8 md:space-y-0 md:space-x-8">
      {feedbacks.map((feedback, index) => (
        <div key={index} className="flex-1 p-4 md:p-6 rounded-lg flex flex-col items-center">
          <p className="mb-4 font-semibold text-center">&ldquo;{feedback.feedback}&rdquo;</p>
          <div className="flex flex-col items-center">
            <Avatar>
              <AvatarImage src={`/img/users/${feedback.imgPath}`} />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
            <p className="font-bold">{feedback.name}</p>
            <p className="text-gray-700 text-sm text-center">{feedback.background}</p>
          </div>
        </div>
      ))}
    </div>
  </section>
)
