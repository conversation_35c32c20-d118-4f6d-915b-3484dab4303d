import fs from 'fs/promises'
import path from 'path'
import matter from 'gray-matter'

const postsDirectory =
  process.env.NODE_ENV === 'production'
    ? path.join(process.cwd(), 'build', 'main', 'client', 'posts')
    : path.join(process.cwd(), 'public', 'posts')

console.log('Posts directory:', postsDirectory)

export async function getPosts() {
  try {
    const filenames = await fs.readdir(postsDirectory)

    const posts = await Promise.all(
      filenames.map(async (filename) => {
        const filePath = path.join(postsDirectory, filename)
        try {
          const content = await fs.readFile(filePath, 'utf8')
          const { data } = matter(content)

          return {
            slug: filename.replace(/\.md$/, ''),
            title: data.title,
            date: data.date,
            public: data.public,
            author: data.author,
          }
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error)
          return null
        }
      }),
    )

    return posts
      .filter((post) => post && post.public)
      .sort((a, b) =>
        posts.length > 1 && b && a ? new Date(b.date).getTime() - new Date(a.date).getTime() : 0,
      )
  } catch (error) {
    console.error(`Error reading posts directory ${postsDirectory}:${process.cwd()}`, error)
    return []
  }
}

export async function getPost(slug: string) {
  const fullPath = path.join(postsDirectory, `${slug}.md`)

  try {
    const fileContents = await fs.readFile(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    return {
      slug,
      title: data.title,
      date: data.date,
      author: data.author,
      content,
    }
  } catch (error) {
    console.error(`Error reading file ${fullPath}:`, error)
    return null
  }
}
