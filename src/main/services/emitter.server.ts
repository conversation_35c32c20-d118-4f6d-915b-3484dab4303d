/* eslint-disable @typescript-eslint/no-explicit-any */
import Redis, { RedisOptions } from 'ioredis'
import { EventEmitter } from 'node:events'

class DistributedEventEmitter extends EventEmitter {
  private redis: Redis
  private sub: Redis

  constructor(redisOptions: RedisOptions) {
    super()
    this.redis = new Redis(redisOptions)
    this.sub = new Redis(redisOptions)

    this.sub.on('message', (channel, message) => {
      super.emit(channel, ...JSON.parse(message))
    })
  }

  emit(event: string, ...args: any[]): boolean {
    this.redis.publish(event, JSON.stringify(args))
    return super.emit(event, ...args)
  }

  on(event: string, listener: (...args: any[]) => void): this {
    this.sub.subscribe(event)
    return super.on(event, listener)
  }

  off(event: string, listener: (...args: any[]) => void): this {
    this.sub.unsubscribe(event)
    return super.off(event, listener)
  }
}

export default DistributedEventEmitter

export const emitter = new DistributedEventEmitter({
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
})
