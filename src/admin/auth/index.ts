import { <PERSON><PERSON>, <PERSON>ieSerializeOptions, redirect } from '@remix-run/node'
import { $path } from 'remix-routes'
import { z } from 'zod'
import getCookieManager from '@deps/cookie-manager'

export const authValueSchema = z.object({
  email: z.string().email(),
  nickname: z.string(),
})

export type AuthValue = z.infer<typeof authValueSchema>

async function getAuthCookie(): Promise<Cookie> {
  const manager = await getCookieManager()
  return await manager.createCookie('admin-auth', {
    maxAge: 3600 * 24 * 7,
  })
}

export async function getAuthValue(request: Request): Promise<AuthValue | undefined> {
  const cookie = await getAuthCookie()
  const cookieValue = await cookie.parse(request.headers.get('Cookie'))
  const parseResult = authValueSchema.safeParse(cookieValue)
  if (parseResult.success) {
    return parseResult.data
  }
}

export async function requireLogin(request: Request): Promise<AuthValue> {
  const authValue = await getAuthValue(request)
  if (!authValue) {
    throw redirect($path('/login'))
  }
  return authValue
}

export async function serializeAuthCookie(
  value: AuthValue,
  options?: CookieSerializeOptions,
): Promise<string> {
  const cookie = await getAuthCookie()
  const string = await cookie.serialize(value, options)
  return string
}

export async function logoutAuthCookie(): Promise<string> {
  const cookie = await getAuthCookie()
  const string = await cookie.serialize('', { maxAge: 1 })
  return string
}
