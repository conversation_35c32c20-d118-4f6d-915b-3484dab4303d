import { PanelInfo } from './panels.model'

type Panel = 'demo-1' | 'management' | 'settings' | 'demo3'

const allPanels: Panel[] = ['demo-1', 'demo3', 'management', 'settings']

function checkHandleAccess(handle: string, panel: Panel): boolean {
  if (panel === 'settings') {
    return true
  }

  if (handle === 'minsheng' || handle === 'anna') {
    return true
  }

  switch (panel) {
    case 'demo-1': {
      const whitelist = ['yihan', 'siyue', 'jessie']
      return whitelist.includes(handle)
    }

    case 'management': {
      const whitelist = ['yihan']
      return whitelist.includes(handle)
    }

    case 'demo3': {
      const whitelist = ['siyue', 'yihan']
      return whitelist.includes(handle)
    }

    default:
      return false
  }
}

function checkEmailAccess(email: string, panel: Panel): boolean {
  const handle = email.split('@')[0]
  return checkHandleAccess(handle, panel)
}

function titleForPanel(panel: Panel): string {
  switch (panel) {
    case 'demo-1':
      return 'Pea'
    case 'demo3':
      return 'Cognitive distortion'
    case 'management':
      return 'Management'
    case 'settings':
      return 'Settings'
  }
}

function pathForPanel(panel: Panel): string {
  switch (panel) {
    case 'demo-1':
      return '/demo1'
    case 'demo3':
      return '/demo3/chat'
    default:
      return `/${panel}`
  }
}

function iconNameForPanel(panel: Panel): string {
  switch (panel) {
    case 'demo-1':
      return 'BookCopy'
    case 'management':
      return 'Users'
    case 'settings':
      return 'Settings'
    case 'demo3':
      return 'BotMessageSquare'
  }
}

export function getAvailablePanels(email: string): PanelInfo[] {
  const panels = allPanels.filter((panel) => checkEmailAccess(email, panel))
  return panels.map(
    (panel) =>
      ({
        panel,
        title: titleForPanel(panel),
        path: pathForPanel(panel),
        iconName: iconNameForPanel(panel),
      }) satisfies PanelInfo,
  )
}
