import { BookCopy, Users, Settings, Circle, BotMessageSquare } from 'lucide-react'

export interface PanelInfo {
  panel: string
  title: string
  path: string
  iconName: string
}

export function resolveIcon(iconName: string): React.ElementType {
  switch (iconName) {
    case 'BookCopy':
      return BookCopy
    case 'Users':
      return Users
    case 'Settings':
      return Settings
    case 'BotMessageSquare':
      return BotMessageSquare
    default:
      return Circle
  }
}
