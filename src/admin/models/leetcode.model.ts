export interface VersionedLeetcodeSample {
  problemID: number
  sampleID: number
  version: number
  input: string
  output: string
}

export type LeetcodeSampleTag = 'train' | 'evaluation' | 'test'

export type VersionedLeetcodeProblem = {
  problemID: number
  problemDetail: string
  problemTitle: string
  version: number
  train: VersionedLeetcodeSample[]
  evaluation: VersionedLeetcodeSample[]
  test: VersionedLeetcodeSample[]
}
