import { Leetcode<PERSON>roblem, Prisma } from '@prisma/client'
import getPrismaClient from '@deps/prisma-client'
import {
  VersionedLeetcodeSample,
  LeetcodeSampleTag,
  VersionedLeetcodeProblem,
} from './leetcode.model'

export async function createProblem(
  problemTitle: string,
  problemDetail: string,
): Promise<LeetcodeProblem> {
  const client = await getPrismaClient()
  const problem = await client.leetcodeProblem.create({
    data: {
      problemTitle,
      problemDetail,
    },
  })
  return problem
}

export async function updateProblem(id: number, problemDetail: string) {
  const client = await getPrismaClient()
  await client.leetcodeProblem.update({
    where: {
      id,
    },
    data: {
      problemDetail,
    },
  })
}

export async function createSample(
  problemID: number,
  tag: LeetcodeSampleTag,
  input: string,
  output: string,
): Promise<VersionedLeetcodeSample> {
  const client = await getPrismaClient()
  return await client.$transaction(async (tx) => {
    const maxVersion = await getProblemMaxSampleVersion(tx, problemID)

    const sample = await tx.leetcodeSample.create({
      data: {
        problemID,
        tag,
      },
    })
    const version = maxVersion + 1
    const result: VersionedLeetcodeSample = {
      problemID,
      sampleID: sample.id,
      version,
      input,
      output,
    }
    await tx.leetcodeSampleVersion.create({ data: result })
    return result
  })
}

export async function updateSample(
  problemID: number,
  sampleID: number,
  input: string,
  output: string,
): Promise<VersionedLeetcodeSample> {
  const client = await getPrismaClient()
  return await client.$transaction(async (tx) => {
    const maxVersion = await getProblemMaxSampleVersion(tx, problemID)
    const version = maxVersion + 1
    const result: VersionedLeetcodeSample = {
      problemID,
      sampleID,
      version,
      input,
      output,
    }
    // It will throw an error if the sample is not defined.
    await tx.leetcodeSampleVersion.create({ data: result })
    return result
  })
}

export async function deleteSample(problemID: number, sampleID: number) {
  const client = await getPrismaClient()
  await client.$transaction(async (tx) => {
    const maxVersion = await getProblemMaxSampleVersion(tx, problemID)
    // It will throw an error if the sample is not defined.
    await tx.leetcodeSample.update({
      where: {
        id: sampleID,
      },
      data: {
        deletedAt: maxVersion + 1,
      },
    })
  })
}

export async function getVersionedProblem(
  problemID: number,
  version: number = 1000_000_000,
): Promise<VersionedLeetcodeProblem> {
  const client = await getPrismaClient()

  const [problem, results] = await client.$transaction([
    client.leetcodeProblem.findUniqueOrThrow({
      where: {
        id: problemID,
      },
    }),
    client.leetcodeSampleVersion.findMany({
      where: {
        problemID,
        version: {
          lte: version,
        },
      },
      include: {
        sample: true,
      },
      distinct: ['sampleID'],
      orderBy: {
        version: 'desc',
      },
    }),
  ])

  const aggregated: VersionedLeetcodeProblem = {
    problemID: problem.id,
    problemTitle: problem.problemTitle,
    problemDetail: problem.problemDetail,
    version: 0,
    train: [],
    evaluation: [],
    test: [],
  }
  for (const result of results) {
    const { problemID, version, input, output } = result
    // This deletion happens in the future.
    if (result.sample.deletedAt && result.sample.deletedAt > version) {
      continue
    }
    aggregated.version = Math.max(aggregated.version, result.version, result.sample.deletedAt || 0)
    // It happened so we won't include it. But it does affect aggregated.version.
    if (result.sample.deletedAt) {
      continue
    }

    aggregated[result.sample.tag].push({
      problemID,
      sampleID: result.sample.id,
      version,
      input,
      output,
    })
  }
  return aggregated
}

export async function getAllProblems(): Promise<LeetcodeProblem[]> {
  const client = await getPrismaClient()
  return await client.leetcodeProblem.findMany()
}

export async function getProblemMaxSampleVersion(
  tx: Prisma.TransactionClient,
  problemID: number,
): Promise<number> {
  const maxVersion = await tx.leetcodeSampleVersion.aggregate({
    _max: {
      version: true,
    },
    where: {
      problemID: problemID,
    },
  })
  const maxDeletedAtVersion = await tx.leetcodeSample.aggregate({
    _max: {
      deletedAt: true,
    },
    where: {
      problemID: problemID,
    },
  })
  return Math.max(
    (maxVersion._max?.version || 0) as unknown as number,
    (maxDeletedAtVersion._max?.deletedAt || 0) as unknown as number,
  )
}
