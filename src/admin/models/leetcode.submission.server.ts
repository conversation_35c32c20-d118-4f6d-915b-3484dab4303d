import OpenAI from 'openai'
import getPrismaClient from '@deps/prisma-client'
import { getProblemMaxSampleVersion } from './leetcode.server'
import {
  LeetcodeInterview,
  LeetcodeSampleVersion,
  LeetcodeSubmission,
  LeetcodeSubmissionSampleResult,
} from '@prisma/client'
import { LeetcodeSampleTag } from './leetcode.model'
import logger from '@deps/logger'

export async function createInterview(
  problemID: number,
  randomID: string,
  recipientName: string,
  recipientEmail: string,
): Promise<LeetcodeInterview> {
  const client = await getPrismaClient()

  return await client.$transaction(async (tx) => {
    const version = await getProblemMaxSampleVersion(tx, problemID)

    return await tx.leetcodeInterview.create({
      data: {
        problemID,
        version,
        randomID,
        recipientName,
        recipientEmail,
      },
    })
  })
}

export async function getInterview(randomID: string): Promise<LeetcodeInterview> {
  const client = await getPrismaClient()

  return await client.leetcodeInterview.findFirstOrThrow({
    where: {
      randomID,
    },
  })
}

export async function createSubmission(
  interviewID: number,
  prompt: string,
  targetSampleVersions: number[],
): Promise<LeetcodeSubmission & { sampleResults: LeetcodeSubmissionSampleResult[] }> {
  const client = await getPrismaClient()

  const submission = await client.leetcodeSubmission.create({
    data: {
      interviewID,
      prompt,
      targetSampleVersions,
    },
  })
  const { problemID } = await client.leetcodeInterview.findFirstOrThrow({
    where: {
      id: interviewID,
    },
  })

  const sampleResults = await runSubmission(submission.id, problemID, prompt, targetSampleVersions)
  return Object.assign({}, submission, { sampleResults })
}

async function runSubmission(
  id: number,
  problemID: number,
  prompt: string,
  sampleVersions: number[],
): Promise<LeetcodeSubmissionSampleResult[]> {
  const client = await getPrismaClient()
  const samples = await client.leetcodeSampleVersion.findMany({
    where: {
      problemID,
      version: {
        in: sampleVersions,
      },
    },
    include: {
      sample: true,
    },
  })

  const allPromises = samples.map(async (sample) => {
    return await evalPromptOnSample(id, prompt, sample, sample.sample.tag)
  })
  try {
    return await Promise.all(allPromises)
  } catch (error) {
    logger.error(error, `An error occurred in submission ${id}.`)
    throw error
  }
}

export async function runChatGPTWithStringOutput(input: string): Promise<string> {
  const openai = new OpenAI()
  const rawOutput = await openai.chat.completions.create({
    messages: [
      {
        role: 'user',
        content: input,
      },
    ],
    model: 'gpt-4o',
  })
  const output = rawOutput.choices[0].message.content
  if (!output) {
    throw new Error(`String output from GPT-4o is missing!`)
  }
  return output
}

async function evalPromptOnSample(
  submissionID: number,
  prompt: string,
  sample: LeetcodeSampleVersion,
  tag: LeetcodeSampleTag,
): Promise<LeetcodeSubmissionSampleResult> {
  const input = prompt.replace(/{question}/g, sample.input)
  const output = await runChatGPTWithStringOutput(input)

  const evalInput = `
Below is a question, its reference answer, and an actual response for you to evaluate. Output "true" if the actual response matches the reference answer and "false" otherwise. Do not include quotes and use only lowercase letters in your output.

Question:

${sample.input}

Reference Answer:

${sample.output}

Actual Answer:

${output}
`
  const evalOutput = await runChatGPTWithStringOutput(evalInput)
  const isCorrect = JSON.parse(evalOutput)
  if (typeof isCorrect !== 'boolean') {
    throw new Error(`Invalid ChatGPT response. Expect true/false but get: ${evalOutput}`)
  }

  const client = await getPrismaClient()
  return await client.leetcodeSubmissionSampleResult.create({
    data: {
      submissionID,
      sampleVersion: sample.version,
      sampleTag: tag,
      input,
      output,
      isCorrect,
    },
  })
}
