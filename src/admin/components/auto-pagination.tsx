import { useSearchParams } from '@remix-run/react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@axiia-ui/components/pagination'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@axiia-ui/components/dropdown-menu'
import { Button } from '@axiia-ui/components/button'
import { Separator } from '@axiia-ui/components/separator'

function getPaginationPages(
  currentPage: number,
  totalPages: number,
  maxPagesToShow: number = 5,
): (number | 'L' | 'R')[] {
  if (totalPages <= maxPagesToShow) {
    return [...Array(totalPages).keys()].map((i) => i + 1)
  }

  const pages: (number | 'L' | 'R')[] = []
  pages.push(1)

  if (currentPage - 1 > 1 + 1) {
    pages.push('L')
  }

  if (currentPage - 1 > 1) {
    pages.push(currentPage - 1)
  }
  if (currentPage > 1 && currentPage < totalPages) {
    pages.push(currentPage)
  }
  if (currentPage + 1 < totalPages) {
    pages.push(currentPage + 1)
  }

  if (currentPage + 1 < totalPages - 1) {
    pages.push('R')
  }

  pages.push(totalPages)

  return pages
}

interface AutoPaginationProps {
  count: number
  itemPerPage: number
}

export function AutoPagination({ count, itemPerPage }: AutoPaginationProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const top = Number(searchParams.get('top')) || itemPerPage
  const skip = Number(searchParams.get('skip')) || 0

  const totalPages = Math.max(1, Math.ceil(count / top))
  const currentPage = Math.floor(skip / top) + 1
  const maxPagesToShow = 5
  const pages = getPaginationPages(currentPage, totalPages, maxPagesToShow)

  function setTop(top: number) {
    return () => {
      const newSkip = Math.floor(skip / top) * top
      setSearchParams((prev) => {
        prev.set('top', String(top))
        prev.set('skip', String(newSkip))
        return prev
      })
    }
  }

  function linkToPage(n: number): string {
    const search = new URLSearchParams(searchParams)
    search.set('top', String(top))
    search.set('skip', String((n - 1) * top))
    return '?' + String(search)
  }

  return (
    <div className="flex w-full items-center justify-between">
      <div className="text-muted-foreground flex h-4 items-center gap-8 text-xs">
        <div>
          Showing <strong>{`${skip + 1}–${Math.min(count, skip + top)}`}</strong> of{' '}
          <strong>{`${count}`}</strong> items
        </div>
        <Separator orientation="vertical" />
        <div className="flex items-center gap-4">
          <div>Items per page</div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1 text-xs">
                <span>{top}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {[10, 20, 50].map((n) => (
                <DropdownMenuCheckboxItem key={n} checked={top === n} onCheckedChange={setTop(n)}>
                  {n}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="flex">
        <Pagination>
          <PaginationContent>
            {currentPage > 1 && (
              <PaginationItem>
                <PaginationPrevious href={linkToPage(currentPage - 1)} />
              </PaginationItem>
            )}

            {pages.map((page) => (
              <PaginationItem key={page}>
                {typeof page === 'string' ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink href={linkToPage(page)} isActive={page === currentPage}>
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            {currentPage < totalPages && (
              <PaginationItem>
                <PaginationNext href={linkToPage(currentPage + 1)} />
              </PaginationItem>
            )}
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}
