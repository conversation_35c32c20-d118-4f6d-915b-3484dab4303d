import { <PERSON>s, Meta, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ScrollRestoration } from '@remix-run/react'
import { LinksFunction } from '@remix-run/node'
import stylesheet from './main.css?url'
import globalStyles from '@axiia-ui/globals.css?url'

export const links: LinksFunction = () => [
  { rel: 'stylesheet', href: stylesheet },
  { rel: 'stylesheet', href: globalStyles },
]

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  return <Outlet />
}
