import { AnyObj } from '@common/utils'

export const dateFormat = (input: string | Date | number, format = 'yyyy-MM-dd hh:mm:ss') => {
  if (typeof input === 'string') {
    if (/^\d+$/.test(input)) {
      input = parseInt(input, 10)
    }
  }
  const date = new Date(input)
  if (!date || date.toUTCString() === 'Invalid Date') {
    console.error(`dateFormat first param is invalid`)
    return ''
  }
  const map: AnyObj<number> = {
    M: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    m: date.getMinutes(),
    s: date.getSeconds(),
    q: Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  }
  format = format.replace(/([yMdhmsqS])+/g, function (all, t) {
    let v: number | undefined | string = map[t] as number | undefined
    if (v !== undefined) {
      if (all.length > 1) {
        v = '0' + v
        v = v.substr(v.length - 2)
      }
      return v as string
    } else if (t === 'y') {
      return ((date as Date).getFullYear() + '').substr(4 - all.length)
    }
    return all
  })
  return format
}
