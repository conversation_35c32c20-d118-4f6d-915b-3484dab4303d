import logger from '@common/deps.server/logger'
import { assertWithHttpError } from '@common/utils'
import getPrismaClient from '@deps/prisma-client'
import type { ActionFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import {
  createTestFromDynamicProblems,
  createTestFromProblemSet,
} from 'src/demo2/database.server/test'
import invariant from 'tiny-invariant'
import { z } from 'zod'

const createTestInputSchema = z.object({
  problemIds: z.array(z.string()).optional(),
  problemSetId: z.string().optional(),
})

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json()
    const input = createTestInputSchema.parse(body)

    const client = await getPrismaClient()
    let testId: string | null = null
    if (input.problemIds) {
      testId = await createTestFromDynamicProblems(input.problemIds, client)
    } else if (input.problemSetId) {
      testId = await createTestFromProblemSet(input.problemSetId, client)
    }
    assertWithHttpError(testId, 400)
    const mainWebsiteURL = process.env.MAIN_WEBSITE_URL
    invariant(mainWebsiteURL)
    const url = `${mainWebsiteURL}/demo2/tests/${testId}`

    return json({ testId, url }, { status: 200 })
  } catch (error) {
    logger.error(error, 'Error processing request:')
    if (error instanceof z.ZodError) {
      return json({ message: 'Invalid input data', errors: error.flatten() }, { status: 400 })
    }
    return json({ message: 'Error processing request' }, { status: 500 })
  }
}
