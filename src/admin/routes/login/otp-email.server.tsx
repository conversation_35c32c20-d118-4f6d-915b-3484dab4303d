import { renderToString } from 'react-dom/server'
import getSendEmailClient from '@deps/send-email-client'

function createEmailHTML(code: string) {
  return renderToString(
    <p>
      Hi
      <br />
      <p>We have received your request for a one-time code to be used on your Paideia account.</p>
      <br />
      <p>
        Your one-time code is: <span style={{ fontWeight: 'bold', fontSize: '16px' }}>{code}</span>.
      </p>
      <br />
      <p>Thank you!</p>
    </p>,
  )
}

export async function sendEmail(email: string, code: string) {
  try {
    const client = await getSendEmailClient()
    await client.sendEmail(
      '<EMAIL>',
      email,
      '[Axiia] Verify your email',
      createEmailHTML(code),
    )
  } catch {
    throw new Error('Server error. Please try again later.')
  }
}
