import { json, redirect, TypedResponse } from '@remix-run/node'
import { Action, ActionError, adminLoginOTPFamily } from './model'
import getOTPManager from '@deps/otp-manager'
import { sendEmail } from './otp-email.server'
import logger from '@deps/logger'
import { $path } from 'remix-routes'
import { serializeAuthCookie } from '@admin/auth'
import { getLoginAttemptCookie } from './cookie.server'
import getPrismaClient from '@deps/prisma-client'

export async function handleAction(
  action: Action,
): Promise<TypedResponse<{ error?: ActionError }>> {
  const cookie = await getLoginAttemptCookie()
  const headers = new Headers()

  if (action.action === 'change-email') {
    headers.append('Set-Cookie', await cookie.serialize('', { maxAge: 1 }))
    return json({}, { headers })
  }

  const otpManager = await getOTPManager(adminLoginOTPFamily)
  const client = await getPrismaClient()

  if (action.action === 'send-code') {
    const { email } = action

    if (!email.endsWith('@paideia.uno')) {
      return json({ error: 'wrong-email' })
    }

    const handle = email.split('@')[0]
    const row = await client.employee.findFirst({ where: { handle } })
    if (!row) {
      return json({ error: 'wrong-email' })
    }

    const [token, code] = await otpManager.generate(email)
    await sendEmail(email, code)
    logger.info(`Sent code ${code} to ${email}`)

    headers.append('Set-Cookie', await cookie.serialize({ email, token }))
    return json({}, { headers })
  }

  if (action.action === 'verify-code') {
    const { email, code, token } = action
    const subject = await otpManager.verify(token, code)

    if (subject !== email) {
      const error: ActionError = 'wrong-code'

      return json({ error })
    }

    const handle = email.split('@')[0]
    const row = await client.employee.findFirst({ where: { handle } })
    if (!row) {
      throw new Response(null, { status: 500 })
    }
    headers.append(
      'Set-Cookie',
      await serializeAuthCookie({
        email,
        nickname: row.nickname,
      }),
    )
    headers.append('Set-Cookie', await cookie.serialize('', { maxAge: 1 }))
    return redirect($path('/'), { headers })
  }

  throw new Error('Impossible!')
}
