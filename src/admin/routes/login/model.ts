import { OTPFamily } from '@deps/otp-manager'
import { z } from 'zod'

export const loginAttemptSchema = z.object({
  email: z.string().email(),
  token: z.string(),
})
export type LoginAttempt = z.infer<typeof loginAttemptSchema>

export const CODE_DURATION = 5 * 60

export const adminLoginOTPFamily: OTPFamily = {
  name: 'ADMIN_LOGIN',
  length: 6,
  duration: CODE_DURATION,
  charset: 'digits',
}

export const actionSchema = z.discriminatedUnion('action', [
  z.object({
    action: z.literal('change-email'),
  }),
  z.object({
    action: z.literal('send-code'),
    email: z.string().email(),
  }),
  z.object({
    action: z.literal('verify-code'),
    email: z.string().email(),
    code: z.string().length(6),
    token: z.string(),
  }),
])

export type Action = z.infer<typeof actionSchema>

export type ActionError = 'wrong-email' | 'wrong-code'

export function errorMessage(error: ActionError): string {
  if (error === 'wrong-email') {
    return 'Invalid email.'
  } else {
    return 'The code is invalid or has expired.'
  }
}
