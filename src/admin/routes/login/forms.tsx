import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { REGEXP_ONLY_DIGITS } from 'input-otp'
import { useForm } from 'react-hook-form'
import { useEffect } from 'react'
import { Link, Form, useActionData, useNavigation, useSubmit } from '@remix-run/react'
import { z } from 'zod'

import { Loader2 } from 'lucide-react'
import { Button } from '@axiia-ui/components/button'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form as UIForm,
} from '@axiia-ui/components/form'
import { Input } from '@axiia-ui/components/input'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@axiia-ui/components/input-otp'
import { ActionError, errorMessage } from './model'

const formSendCodeSchema = z.object({
  email: z.string().email(),
})
const formSendCodeResolver = zodResolver(formSendCodeSchema)

export function FormSendCode() {
  const form = useForm<z.infer<typeof formSendCodeSchema>>({
    resolver: formSendCodeResolver,
    defaultValues: {
      email: '',
    },
  })

  const serverError = useServerError()
  useEffect(() => {
    if (serverError === 'wrong-email') {
      form.setError('email', { type: 'custom', message: errorMessage(serverError) })
      return
    }
  }, [serverError, form])

  const submit = useSubmit()
  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    const target = e.currentTarget
    form.handleSubmit(() => {
      submit(target)
    })()
  }

  return (
    <UIForm {...form}>
      <Form method="post" onSubmit={handleSubmit}>
        <div className="grid gap-8">
          <input name="action" value="send-code" type="hidden" />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" required {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <SubmitButton title="Send Code" />
        </div>
      </Form>
    </UIForm>
  )
}

const formVerifyCodeSchema = z.object({
  code: z.string().length(6),
})
const formVerifyCodeResolver = zodResolver(formVerifyCodeSchema)

export function FormVerifyCode({ email, token }: { email: string; token: string }) {
  const form = useForm<z.infer<typeof formVerifyCodeSchema>>({
    resolver: formVerifyCodeResolver,
    defaultValues: {
      code: '',
    },
  })

  const serverError = useServerError()
  useEffect(() => {
    if (serverError === 'wrong-code') {
      form.setError('code', { type: 'custom', message: errorMessage(serverError) })
      return
    }
  }, [serverError, form])

  const submit = useSubmit()
  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    const target = e.currentTarget
    form.handleSubmit(() => {
      submit(target)
    })()
  }

  function handleChangeEmail(e: React.MouseEvent<HTMLAnchorElement>) {
    e.preventDefault()
    submit({ action: 'change-email' }, { method: 'post' })
  }
  function handleResendCode(e: React.MouseEvent<HTMLAnchorElement>) {
    e.preventDefault()
    submit({ action: 'send-code', email }, { method: 'post' })
  }

  return (
    <UIForm {...form}>
      <Form method="post" onSubmit={handleSubmit}>
        <div className="grid gap-8">
          <input name="action" value="verify-code" type="hidden" />
          <input name="email" value={email} type="hidden" />
          <input name="token" value={token} type="hidden" />

          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} pattern={REGEXP_ONLY_DIGITS} {...field}>
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-2">
            <Link to="#" className="text-sm underline" onClick={handleChangeEmail}>
              Change email
            </Link>
            <Link to="#" className="text-sm underline" onClick={handleResendCode}>
              Resend code
            </Link>
          </div>

          <SubmitButton title="Login" />
        </div>
      </Form>
    </UIForm>
  )
}

function useServerError(): ActionError | undefined {
  const actionData = useActionData<{ error?: ActionError }>()
  return actionData?.error
}

function SubmitButton({ title, disabled }: { title: string; disabled?: boolean }) {
  const navigation = useNavigation()
  return (
    <Button
      type="submit"
      className="w-full"
      disabled={disabled || navigation.state === 'submitting'}
    >
      {navigation.state === 'submitting' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {title}
    </Button>
  )
}
