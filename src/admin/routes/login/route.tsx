import {
  ActionFunction<PERSON>rgs,
  json,
  LoaderFunction<PERSON>rgs,
  MetaFunction,
  redirect,
} from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { $path } from 'remix-routes'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@axiia-ui/components/card'
import { getAuthValue } from '@admin/auth'

import { handleAction } from './action.server'
import { getLoginAttempt } from './cookie.server'
import { FormSendCode, FormVerifyCode } from './forms'
import { actionSchema } from './model'

export const meta: MetaFunction = () => {
  return [{ title: 'Login' }]
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const authValue = await getAuthValue(request)
  if (authValue) {
    return redirect($path('/'))
  }

  const loginAttempt = await getLoginAttempt(request)
  return json({ loginAttempt })
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData()
  const parseResult = actionSchema.safeParse(Object.fromEntries(formData.entries()))
  if (!parseResult.success) {
    throw new Response(null, { status: 400 })
  }
  const response = await handleAction(parseResult.data)
  return response
}

export default function Login() {
  const { loginAttempt } = useLoaderData<typeof loader>()

  let description = 'Enter your email below to login to your account.'
  if (loginAttempt) {
    description = `Enter the code sent to ${loginAttempt.email}`
  }

  return (
    <div className="flex h-screen w-full items-center justify-center px-4">
      <Card className="mx-auto max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          {!loginAttempt && <FormSendCode />}
          {loginAttempt && <FormVerifyCode {...loginAttempt} />}
        </CardContent>
      </Card>
    </div>
  )
}
