import { <PERSON><PERSON> } from '@remix-run/node'
import getCookieManager from '@deps/cookie-manager'
import { CODE_DURATION, LoginAttempt, loginAttemptSchema } from './model'

export async function getLoginAttemptCookie(): Promise<Cookie> {
  const manager = await getCookieManager()
  return await manager.createCookie('admin-login', {
    path: '/login',
    maxAge: CODE_DURATION,
  })
}

export async function getLoginAttempt(request: Request): Promise<LoginAttempt | undefined> {
  const cookie = await getLoginAttemptCookie()
  const cookieValue = await cookie.parse(request.headers.get('Cookie'))
  const parseResult = loginAttemptSchema.safeParse(cookieValue)
  if (parseResult.success) {
    return parseResult.data
  }
}
