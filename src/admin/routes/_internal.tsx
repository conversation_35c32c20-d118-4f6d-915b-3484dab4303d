import { BrainCircuit, LogOut, Home, UserCircle } from 'lucide-react'
import { json, LoaderFunctionArgs } from '@remix-run/node'
import { Link, Outlet, useLoaderData, useLocation } from '@remix-run/react'
import { requireLogin } from '@admin/auth'
import { getAvailablePanels } from '@admin/models/panels.server'
import { resolveIcon } from '@admin/models/panels.model'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const value = await requireLogin(request)
  const panels = getAvailablePanels(value.email)
  return json({ ...value, panels })
}

interface NavLinkProps {
  Icon: React.ElementType
  title: string
  to: string
}

function NavLink({ Icon, title, to }: NavLinkProps) {
  const { pathname } = useLocation()

  let isActive = pathname.startsWith(to)
  if (to === '/') {
    isActive = pathname === '/'
  }

  let className = 'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary'
  if (isActive) {
    className += ' text-primary bg-muted'
  } else {
    className += ' text-muted-foreground'
  }

  return (
    <Link to={to} className={className}>
      {Icon && <Icon className="h-4 w-4" />}
      {title}
    </Link>
  )
}

export default function Internal() {
  const { nickname, panels } = useLoaderData<typeof loader>()
  return (
    <div className="grid min-h-screen w-screen md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
      <div className="bg-muted/40 hidden border-r md:block">
        <div className="flex h-full max-h-screen flex-col">
          <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
            <Link to="/" className="flex items-center gap-2 font-semibold">
              <BrainCircuit className="h-6 w-6" />
              <span className="">Isofucius</span>
            </Link>
          </div>
          <div className="flex flex-1 flex-col justify-between py-2">
            <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
              <NavLink Icon={Home} title="Dashboard" to="/" />
              {panels.map(({ panel, iconName, title, path }) => (
                <NavLink key={panel} Icon={resolveIcon(iconName)} title={title} to={path} />
              ))}
            </nav>
          </div>
          <div className="flex h-10 items-center justify-between border-t px-4 lg:h-[60px] lg:px-6">
            <div className="flex items-center gap-2">
              <UserCircle className="h-5 w-5" />
              <span className="text-sm font-medium">{nickname}</span>
            </div>
            <Link to="/logout">
              <LogOut className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
      <main className="gap-4 overflow-hidden lg:gap-6">
        <Outlet />
      </main>
    </div>
  )
}
