import getPrismaClient from '@deps/prisma-client'
import { LoaderFunctionArgs } from '@remix-run/node'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url)
  const testIds = url.searchParams.getAll('testId')

  const client = await getPrismaClient()

  const tests = await client.peaTest.findMany({
    where: testIds.length > 0 ? { id: { in: testIds } } : {},
    include: {
      progresses: {
        orderBy: {
          createdAt: 'asc',
        },
        include: {
          resolvedProblem: {
            include: {
              problem: true,
            },
          },
          axiiaChat: {
            include: {
              chat: {
                include: {
                  chatRequests: {
                    orderBy: {
                      createdAt: 'asc',
                    },
                  },
                },
              },
            },
          },
          customChats: {
            include: {
              chat: {
                include: {
                  chatRequests: {
                    orderBy: {
                      createdAt: 'asc',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  })

  const result = tests.map((test) => ({
    id: test.id,
    isComplete: test.isComplete,
    progresses: test.progresses.map((progress) => ({
      id: progress.id,
      problemId: progress.resolvedProblem?.problemId,
      axiiaChat: {
        systemPrompt: progress.axiiaChat.chat.systemPrompt,
        chatRequests: progress.axiiaChat.chat.chatRequests.map((req) => ({
          input: req.input,
          output: req.output,
        })),
      },
      customChats: progress.customChats.map((chat) => ({
        title: chat.title,
        systemPrompt: chat.chat.systemPrompt,
        chatRequests: chat.chat.chatRequests.map((req) => ({
          input: req.input,
          output: req.output,
        })),
      })),
    })),
  }))

  return new Response(JSON.stringify(result, null, 2), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
