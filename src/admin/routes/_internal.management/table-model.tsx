import { Button } from '@axiia-ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@axiia-ui/components/dropdown-menu'
import { ColumnDef } from '@tanstack/react-table'
import { MoreHorizontal } from 'lucide-react'

export type Employee = {
  handle: string
  nickname: string
  privateEmail: string
}

export const columns: ColumnDef<Employee>[] = [
  {
    header: 'Nickname',
    accessorKey: 'nickname',
  },
  {
    header: 'Handle',
    accessorKey: 'handle',
  },
  {
    header: 'Private Email',
    accessorKey: 'privateEmail',
  },
  {
    id: 'action',
    cell: () => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
