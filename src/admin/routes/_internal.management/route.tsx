import getPrismaClient from '@deps/prisma-client'
import { json, LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { AutoPagination } from '../../components/auto-pagination'
import { columns, Employee } from './table-model'
import { DataTable } from '../../components/data-table'
import { Card, CardContent, CardTitle, CardFooter, CardHeader } from '@axiia-ui/components/card'

export const meta: MetaFunction = () => {
  return [{ title: 'Management' }]
}

const ITEM_PER_PAGE = 20

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url)
  const top = Number(url.searchParams.get('top')) || ITEM_PER_PAGE
  const skip = Number(url.searchParams.get('skip')) || 0

  const client = await getPrismaClient()
  const count = await client.employee.count()
  const rows = await client.employee.findMany({
    orderBy: {
      nickname: 'asc',
    },
    take: top,
    skip: skip,
  })
  const list: Employee[] = rows.map((row) => {
    return {
      handle: row.handle,
      nickname: row.nickname,
      privateEmail: row.privateEmail,
    } satisfies Employee
  })
  return json({ list, count })
}

export default function Management() {
  const { count, list } = useLoaderData<typeof loader>()

  return (
    <div className="container mx-auto h-screen overflow-hidden py-6">
      <Card className="flex h-full flex-col">
        <CardHeader>
          <CardTitle>Employees</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 overflow-y-auto">
          <DataTable columns={columns} data={list} />
        </CardContent>
        <CardFooter>
          <AutoPagination count={count} itemPerPage={ITEM_PER_PAGE} />
        </CardFooter>
      </Card>
    </div>
  )
}
