import getPrismaClient from '@deps/prisma-client'
import { LoaderFunctionArgs } from '@remix-run/node'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url)
  const testIds = url.searchParams.getAll('testId')

  const client = await getPrismaClient()

  const tests = await client.peaTest.findMany({
    where: testIds.length > 0 ? { id: { in: testIds } } : {},
    include: {
      problemSet: true,
      progresses: {
        orderBy: {
          createdAt: 'asc',
        },
        include: {
          resolvedProblem: {
            include: {
              problem: true,
            },
          },
          axiiaChat: {
            include: {
              chat: {
                include: {
                  chatRequests: {
                    orderBy: {
                      createdAt: 'asc',
                    },
                  },
                },
              },
            },
          },
          submissionSnapshots: {
            orderBy: {
              timestamp: 'asc',
            },
            include: {
              text: true,
              feedback: {
                include: {
                  feedbackMemberships: {
                    include: {
                      run: true,
                    },
                  },
                },
              },
            },
          },
          customChats: {
            include: {
              chat: {
                include: {
                  chatRequests: {
                    orderBy: {
                      createdAt: 'asc',
                    },
                  },
                },
              },
            },
          },
          labelPredictionPairTests: {
            orderBy: {
              timestamp: 'asc',
            },
            include: {
              pair: true,
            },
          },
        },
      },
    },
  })

  const result = tests.map((test) => ({
    id: test.id,
    isComplete: test.isComplete,
    problemSet: test.problemSet,
    progresses: test.progresses.map((progress) => ({
      id: progress.id,
      problemId: progress.resolvedProblem?.problemId,
      createdAt: progress.createdAt.toISOString(),
      completedAt: progress.completedAt?.toISOString(),
      labelPredictionBatchTestUnlocked: progress.labelPredictionBatchTestUnlocked,
      axiiaChat: {
        ...progress.axiiaChat,
        chat: {
          ...progress.axiiaChat.chat,
          chatRequests: progress.axiiaChat.chat.chatRequests.map((req) => ({
            ...req,
            createdAt: req.createdAt.toISOString(),
            updatedAt: req.updatedAt.toISOString(),
          })),
        },
      },
      submissionSnapshots: progress.submissionSnapshots.map((snapshot) => ({
        ...snapshot,
        text: snapshot.text.text,
        timestamp: snapshot.timestamp.toISOString(),
        feedback: snapshot.feedback
          ? {
              ...snapshot.feedback,
              timestamp: snapshot.feedback.timestamp.toISOString(),
              feedbackMemberships: snapshot.feedback.feedbackMemberships.map((membership) => ({
                ...membership,
                run: {
                  ...membership.run,
                  createdAt: membership.run.createdAt.toISOString(),
                  updatedAt: membership.run.updatedAt.toISOString(),
                },
              })),
            }
          : null,
      })),
      customChats: progress.customChats.map((chat) => ({
        ...chat,
        chat: {
          ...chat.chat,
          chatRequests: chat.chat.chatRequests.map((req) => ({
            ...req,
            createdAt: req.createdAt.toISOString(),
            updatedAt: req.updatedAt.toISOString(),
          })),
        },
      })),
      labelPredictionPairTests: progress.labelPredictionPairTests.map((test) => ({
        ...test,
        timestamp: test.timestamp.toISOString(),
        pair: test.pair,
      })),
    })),
  }))

  return new Response(JSON.stringify(result, null, 2), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
