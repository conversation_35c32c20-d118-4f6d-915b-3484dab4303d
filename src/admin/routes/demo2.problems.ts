import logger from '@common/deps.server/logger'
import getPrismaClient from '@deps/prisma-client'
import { json } from '@remix-run/node'

export const loader = async () => {
  try {
    const client = await getPrismaClient()
    const rows = await client.peaProblem.findMany({
      where: {
        AND: [
          { id: { gte: '000000-' } },
          { id: { lt: '999999.' } }, // Using . as it comes after - in ASCII
        ],
      },
      distinct: ['id'],
      orderBy: [{ id: 'asc' }, { version: 'desc' }],
    })

    const problems: string[] = []
    for (const row of rows) {
      problems.push(row.id)
    }

    return json({ problems })
  } catch (error) {
    logger.error(error, 'Error processing request:')
    return json({ message: 'Error processing request' }, { status: 500 })
  }
}
