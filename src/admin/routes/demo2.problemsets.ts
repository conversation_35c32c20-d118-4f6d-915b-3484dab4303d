import logger from '@common/deps.server/logger'
import getPrismaClient from '@deps/prisma-client'
import type { ActionFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { createProblemSetFromDynamicProblems } from 'src/demo2/database.server/test'
import { z } from 'zod'

const createProblemSetInputSchema = z.object({
  problemIds: z.array(z.string()),
  expireMinutes: z.number().optional(),
})

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json()
    const input = createProblemSetInputSchema.parse(body)

    const client = await getPrismaClient()
    const problemSetId = await createProblemSetFromDynamicProblems(
      input.problemIds,
      client,
      input.expireMinutes,
    )

    return json({ problemSetId }, { status: 200 })
  } catch (error) {
    logger.error(error, 'Error processing request:')
    if (error instanceof z.ZodError) {
      return json({ message: 'Invalid input data', errors: error.flatten() }, { status: 400 })
    }
    return json({ message: 'Error processing request' }, { status: 500 })
  }
}
