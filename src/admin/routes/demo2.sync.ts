import logger from '@common/deps.server/logger'
import getPrismaClient from '@deps/prisma-client'
import type { ActionFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { createOrUpdateBotPreset } from 'src/demo2/database.server/bot-presets'
import { createOrUpdateProblem } from 'src/demo2/database.server/problem'
import { botPresetInputSchema } from 'src/demo2/models/bot-presets'
import { problemInputSchema } from 'src/demo2/models/problem'
import { z } from 'zod'

const syncInputSchema = z.object({
  bots: z.array(botPresetInputSchema),
  problems: z.array(problemInputSchema),
})

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json()
    const input = syncInputSchema.parse(body)

    const client = await getPrismaClient()
    const botResults = await Promise.all(
      input.bots.map((bot) => createOrUpdateBotPreset(bot, client)),
    )

    const problemResults = await Promise.all(
      input.problems.map((problem) => createOrUpdateProblem(problem, client)),
    )

    const result = {
      bots: botResults.map(({ version, changed }) => ({ ...version, changed })),
      problems: problemResults.map(({ version, changed }) => ({ ...version, changed })),
    }
    return json(result, { status: 200 })
  } catch (error) {
    logger.error(error, 'Error processing request:')
    if (error instanceof z.ZodError) {
      return json({ message: 'Invalid input data', errors: error.flatten() }, { status: 400 })
    }
    return json({ message: 'Error processing request' }, { status: 500 })
  }
}
