import { requireLogin } from '@admin/auth'
import { LoaderFunctionArgs } from '@remix-run/node'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await requireLogin(request)
  return null
}

export default function Admin() {
  return (
    <div className="container flex flex-col items-center justify-center">
      <h1 className="mt-4 text-3xl font-bold">Admin Dashboard</h1>
    </div>
  )
}
