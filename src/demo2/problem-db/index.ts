import { z } from 'zod'
import { syncInputSchema } from './input-schema'
import { BotPresetVersion } from '../models/bot-presets'
import { ProblemVersion } from '../models/problem'

export type SyncInput = z.infer<typeof syncInputSchema>

export interface SyncResult {
  bots: {
    version: BotPresetVersion
    changed: boolean
  }[]
  problems: {
    version: ProblemVersion
    changed: boolean
  }[]
}

export function parseSyncInput(raw: unknown): SyncInput {
  return syncInputSchema.parse(raw)
}
