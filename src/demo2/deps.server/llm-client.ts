import dependencyManager from '@common/deps.server/manager'
import OpenAI from 'openai'
import invariant from 'tiny-invariant'

export interface LLMInput {
  input: string
  model?: string
  temperature?: number
}

export interface LLMResult {
  requestId: string
  output: string
}

export interface LLMInterface {
  generateText(input: LLMInput): Promise<LLMResult>
}

export class LLMClient {
  private llmInterface: LLMInterface

  constructor(llmInterface: LLMInterface) {
    this.llmInterface = llmInterface
  }

  async generateText(input: LLMInput): Promise<LLMResult> {
    try {
      return await this.llmInterface.generateText(input)
    } catch (error) {
      console.error('Error in generateText:', error)
      throw error
    }
  }
}

export class DynamicLLMInterface implements LLMInterface {
  private makeResponse: (input: LLMInput) => Promise<string>

  constructor(makeResponse: (input: LLMInput) => Promise<string>) {
    this.makeResponse = makeResponse
  }

  async generateText(input: LLMInput): Promise<LLMResult> {
    const output = await this.makeResponse(input)
    return { requestId: crypto.randomUUID(), output }
  }

  static pure(makeResponse: (input: LLMInput) => string): DynamicLLMInterface {
    return new DynamicLLMInterface(async (input) => makeResponse(input))
  }
}

export class HeliconeLLMInterface implements LLMInterface {
  private openai: OpenAI
  private maxRetries: number
  private retryDelay: number

  constructor(openai: OpenAI, maxRetries = 3, retryDelay = 1000) {
    this.openai = openai
    this.maxRetries = maxRetries
    this.retryDelay = retryDelay
  }

  private async retry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        console.error(`Attempt ${attempt + 1} failed:`, error)
        lastError = error as Error
        if (attempt < this.maxRetries - 1) {
          await new Promise((resolve) => setTimeout(resolve, this.retryDelay))
        }
      }
    }
    throw lastError || new Error('All retry attempts failed')
  }

  async generateText(input: LLMInput): Promise<LLMResult> {
    return this.retry(async () => {
      if (!input.input || input.input.trim().length === 0) {
        throw new Error('Input text cannot be empty')
      }

      const chatCompletion = await this.openai.chat.completions.create({
        model: input.model || 'gpt-4o',
        messages: [{ role: 'user', content: input.input }],
        temperature: input.temperature,
      })

      const requestId = chatCompletion.id
      invariant(
        chatCompletion.choices[0].message.content,
        'Upstream LLM does not give any text output.',
      )

      return { requestId, output: chatCompletion.choices[0].message.content }
    })
  }
}

dependencyManager.registerIfNeeded(LLMClient, async () => {
  const apiKey = process.env.OPENAI_API_KEY
  const heliconeApiKey = process.env.HELICONE_API_KEY

  if (!apiKey || !heliconeApiKey) {
    throw new Error('Missing required environment variables')
  }

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: 'https://oai.helicone.ai/v1',
    defaultHeaders: {
      'Helicone-Auth': `Bearer ${heliconeApiKey}`,
    },
  })

  const llmInterface = new HeliconeLLMInterface(openai)
  return new LLMClient(llmInterface)
})

export default async function getLLMClient(): Promise<LLMClient> {
  return await dependencyManager.get(LLMClient)
}
