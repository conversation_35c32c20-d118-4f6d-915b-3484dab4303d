import dependencyManager from '@common/deps.server/manager'

export interface ClockInterface {
  currentTime(): Date
  sleep(ms: number): Promise<void>
  step(ms: number): void
  flushAll(): void
}

export class ClockClient {
  private clockInterface: ClockInterface

  constructor(clockInterface: ClockInterface) {
    this.clockInterface = clockInterface
  }

  currentTime(): Date {
    return this.clockInterface.currentTime()
  }

  async sleep(ms: number): Promise<void> {
    return this.clockInterface.sleep(ms)
  }

  step(ms: number): void {
    this.clockInterface.step(ms)
  }

  flushAll() {
    this.clockInterface.flushAll()
  }
}

export class SystemClockInterface implements ClockInterface {
  currentTime(): Date {
    return new Date()
  }

  async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  step(): void {
    throw new Error('Cannot step system clock')
  }

  flushAll(): void {}
}

export class ControllableClockInterface implements ClockInterface {
  private currentTimeValue: Date
  sleepPromises: Array<{ resolveTime: number; resolve: () => void }> = []

  constructor() {
    this.currentTimeValue = new Date()
  }

  currentTime(): Date {
    return new Date(this.currentTimeValue)
  }

  async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => {
      const resolveTime = this.currentTimeValue.getTime() + ms
      this.sleepPromises.push({ resolveTime, resolve })
      this.sleepPromises.sort((a, b) => a.resolveTime - b.resolveTime)
    })
  }

  step(ms: number): void {
    this.currentTimeValue = new Date(this.currentTimeValue.getTime() + ms)

    const now = this.currentTimeValue.getTime()
    this.sleepPromises = this.sleepPromises.filter(({ resolveTime, resolve }) => {
      if (now >= resolveTime) {
        resolve()
        return false
      }
      return true
    })
  }

  flushAll(): void {
    this.sleepPromises.forEach(({ resolve }) => {
      resolve()
    })
  }
}

dependencyManager.registerIfNeeded(ClockClient, async () => {
  const clockInterface = new SystemClockInterface()
  return new ClockClient(clockInterface)
})

export default async function getClockClient(): Promise<ClockClient> {
  return await dependencyManager.get(ClockClient)
}
