import dependencyManager from '@common/deps.server/manager'
import get<PERSON><PERSON><PERSON><PERSON> from './clock-client'
import Redis from 'ioredis'

export interface TaskObservationInterface {
  startTask(): Promise<string>
  completeTask(taskId: string): Promise<void>
  waitForTask(taskId: string): Promise<void>
}

export class TaskObservationClient {
  private taskObservationInterface: TaskObservationInterface

  constructor(taskObservationInterface: TaskObservationInterface) {
    this.taskObservationInterface = taskObservationInterface
  }

  async startTask(): Promise<string> {
    return this.taskObservationInterface.startTask()
  }

  async completeTask(taskId: string): Promise<void> {
    return this.taskObservationInterface.completeTask(taskId)
  }

  async waitForTask(taskId: string, timeout?: number): Promise<void> {
    const client = await getClockClient()
    const tasks = [this.taskObservationInterface.waitForTask(taskId)]
    if (timeout !== undefined && timeout > 0) {
      tasks.push(client.sleep(timeout))
    }
    return Promise.race(tasks)
  }
}

export class InMemoryTaskObservationInterface implements TaskObservationInterface {
  private tasks: Map<string, { resolve: () => void }> = new Map()
  private completedTasks: Set<string> = new Set()

  async startTask(): Promise<string> {
    const taskId = crypto.randomUUID()
    let resolveTask: () => void
    new Promise<void>((resolve) => {
      resolveTask = resolve
    })
    this.tasks.set(taskId, { resolve: resolveTask! })
    return taskId
  }

  async completeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error(`Task not found: ${taskId}`)
    }
    task.resolve()
    this.tasks.delete(taskId)
    this.completedTasks.add(taskId)
  }

  async waitForTask(taskId: string): Promise<void> {
    if (this.completedTasks.has(taskId)) {
      return
    }
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error(`Task not found: ${taskId}`)
    }
    return new Promise<void>((resolve) => {
      task.resolve = () => {
        resolve()
        this.completedTasks.add(taskId)
      }
    })
  }
}

export class RedisTaskObservationInterface implements TaskObservationInterface {
  private redis: Redis

  constructor(redisHost: string, redisPort: number) {
    this.redis = new Redis({
      host: redisHost,
      port: redisPort,
    })
  }

  async startTask(): Promise<string> {
    const taskId = crypto.randomUUID()
    await this.redis.set(`task:${taskId}`, 'pending', 'EX', 300) // Set expiration to 5 minutes
    return taskId
  }

  async completeTask(taskId: string): Promise<void> {
    const taskStatus = await this.redis.get(`task:${taskId}`)
    if (!taskStatus) {
      throw new Error(`Task not found: ${taskId}`)
    }
    await this.redis.set(`task:${taskId}`, 'completed', 'EX', 300)
    await this.redis.publish(`taskComplete:${taskId}`, 'completed')
  }

  async waitForTask(taskId: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const subscriber = this.redis.duplicate()

      const cleanup = () => {
        subscriber.unsubscribe()
        subscriber.quit()
      }

      subscriber.on('error', (error) => {
        cleanup()
        reject(error)
      })

      subscriber.subscribe(`taskComplete:${taskId}`, async () => {
        try {
          const taskStatus = await this.redis.get(`task:${taskId}`)
          if (taskStatus === 'completed') {
            cleanup()
            resolve()
            return
          }
          if (!taskStatus) {
            cleanup()
            reject(new Error(`Task not found: ${taskId}`))
            return
          }

          subscriber.on('message', (channel, message) => {
            if (channel === `taskComplete:${taskId}` && message === 'completed') {
              cleanup()
              resolve()
            }
          })
        } catch (error) {
          cleanup()
          reject(error)
        }
      })
    })
  }
}

dependencyManager.registerIfNeeded(TaskObservationClient, async () => {
  const useRedis = process.env.DEMO2_ENABLE_REDIS
    ? process.env.DEMO2_ENABLE_REDIS === '1'
    : process.env.NODE_ENV === 'production'

  if (useRedis) {
    const redisHost = process.env.REDIS_HOST
    const redisPort = process.env.REDIS_PORT
    if (!redisHost || !redisPort) {
      throw new Error('REDIS_HOST and REDIS_PORT environment variables are not set')
    }
    return new TaskObservationClient(
      new RedisTaskObservationInterface(redisHost, Number(redisPort)),
    )
  } else {
    return new TaskObservationClient(new InMemoryTaskObservationInterface())
  }
})

export default async function getTaskObservationClient(): Promise<TaskObservationClient> {
  return await dependencyManager.get(TaskObservationClient)
}
