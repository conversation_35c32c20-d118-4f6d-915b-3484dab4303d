import dependencyManager from '@common/deps.server/manager'
import logger from '@deps/logger'
import Redis from 'ioredis'

export type BroadcastChannelValue = { type: 'value' | 'complete'; value: string }

export interface BroadcastInterface {
  createChannel(): Promise<string>
  post(channelId: string, text: string): Promise<void>
  retrieve(channelId: string): Promise<BroadcastChannelValue | undefined>
  close(channelId: string): Promise<void>
}

export class BroadcastClient {
  private broadcastInterface: BroadcastInterface

  constructor(broadcastInterface: BroadcastInterface) {
    this.broadcastInterface = broadcastInterface
  }

  async createChannel(): Promise<string> {
    return this.broadcastInterface.createChannel()
  }

  async post(channelId: string, text: string): Promise<void> {
    return this.broadcastInterface.post(channelId, text)
  }

  async retrieve(channelId: string): Promise<BroadcastChannelValue | undefined> {
    return this.broadcastInterface.retrieve(channelId)
  }

  async close(channelId: string): Promise<void> {
    return this.broadcastInterface.close(channelId)
  }
}

export class InMemoryBroadcastInterface implements BroadcastInterface {
  private channels: Map<string, BroadcastChannelValue> = new Map()

  async createChannel(): Promise<string> {
    const channelId = crypto.randomUUID()
    this.channels.set(channelId, { type: 'value', value: '' })
    return channelId
  }

  async post(channelId: string, text: string): Promise<void> {
    const channel = this.channels.get(channelId)
    if (!channel || channel.type === 'complete') {
      throw new Error(`Channel not found or already closed: ${channelId}`)
    }
    this.channels.set(channelId, { type: 'value', value: text })
  }

  async retrieve(channelId: string): Promise<BroadcastChannelValue | undefined> {
    return this.channels.get(channelId)
  }

  async close(channelId: string): Promise<void> {
    const channel = this.channels.get(channelId)
    if (!channel) {
      throw new Error(`Channel not found: ${channelId}`)
    }
    this.channels.set(channelId, { type: 'complete', value: channel.value })
  }
}
export class RedisBroadcastInterface implements BroadcastInterface {
  private redis: Redis

  constructor(redisHost: string, redisPort: number) {
    this.redis = new Redis({
      host: redisHost,
      port: redisPort,
    })
  }

  async createChannel(): Promise<string> {
    const channelId = crypto.randomUUID()
    await this.redis.set(
      `channel:${channelId}`,
      JSON.stringify({ type: 'value', value: '' }),
      'EX',
      120,
    )
    return channelId
  }

  async post(channelId: string, text: string): Promise<void> {
    const channel = await this.redis.get(`channel:${channelId}`)
    if (!channel || JSON.parse(channel).type === 'complete') {
      throw new Error(`Channel not found or already closed: ${channelId}`)
    }
    await this.redis.set(
      `channel:${channelId}`,
      JSON.stringify({ type: 'value', value: text }),
      'EX',
      120,
    )
  }

  async retrieve(channelId: string): Promise<BroadcastChannelValue | undefined> {
    const channel = await this.redis.get(`channel:${channelId}`)
    if (!channel) return undefined
    try {
      return JSON.parse(channel)
    } catch (error) {
      logger.error(error, `An error occurred when parsing channel data for ${channelId}.`)
      return undefined
    }
  }

  async close(channelId: string): Promise<void> {
    const channel = await this.redis.get(`channel:${channelId}`)
    if (!channel) {
      throw new Error(`Channel not found: ${channelId}`)
    }
    const parsedChannel = JSON.parse(channel)
    await this.redis.set(
      `channel:${channelId}`,
      JSON.stringify({ type: 'complete', value: parsedChannel.value }),
      'EX',
      120,
    )
  }
}

dependencyManager.registerIfNeeded(BroadcastClient, async () => {
  if (process.env.NODE_ENV === 'test') {
    return new BroadcastClient(new InMemoryBroadcastInterface())
  }

  const useRedis = process.env.DEMO2_ENABLE_REDIS
    ? process.env.DEMO2_ENABLE_REDIS === '1'
    : process.env.NODE_ENV === 'production'

  if (useRedis) {
    const redisHost = process.env.REDIS_HOST
    const redisPort = process.env.REDIS_PORT
    if (!redisHost || !redisPort) {
      throw new Error('REDIS_HOST and REDIS_PORT environment variables are not set')
    }
    return new BroadcastClient(new RedisBroadcastInterface(redisHost, Number(redisPort)))
  } else {
    return new BroadcastClient(new InMemoryBroadcastInterface())
  }
})

export default async function getBroadcastClient(): Promise<BroadcastClient> {
  return await dependencyManager.get(BroadcastClient)
}
