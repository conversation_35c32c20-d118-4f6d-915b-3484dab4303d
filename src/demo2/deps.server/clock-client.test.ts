import { describe, it, expect, beforeEach } from 'vitest'
import { ControllableClockInterface } from './clock-client'

describe('ControllableClockInterface', () => {
  let clock: ControllableClockInterface

  beforeEach(() => {
    clock = new ControllableClockInterface()
  })

  it('should initialize with the current time', () => {
    const now = new Date()
    expect(clock.currentTime().getTime()).toBeCloseTo(now.getTime(), -2) // Allow 100ms difference
  })

  it('should step forward in time', () => {
    const initialTime = clock.currentTime()
    clock.step(1000)
    expect(clock.currentTime().getTime()).toBe(initialTime.getTime() + 1000)
  })

  it('should resolve sleep promises in order', async () => {
    const results: number[] = []

    const task1 = clock.sleep(20).then(() => results.push(1))
    const task2 = clock.sleep(30).then(() => results.push(2))
    const task3 = clock.sleep(40).then(() => results.push(3))

    clock.step(50)

    await Promise.all([task1, task2, task3])

    expect(results).toEqual([1, 2, 3])
  })

  it('should resolve sleep promises in order when inserted in random order', async () => {
    const results: number[] = []

    const tasks = [
      clock.sleep(20).then(() => results.push(1)),
      clock.sleep(30).then(() => results.push(2)),
      clock.sleep(40).then(() => results.push(3)),
    ]

    // Shuffle the order of insertion
    tasks.sort(() => Math.random() - 0.5)

    clock.step(50)

    await Promise.all(tasks)

    expect(results).toEqual([1, 2, 3])
  })

  it('should not resolve promises before their time', async () => {
    const results: number[] = []

    const task1 = clock.sleep(20).then(() => results.push(1))
    const task2 = clock.sleep(30).then(() => results.push(2))

    clock.step(25)

    await task1
    expect(results).toEqual([1])

    clock.step(10)

    await task2
    expect(results).toEqual([1, 2])
  })

  it('should handle multiple steps', async () => {
    const results: number[] = []

    const task1 = clock.sleep(20).then(() => results.push(1))
    const task2 = clock.sleep(30).then(() => results.push(2))
    const task3 = clock.sleep(40).then(() => results.push(3))

    clock.step(25)
    await task1
    expect(results).toEqual([1])

    clock.step(10)
    await task2
    expect(results).toEqual([1, 2])

    clock.step(5)
    await task3
    expect(results).toEqual([1, 2, 3])
  })
})
