import OpenAI from 'openai'
import dependencyManager from '@common/deps.server/manager'

export interface ChatMessage {
  role: 'system' | 'assistant' | 'user'
  content: string
}

export interface ChatInput {
  messages: ChatMessage[]
  model?: string
  temperature?: number
}

export interface StreamChatResult {
  requestId: string
  stream: AsyncIterableIterator<string>
}

export interface StreamChatInterface {
  streamChat(input: ChatInput): Promise<StreamChatResult>
}

export class StreamChatClient {
  private chatInterface: StreamChatInterface

  constructor(chatInterface: StreamChatInterface) {
    this.chatInterface = chatInterface
  }

  async streamChat(input: ChatInput): Promise<StreamChatResult> {
    try {
      return await this.chatInterface.streamChat(input)
    } catch (error) {
      console.error('Error in streamChat:', error)
      throw error
    }
  }
}

export class DynamicChatInterface implements StreamChatInterface {
  private makeStream: (input: ChatInput) => AsyncIterableIterator<string>

  constructor(makeStream: (input: ChatInput) => AsyncIterableIterator<string>) {
    this.makeStream = makeStream
  }

  async streamChat(input: ChatInput): Promise<StreamChatResult> {
    return { stream: this.makeStream(input), requestId: crypto.randomUUID() }
  }

  static pure(makeResponse: (input: ChatInput) => string): DynamicChatInterface {
    return new DynamicChatInterface((input) => {
      const response = makeResponse(input)
      const stream = async function* () {
        yield response
      }
      return stream()
    })
  }
}

export class HeliconeChatInterface implements StreamChatInterface {
  private openai: OpenAI

  constructor(openai: OpenAI) {
    this.openai = openai
  }

  async streamChat(input: ChatInput): Promise<StreamChatResult> {
    try {
      const stream = await this.openai.chat.completions.create({
        model: input.model || 'gpt-4o',
        messages: input.messages,
        temperature: input.temperature,
        stream: true,
      })

      const requestId = crypto.randomUUID()

      const asyncIterator = {
        async *[Symbol.asyncIterator]() {
          for await (const chunk of stream) {
            if (chunk.choices[0]?.delta?.content) {
              yield chunk.choices[0].delta.content
            }
          }
        },
      }

      return { requestId, stream: asyncIterator[Symbol.asyncIterator]() }
    } catch (error) {
      console.error('Error in streamChat:', error)
      throw error
    }
  }
}

dependencyManager.registerIfNeeded(StreamChatClient, async () => {
  const apiKey = process.env.OPENAI_API_KEY
  const heliconeApiKey = process.env.HELICONE_API_KEY

  if (!apiKey || !heliconeApiKey) {
    throw new Error('Missing required environment variables')
  }

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: 'https://oai.helicone.ai/v1',
    defaultHeaders: {
      'Helicone-Auth': `Bearer ${heliconeApiKey}`,
    },
  })
  const chatInterface = new HeliconeChatInterface(openai)
  return new StreamChatClient(chatInterface)
})

export default async function getStreamChatClient(): Promise<StreamChatClient> {
  return await dependencyManager.get(StreamChatClient)
}
