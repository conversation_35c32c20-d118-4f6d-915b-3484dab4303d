import getPrismaClient from '@deps/prisma-client'
import { createOrUpdateBotPreset } from '../database.server/bot-presets'
import { createOrUpdateProblem } from '../database.server/problem'
import { SyncInput, SyncResult } from '../problem-db'

export async function syncInput(input: SyncInput): Promise<SyncResult> {
  const client = await getPrismaClient()
  return await client.$transaction(async (tx) => {
    const botResults = await Promise.all(input.bots.map((bot) => createOrUpdateBotPreset(bot, tx)))

    const problemResults = await Promise.all(
      input.problems.map((problem) => createOrUpdateProblem(problem, tx)),
    )

    return {
      bots: botResults.map(({ version, changed }) => ({ version, changed })),
      problems: problemResults.map(({ version, changed }) => ({ version, changed })),
    } satisfies SyncResult
  })
}
