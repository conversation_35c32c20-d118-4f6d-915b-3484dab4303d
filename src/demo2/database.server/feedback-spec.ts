import { Prisma } from '@prisma/client'
import { FeedbackSpecInput, FeedbackSpec, FeedbackSpecVersion } from '../models/feedback-spec'
import { deepEqual } from '@common/utils'
import invariant from 'tiny-invariant'

export async function createOrUpdateFeedbackSpec(
  { problemId, keys, prompt, template }: FeedbackSpecInput,
  client: Prisma.TransactionClient,
): Promise<{ version: FeedbackSpecVersion; changed: boolean }> {
  invariant(prompt.replacementKeys.length === 1)

  const latestRow = await client.peaFeedbackSpec.findFirst({
    where: { problemId },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    if (
      deepEqual(latestRow.keys, keys) &&
      latestRow.template === template &&
      latestRow.prompt === prompt.content &&
      latestRow.promptReplacementKey === prompt.replacementKeys[0]
    ) {
      return { version: { problemId, version: latestRow.version }, changed: false }
    }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaFeedbackSpec.create({
    data: {
      problemId,
      version: newVersion,
      keys,
      template,
      prompt: prompt.content,
      promptReplacementKey: prompt.replacementKeys[0],
    },
  })

  return { version: { problemId, version: newVersion }, changed: true }
}

export async function resolveLatestFeedbackSpecVersion(
  problemId: string,
  client: Prisma.TransactionClient,
): Promise<FeedbackSpecVersion | undefined> {
  const row = await client.peaFeedbackSpec.findFirst({
    where: { problemId },
    select: { version: true },
    orderBy: { version: 'desc' },
  })
  if (row === null) {
    return undefined
  }
  return { problemId, version: row.version }
}

export async function getFeedbackSpec(
  version: FeedbackSpecVersion,
  client: Prisma.TransactionClient,
): Promise<FeedbackSpec | undefined> {
  const row = await client.peaFeedbackSpec.findFirst({ where: version })
  if (!row) {
    return
  }

  return {
    ...version,
    keys: row.keys,
    template: row.template,
    prompt: row.prompt,
    promptReplacementKey: row.promptReplacementKey,
  }
}
