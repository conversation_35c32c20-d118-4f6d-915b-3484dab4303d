import logger from '@common/deps.server/logger'
import getPrismaClient from '@deps/prisma-client'
import { PeaChatRequest, Prisma, PrismaClient } from '@prisma/client'
import getBroadcastClient, { BroadcastChannelValue } from '../deps.server/broadcast-client'
import getClockClient from '../deps.server/clock-client'
import getStreamChatClient, { StreamChatResult } from '../deps.server/stream-chat-client'
import { Chat, ChatRequest, ChatRequestContent } from '../models/chat'

export type ChatRequestStatus = 'complete' | 'errored' | 'in-progress'

export function chatRequestStatus(request: PeaChatRequest, now: Date): ChatRequestStatus {
  if (request.output) {
    return 'complete'
  }
  if (request.upstreamErrored) {
    return 'errored'
  }
  if (request.terminatedByUser) {
    // This should have created an output. If not something went wrong.
    return 'errored'
  }
  if (now.getTime() - request.updatedAt.getTime() > 60 * 1000) {
    // No update for one minute. Consider it timed out.
    return 'errored'
  }
  return 'in-progress'
}

export function isChatRequestComplete(request: PeaChatRequest, now: Date): boolean {
  return chatRequestStatus(request, now) !== 'in-progress'
}

export async function loadChat(
  id: string,
  client: Prisma.TransactionClient,
): Promise<Chat | undefined> {
  const chat = await client.peaChat.findUnique({
    where: { id },
    include: {
      chatRequests: {
        orderBy: { createdAt: 'asc' },
      },
    },
  })

  if (!chat) {
    return
  }

  const clockClient = await getClockClient()

  const chatRequests: ChatRequest[] = chat.chatRequests.map((request) => ({
    id: request.id,
    createdAt: request.createdAt,
    updatedAt: request.updatedAt,
    type: chatRequestStatus(request, clockClient.currentTime()),
  }))

  return {
    id: chat.id,
    createdAt: chat.createdAt,
    chatRequests,
  }
}

export async function loadChatRequestContent(
  id: string,
  client: Prisma.TransactionClient,
): Promise<ChatRequestContent | undefined> {
  const chatRequest = await client.peaChatRequest.findUnique({
    where: { id },
  })

  if (!chatRequest) {
    return undefined
  }

  const clockClient = await getClockClient()

  if (!isChatRequestComplete(chatRequest, clockClient.currentTime())) {
    return {
      type: 'in-progress',
      input: chatRequest.input,
      streamingId: chatRequest.streamingId ?? undefined,
    }
  }

  if (chatRequest.output) {
    const { input, output } = chatRequest
    return { type: 'complete', input, output }
  }

  return {
    type: 'errored',
    input: chatRequest.input,
  }
}

export async function createChat(
  systemPrompt: string,
  client: Prisma.TransactionClient,
): Promise<string> {
  const clockClient = await getClockClient()
  const now = clockClient.currentTime()
  const chat = await client.peaChat.create({
    data: {
      createdAt: now,
      systemPrompt,
    },
    select: { id: true },
  })

  return chat.id
}

async function _createChatRequest(
  chatId: string,
  message: string,
  client: Prisma.TransactionClient,
): Promise<string> {
  const chatRow = await client.peaChat.findFirstOrThrow({
    where: { id: chatId },
    include: {
      chatRequests: {
        orderBy: {
          createdAt: 'asc',
        },
      },
    },
  })

  const { systemPrompt, chatRequests } = chatRow

  const clockClient = await getClockClient()

  if (chatRequests.length > 0) {
    const lastRequest = chatRequests[chatRequests.length - 1]
    if (!isChatRequestComplete(lastRequest, clockClient.currentTime())) {
      throw new Error(`Another chat request with id ${lastRequest.id} is in progress.`)
    }
  }

  const unresolvedMessages: string[] = [systemPrompt]
  for (const request of chatRequests) {
    if (!request.output) {
      continue
    }
    unresolvedMessages.push(request.input)
    unresolvedMessages.push(request.output)
  }

  const chatHistory = unresolvedMessages
  chatHistory.push(message)
  const chatClient = await getStreamChatClient()
  const streamChatResult = await chatClient.streamChat({
    messages: chatHistory.map((content, i) => {
      if (i == 0) {
        return { content, role: 'system' }
      }
      return { content, role: i % 2 === 1 ? 'user' : 'assistant' }
    }),
  })

  const broadcastClient = await getBroadcastClient()
  const channelId = await broadcastClient.createChannel()

  const now = clockClient.currentTime()
  const chatRequestRow = await client.peaChatRequest.create({
    data: {
      chatId,
      createdAt: now,
      updatedAt: now,
      input: message,
      upstreamRequestId: streamChatResult.requestId,
      streamingId: channelId,
    },
  })

  processStreamChatResult(streamChatResult, chatRequestRow, channelId)

  return chatRequestRow.id
}

export async function createChatRequest(
  chatId: string,
  message: string,
  client: PrismaClient,
): Promise<string> {
  return await client.$transaction(async (tx) => _createChatRequest(chatId, message, tx), {
    isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
  })
}

async function updateChatRequestIfNotComplete(
  id: string,
  body: (tx: Prisma.TransactionClient, latest: PeaChatRequest) => Promise<Partial<PeaChatRequest>>,
): Promise<boolean> {
  const client = await getPrismaClient()
  const clockClient = await getClockClient()

  return await client.$transaction(
    async (tx) => {
      const row = await tx.peaChatRequest.findUniqueOrThrow({ where: { id } })
      if (isChatRequestComplete(row, clockClient.currentTime())) {
        return false
      }
      const data = await body(tx, row)
      await tx.peaChatRequest.update({
        data: { ...data, updatedAt: clockClient.currentTime() },
        where: { id },
      })
      return true
    },
    {
      isolationLevel: Prisma.TransactionIsolationLevel.RepeatableRead,
    },
  )
}

async function processStreamChatResult(
  result: StreamChatResult,
  chatRequest: PeaChatRequest,
  channelId: string,
) {
  const broadcastClient = await getBroadcastClient()
  const clockClient = await getClockClient()

  try {
    let output = ''
    let lastBroadcastedAt = Date.now()
    let updatedAt = chatRequest.updatedAt

    for await (const chunk of result.stream) {
      output += chunk

      const now = clockClient.currentTime().getTime()

      if (now - lastBroadcastedAt > 100) {
        await broadcastClient.post(channelId, output)
        lastBroadcastedAt = now
      }

      if (now - updatedAt.getTime() > 10 * 1000) {
        const didUpdate = await updateChatRequestIfNotComplete(chatRequest.id, async () => ({}))
        if (!didUpdate) {
          return
        }
        updatedAt = new Date(now)
      }
    }

    await broadcastClient.post(channelId, output)

    await updateChatRequestIfNotComplete(chatRequest.id, async () => {
      return { streamingId: null, output }
    })
    logger.info(`Complete process chat request ${chatRequest.id}.`)
  } catch (error) {
    logger.error(
      error,
      `An error happened when streaming chat. Chat request id ${chatRequest.id}. Upstream id ${chatRequest.upstreamRequestId}`,
    )
    await updateChatRequestIfNotComplete(chatRequest.id, async () => ({
      streamingId: null,
      upstreamErrored: true,
    }))
  } finally {
    await broadcastClient.close(channelId)
  }
}

/**
 * Terminates an ongoing chat request.
 *
 * This function attempts to terminate a chat request that is currently in progress.
 * It will update the chat request's status in the database and close the associated
 * broadcast channel if it exists.
 *
 * The function's behavior is as follows:
 * 1. If the chat request is already complete (no streaming ID), it simply marks it as terminated.
 * 2. If the chat request is still streaming, it retrieves the current partial response,
 *    saves it to the database, and then marks the request as terminated.
 * 3. If the termination is successful and a broadcast channel exists, it closes the channel.
 *
 * @param chatRequestId - The ID of the chat request to terminate.
 * @returns A Promise that resolves to a boolean indicating whether the termination was successful.
 *          Returns true if the chat request was terminated, false if it was already complete.
 *
 * @throws Will throw an error if there are issues accessing the database or broadcast client.
 */
export async function terminateChatRequest(chatRequestId: string): Promise<boolean> {
  const broadcastClient = await getBroadcastClient()

  let channelId: string | null = null

  const terminated = await updateChatRequestIfNotComplete(chatRequestId, async (tx, latest) => {
    if (!latest.streamingId) {
      return {
        terminatedByUser: true,
      }
    }

    const result = await broadcastClient.retrieve(latest.streamingId)
    if (!result || result.type === 'complete') {
      return {
        streamingId: null,
        terminatedByUser: true,
      }
    }

    channelId = latest.streamingId

    return {
      output: result.value,
      streamingId: null,
      terminatedByUser: true,
    }
  })

  if (terminated && channelId) {
    broadcastClient.close(channelId)
  }

  return terminated
}

export async function getChatRequestStreamingValue(
  chatRequestId: string,
  streamingId?: string,
  streamingIdIsValid?: boolean,
): Promise<BroadcastChannelValue | undefined> {
  const client = await getPrismaClient()
  const broadcastClient = await getBroadcastClient()
  const clockClient = await getClockClient()

  const chatRequest = await client.peaChatRequest.findUnique({
    where: { id: chatRequestId },
  })

  if (!chatRequest) {
    return undefined
  }

  if (isChatRequestComplete(chatRequest, clockClient.currentTime())) {
    if (chatRequest.output) {
      // We need to be very careful here.
      // Streaming id is removed from the database afterwards.
      // So we can't verify them now.
      // We only use a streaming id if it was previously verified.
      if (streamingId && streamingIdIsValid === true) {
        try {
          const result = await broadcastClient.retrieve(streamingId)
          if (result) {
            return { type: 'complete', value: result.value }
          }
        } catch {
          // ignore this error
        }
      }

      const value = chatRequest.output
      return { type: 'complete', value }
    } else {
      // There is an error. Reset output.
      return { type: 'complete', value: '' }
    }
  }

  if (chatRequest.streamingId) {
    return await broadcastClient.retrieve(chatRequest.streamingId)
  }

  return undefined
}
