import { Prisma } from '@prisma/client'
import { CustomChatInput, CustomChatVersion } from '../models/custom-chat'
import { deepEqual } from '@common/utils'

export async function createOrUpdateCustomChat(
  problemId: string,
  { title, prompt, initialMessage }: CustomChatInput,
  client: Prisma.TransactionClient,
): Promise<{ version: CustomChatVersion; changed: boolean }> {
  const latestRow = await client.peaCustomChat.findFirst({
    where: { problemId, title },
    orderBy: { version: 'desc' },
  })

  // Convert initialMessage to a serialized format for storage
  const serializedInitialMessage = initialMessage.map((msg) => JSON.stringify(msg))

  if (
    latestRow &&
    latestRow.systemPrompt === prompt &&
    deepEqual(latestRow.initialMessage, serializedInitialMessage)
  ) {
    return {
      version: { problemId, title, version: latestRow.version },
      changed: false,
    }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaCustomChat.create({
    data: {
      problemId,
      title,
      version: newVersion,
      systemPrompt: prompt,
      initialMessage: serializedInitialMessage,
    },
  })

  return {
    version: { problemId, title, version: newVersion },
    changed: true,
  }
}

export async function createOrUpdateCustomChatList(
  problemId: string,
  chats: CustomChatInput[],
  client: Prisma.TransactionClient,
): Promise<{ version: number; changed: boolean }> {
  const latestRow = await client.peaCustomChatList.findFirst({
    where: { problemId },
    select: {
      version: true,
      chats: {
        select: { chat: { select: { title: true, version: true } }, order: true },
        orderBy: {
          order: 'asc',
        },
      },
    },
    orderBy: { version: 'desc' },
  })

  const newChats = await Promise.all(
    chats.map((chat) => createOrUpdateCustomChat(problemId, chat, client)),
  )

  const oldChatIdentities = latestRow?.chats.map((x) => [x.chat.title, x.chat.version, x.order])
  const newChatIdentities = newChats.map(({ version }, index) => [
    version.title,
    version.version,
    index,
  ])

  if (latestRow && deepEqual(oldChatIdentities, newChatIdentities)) {
    return { version: latestRow.version, changed: false }
  }

  const newChatsData = newChats.map((chat, index) => ({
    chat: {
      connect: {
        problemId_title_version: {
          problemId: chat.version.problemId,
          title: chat.version.title,
          version: chat.version.version,
        },
      },
    },
    order: index,
  }))

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaCustomChatList.create({
    data: {
      problemId,
      version: newVersion,
      chats: {
        create: newChatsData,
      },
    },
  })

  return { version: newVersion, changed: true }
}
