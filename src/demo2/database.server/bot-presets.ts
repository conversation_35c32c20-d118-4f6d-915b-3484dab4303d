import { Prisma } from '@prisma/client'
import { BotPresetInput, BotPresetItem, BotPresetVersion } from '../models/bot-presets'

export async function createOrUpdateBotPreset(
  { id, title, prompt }: BotPresetInput,
  client: Prisma.TransactionClient,
): Promise<{ version: BotPresetVersion; changed: boolean }> {
  const latestRow = await client.peaBotPreset.findFirst({
    where: { id },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    if (latestRow.title === title && latestRow.prompt === prompt) {
      return { version: { id, version: latestRow.version }, changed: false }
    }
  }

  const version = latestRow ? latestRow.version + 1 : 0

  await client.peaBotPreset.create({
    data: {
      id,
      version,
      title,
      prompt,
    },
  })

  return { version: { id, version }, changed: true }
}

export async function resolveLatestBotPresetVersions(
  ids: string[],
  client: Prisma.TransactionClient,
): Promise<(BotPresetVersion | undefined)[]> {
  const result = await client.peaBotPreset.findMany({
    where: { id: { in: ids } },
    distinct: ['id'],
    orderBy: { version: 'desc' },
    select: {
      id: true,
      version: true,
    },
  })

  return ids.map((id) => {
    const row = result.find((x) => x.id === id)
    if (!row) {
      return undefined
    }
    return {
      id: row.id,
      version: row.version,
    }
  })
}

export async function getBotPresetItem(
  version: BotPresetVersion,
  client: Prisma.TransactionClient,
): Promise<BotPresetItem | undefined> {
  const row = await client.peaBotPreset.findFirst({
    where: version,
    select: {
      title: true,
    },
  })
  if (!row) {
    return
  }
  return { ...version, title: row.title }
}

export async function getBotPresetPrompt(
  version: BotPresetVersion,
  client: Prisma.TransactionClient,
): Promise<string | undefined> {
  const row = await client.peaBotPreset.findFirst({
    where: version,
  })
  if (!row) {
    return
  }
  return row.prompt
}
