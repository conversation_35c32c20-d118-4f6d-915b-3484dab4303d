import { PeaProblemProgress, Prisma, PrismaClient } from '@prisma/client'
import { ProblemVersion } from '../models/problem'
import { resolveLatestBotPresetVersions } from './bot-presets'
import { BotPresetVersion, ResolvedBotPreset } from '../models/bot-presets'
import { sha256ForS3Text } from './s3text'
import { renderNunjucksTemplate } from '../models/nunjucks'
import getClockClient from '../deps.server/clock-client'
import getPrismaClient from '@deps/prisma-client'
import invariant from 'tiny-invariant'
import { assertWithHttpError } from '@common/utils'
import logger from '@deps/logger'
import { ChatMessage } from '../models/custom-chat'

async function resolveDynamicProblem(
  problemIds: string[],
  tx: Prisma.TransactionClient,
): Promise<ProblemVersion[]> {
  const latestProblemVersions = await tx.peaProblem.findMany({
    where: {
      id: { in: problemIds },
    },
    orderBy: {
      version: 'desc',
    },
    distinct: ['id'],
    select: { id: true, version: true },
  })

  // Ensure we found a version for each problem ID
  if (latestProblemVersions.length !== problemIds.length) {
    const foundIds = new Set(latestProblemVersions.map((p) => p.id))
    const missingIds = problemIds.filter((id) => !foundIds.has(id))
    throw new Error(`No problems found for IDs: ${missingIds.join(', ')}`)
  }

  // Sort latestProblemVersions to match the order of problemIds
  return problemIds.map((id) => {
    const version = latestProblemVersions.find((v) => v.id === id)
    invariant(version)
    return version
  })
}

export async function createTestFromDynamicProblems(
  problemIds: string[],
  client: PrismaClient,
): Promise<string> {
  return client.$transaction(
    async (tx) => {
      const problemVersions = await resolveDynamicProblem(problemIds, tx)
      return createTest(problemVersions, tx)
    },
    {
      isolationLevel: Prisma.TransactionIsolationLevel.RepeatableRead,
    },
  )
}

export async function createProblemSetFromDynamicProblems(
  problemIds: string[],
  client: PrismaClient,
  expireMinutes?: number,
): Promise<string> {
  return client.$transaction(
    async (tx) => {
      const problemVersions = await resolveDynamicProblem(problemIds, tx)
      const problems = await createResolvedProblems(problemVersions, client)
      const row = await tx.peaProblemSet.create({
        data: {
          problems,
          expireMinutes,
        },
      })
      return row.id
    },
    {
      isolationLevel: Prisma.TransactionIsolationLevel.RepeatableRead,
    },
  )
}

export async function createTestFromProblemSet(
  problemSetId: string,
  client: PrismaClient,
): Promise<string> {
  return client.$transaction(
    async (tx) => {
      const problemSet = await tx.peaProblemSet.findUniqueOrThrow({
        where: {
          id: problemSetId,
        },
      })
      const test = await client.peaTest.create({
        data: {
          isComplete: problemSet.problems.length === 0,
          problems: problemSet.problems,
          problemSetId,
        },
      })
      return test.id
    },
    {
      isolationLevel: Prisma.TransactionIsolationLevel.RepeatableRead,
    },
  )
}

async function createResolvedProblems(
  problemVersions: ProblemVersion[],
  client: Prisma.TransactionClient,
): Promise<PrismaJson.PeajTestProblem[]> {
  const resolvedProblems = await Promise.all(
    problemVersions.map(async (problem) => {
      const resolvedPresets = await resolveBotPresets(problem, client)
      return {
        problemId: problem.id,
        problemVersion: problem.version,
        resolvedPresets,
      }
    }),
  )

  const ids = await client.peaResolvedProblem.createManyAndReturn({
    select: { id: true },
    data: resolvedProblems,
  })

  return resolvedProblems.map(
    (rp, i) =>
      ({
        problem_id: rp.problemId,
        problem_version: rp.problemVersion,
        resolved_problem_id: ids[i].id,
        progress_id: null,
      }) satisfies PrismaJson.PeajTestProblem,
  )
}

export async function createTest(
  problemVersions: ProblemVersion[],
  client: Prisma.TransactionClient,
): Promise<string> {
  const problems = await createResolvedProblems(problemVersions, client)
  const test = await client.peaTest.create({
    data: {
      isComplete: problemVersions.length === 0,
      problems,
    },
  })

  return test.id
}

async function resolveBotPresets(
  problemVersion: ProblemVersion,
  client: Prisma.TransactionClient,
): Promise<PrismaJson.PeajResolvedBotPresetList> {
  // let's implement this function step by step
  const {
    botPresetList: { presets: presetIds },
  } = await client.peaProblem.findFirstOrThrow({
    where: problemVersion,
    select: {
      botPresetList: {
        select: {
          presets: true,
        },
      },
    },
  })
  const resolvedVersions = await resolveLatestBotPresetVersions(presetIds, client).then((list) =>
    list.filter((version): version is BotPresetVersion => version !== undefined),
  )

  return resolvedVersions.map((x) => ({
    id: x.id,
    version: x.version,
  }))
}

export async function loadResolvedPresets(
  resolvedProblemId: string,
  client: Prisma.TransactionClient,
): Promise<ResolvedBotPreset[]> {
  const { resolvedPresets } = await client.peaResolvedProblem.findUniqueOrThrow({
    where: { id: resolvedProblemId },
  })
  const rows = await client.peaBotPreset.findMany({
    where: {
      OR: resolvedPresets,
    },
  })
  return resolvedPresets.map(({ id }) => {
    const match = rows.find((x) => x.id === id)
    invariant(match)
    return match
  })
}

async function createProblemProgress(
  testId: string,
  resolvedProblemId: string,
  client: Prisma.TransactionClient,
): Promise<PeaProblemProgress> {
  // 1. Get axiia metadata and submission spec
  const resolvedProblem = await client.peaResolvedProblem.findUniqueOrThrow({
    where: { id: resolvedProblemId },
    include: {
      problem: {
        include: {
          axiiaMetadata: true,
          submissionSpec: true,
          customChatList: {
            include: {
              chats: {
                include: {
                  chat: true,
                },
                orderBy: {
                  order: 'asc',
                },
              },
            },
          },
        },
      },
    },
  })

  const { axiiaMetadata, submissionSpec, customChatList } = resolvedProblem.problem

  // 2. Create a chat with prompt from axiia metadata
  const prompt = renderNunjucksTemplate(axiiaMetadata.prompt, {
    [axiiaMetadata.promptReplacementKey]: axiiaMetadata.initialMessage,
  })
  const clockClient = await getClockClient()

  const chat = await client.peaChat.create({
    data: {
      createdAt: clockClient.currentTime(),
      systemPrompt: prompt,
    },
  })

  // 3. Create a PeaProblemAxiiaChat wrapping that chat
  const axiiaChat = await client.peaProblemAxiiaChat.create({
    data: { id: chat.id },
  })

  // 4. Create the PeaProblemProgress
  const progress = await client.peaProblemProgress.create({
    data: {
      createdAt: clockClient.currentTime(),
      testId,
      resolvedProblemId,
      axiiaChatId: axiiaChat.id,
    },
  })

  // 5. Create custom chats if they exist
  if (customChatList) {
    await Promise.all(
      customChatList.chats.map(async (chatMembership) => {
        const customChat = chatMembership.chat
        const customChatInstance = await client.peaChat.create({
          data: {
            createdAt: clockClient.currentTime(),
            systemPrompt: customChat.systemPrompt,
          },
        })

        await client.peaProblemCustomChat.create({
          data: {
            id: customChatInstance.id,
            progressId: progress.id,
            title: customChat.title,
            sourcePresetId: 'problem-config',
          },
        })

        if (customChat.initialMessage && customChat.initialMessage.length > 0) {
          for (let i = 0; i < customChat.initialMessage.length; i += 2) {
            // Skip if we don't have enough messages for a complete pair
            if (i + 1 >= customChat.initialMessage.length) {
              break
            }

            const userMessage: ChatMessage = JSON.parse(customChat.initialMessage[i])
            const assistantMessage: ChatMessage = JSON.parse(customChat.initialMessage[i + 1])

            // Skip if not a valid user+assistant pair
            if (userMessage.role !== 'user' || assistantMessage.role !== 'assistant') {
              continue
            }

            await client.peaChatRequest.create({
              data: {
                chatId: customChatInstance.id,
                createdAt: clockClient.currentTime(),
                updatedAt: clockClient.currentTime(),
                input: userMessage.content,
                output: assistantMessage.content,
              },
            })
          }
        }
      }),
    )
  }

  // 6. Create a submission snapshot using submission spec's prefill
  const sha256 = await sha256ForS3Text(submissionSpec.prefill)
  const textRow = await client.peaSubmissionTexts.create({
    data: {
      sha256,
      text: submissionSpec.prefill,
      progressId: progress.id,
    },
  })
  await client.peaSubmissionSnapshot.create({
    data: {
      isValid: true,
      timestamp: clockClient.currentTime(),
      progressId: progress.id,
      textId: textRow.id,
    },
  })

  return progress
}

async function createProblemProgressIfNeeded(testId: string, n: number): Promise<string> {
  let i = 0
  const client = await getPrismaClient()

  while (true) {
    try {
      const progressId = await client.$transaction(
        async (tx) => {
          const test = await tx.peaTest.findUniqueOrThrow({
            where: { id: testId },
            include: { problemSet: true },
          })
          if (test.problems[n].progress_id) {
            return test.problems[n].progress_id
          }
          if (test.isComplete) {
            throw new Error('Cannot modify a test after it is complete.')
          }
          const progress = await createProblemProgress(
            testId,
            test.problems[n].resolved_problem_id,
            tx,
          )
          const problems = JSON.parse(JSON.stringify(test.problems))
          problems[n].progress_id = progress.id
          await tx.peaTest.update({
            where: {
              id: testId,
            },
            data: {
              problems,
            },
          })

          return progress.id
        },
        {
          isolationLevel: 'Serializable',
        },
      )
      return progressId
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2034') {
        logger.info(
          `A conflict occurred when creating progress for ${testId}:${n}. This is generally safe to ignore`,
        )
        i += 1
        if (i >= 5) {
          logger.error(`Failed to create progress for ${testId}:${n} for too many times. Bug?`)
          throw error
        }
        continue
      }
      throw error
    }
  }
}

export async function getProblemProgressId(testId: string, n: number): Promise<string> {
  const client = await getPrismaClient()
  const test = await client.peaTest.findUniqueOrThrow({
    where: { id: testId },
  })
  assertWithHttpError(n >= 0 && n < test.problems.length, 404)
  if (test.problems[n].progress_id) {
    return test.problems[n].progress_id
  }
  return await createProblemProgressIfNeeded(testId, n)
}

export async function endTest(testId: string) {
  const client = await getPrismaClient()
  await client.peaTest.update({
    where: { id: testId },
    data: {
      isComplete: true,
    },
  })
}
