import getPrismaClient from '@deps/prisma-client'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DynamicChatRequest,
  DynamicCustomChat,
  FeedbackDynamicContent,
  LabelPredGroupListDynamicContent,
  LabelPredPairDynamicContent,
  ProblemSubmissions,
  ProblemFinalConfirmContent,
  ProblemDynamicContent,
} from '../models/test-dynamic-content'
import { loadChat, loadChatRequestContent } from './chat'
import invariant from 'tiny-invariant'
import { assertWithHttpError } from '@common/utils'
import { getLatestSubmission } from './problem-progress'
import { PeaLabelPredictionPair } from '@prisma/client'
import { renderNunjucksTemplate } from '../models/nunjucks'
import getLLMClient from '../deps.server/llm-client'
import logger from '@deps/logger'
import { extractFields } from './feedback'
import { getProblemProgressId } from './test'
import { TestProblemIndex } from '../components/common'

export async function getLabelPredGroupListVersion(progressId: string): Promise<{
  problemId: string
  version: number
}> {
  const client = await getPrismaClient()
  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: { id: progressId },
    select: {
      resolvedProblem: {
        select: {
          problem: {
            select: {
              id: true,
              vLabelPredictionGroupList: true,
            },
          },
        },
      },
    },
  })

  const { problem } = progress.resolvedProblem
  invariant(problem.vLabelPredictionGroupList !== null)

  return {
    problemId: problem.id,
    version: problem.vLabelPredictionGroupList,
  }
}

async function loadLabelPredGroupList(
  progressId: string,
): Promise<LabelPredGroupListDynamicContent> {
  const client = await getPrismaClient()
  const allTests = await client.peaLabelPredictionPairTest.findMany({
    where: { progressId },
    include: {
      snapshot: {
        select: {
          textId: true,
        },
      },
      pair: true,
    },
    distinct: ['pairId'],
    orderBy: { timestamp: 'desc' },
  })

  if (allTests.length === 0) {
    return { byGroup: {}, texts: {} }
  }

  const vLabelPredictionGroupList = await getLabelPredGroupListVersion(progressId)
  const labelPredictionGroupList = await client.peaLabelPredictionGroupList.findUniqueOrThrow({
    where: {
      problemId_version: vLabelPredictionGroupList,
    },
    select: {
      groups: {
        select: {
          group: true,
        },
      },
    },
  })

  const groupsVisible = new Map<string, boolean>()
  for (const { group } of labelPredictionGroupList.groups) {
    if (!group.hidesInput) {
      groupsVisible.set(group.name, true)
    }
  }

  const byGroup: Record<string, Record<string, LabelPredPairDynamicContent>> = {}
  const usedTextIds = new Set<string>()

  for (const test of allTests) {
    const groupName = test.pair.groupName
    const pairName = test.pair.name
    const hidesInput = !groupsVisible.get(groupName)

    let dict = byGroup[groupName]
    if (!dict) {
      dict = {}
      byGroup[groupName] = dict
    }

    const content: LabelPredPairDynamicContent = {
      name: pairName,
      timestamp: test.timestamp.getTime(),
      hidesInput,
      result: 'errored',
      extractedOutput: test.extractedOutput === null ? undefined : test.extractedOutput,
      textId: test.snapshot.textId,
    }

    if (content.extractedOutput === test.pair.output) {
      content.result = 'match'
    } else if (content.extractedOutput) {
      content.result = 'mismatch'
    } else if (test.output) {
      content.result = 'no-value-extracted'
    }

    if (!hidesInput && test.output) {
      content.output = test.output
    }

    usedTextIds.add(test.snapshot.textId)
    dict[pairName] = content
  }

  const textRows = await client.peaSubmissionTexts.findMany({
    where: {
      id: {
        in: Array.from(usedTextIds),
      },
    },
  })

  const texts: Record<string, string> = {}
  textRows.forEach((row) => {
    texts[row.id] = row.text
  })

  return {
    byGroup,
    texts,
  }
}

export async function runLabelPredTask(
  { testId, n }: TestProblemIndex,
  groupName: string,
  pairNames: string[],
): Promise<void> {
  const client = await getPrismaClient()
  const progressId = await getProblemProgressId(testId, n)

  const submissionRow = await getLatestSubmission(progressId, client)
  invariant(submissionRow)
  const snapshotId = submissionRow.id
  const submission = submissionRow.content

  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: { id: progressId },
    include: {
      resolvedProblem: {
        include: {
          problem: {
            include: {
              submissionSpec: true,
            },
          },
        },
      },
    },
  })

  const submissionSpec = progress.resolvedProblem.problem.submissionSpec
  if (submissionSpec.maxLength !== null && submission.length > submissionSpec.maxLength) {
    throw new Error('Invalid submission.')
  }
  for (const phrase of submissionSpec.requiredPhrases) {
    if (!submission.includes(phrase)) {
      throw new Error('Invalid submission.')
    }
  }

  const vLabelPredictionGroupList = progress.resolvedProblem.problem.vLabelPredictionGroupList
  invariant(vLabelPredictionGroupList !== null)
  const labelPredictionGroupList = await client.peaLabelPredictionGroupList.findUniqueOrThrow({
    where: {
      problemId_version: {
        problemId: progress.resolvedProblem.problem.id,
        version: vLabelPredictionGroupList,
      },
    },
    select: {
      groups: {
        select: {
          group: {
            select: {
              pairs: {
                select: {
                  pair: true,
                },
              },
            },
          },
        },
        where: {
          groupName,
        },
      },
    },
  })

  assertWithHttpError(labelPredictionGroupList.groups.length > 0, 404)
  const { group } = labelPredictionGroupList.groups[0]
  const pairs = group.pairs.map((x) => x.pair)

  const usedPairs: PeaLabelPredictionPair[] = []

  for (const pairName of pairNames) {
    const target = pairs.find((x) => x.name === pairName)
    assertWithHttpError(target, 404)
    usedPairs.push(target)
  }

  const llmClient = await getLLMClient()

  async function runOnPair(pair: PeaLabelPredictionPair) {
    const input = pair.input
    const renderedInput = renderNunjucksTemplate(submission, { input })

    let upstreamRequestId: string | null = null
    let output: string | null = null
    let extractedOutput: string | null = null

    try {
      const result = await llmClient.generateText({
        input: renderedInput,
        model: 'gpt-4o-mini',
      })

      upstreamRequestId = result.requestId
      output = result.output

      const extraction = extractFields(result.output, ['result'])

      if (extraction.status === 'ok') {
        extractedOutput = extraction.dict['result']
      }
    } catch (error) {
      logger.error(error, 'An error occurred when running label prediction task.')
    }

    await client.peaLabelPredictionPairTest.create({
      data: {
        progressId,
        snapshotId,
        pairId: pair.id,
        upstreamRequestId,
        output,
        extractedOutput,
        isCorrect: extractedOutput === pair.output,
      },
    })
  }

  await Promise.all(usedPairs.map(runOnPair))
}

async function loadDynamicChat(chatId: string): Promise<DynamicChat> {
  const client = await getPrismaClient()
  const chat = await loadChat(chatId, client)
  invariant(chat)

  const requests = await Promise.all(
    chat.chatRequests.map(async (x): Promise<DynamicChatRequest> => {
      const content = await loadChatRequestContent(x.id, client)
      invariant(content)
      const base = {
        id: x.id,
        input: content.input,
        timestamp: x.updatedAt.getTime(),
      }

      switch (content.type) {
        case 'complete':
          return {
            ...base,
            type: 'complete',
            output: content.output,
          }
        case 'errored':
          return {
            ...base,
            type: 'errored',
          }
        case 'in-progress':
          return {
            ...base,
            type: 'in-progress',
            streamingId: content.streamingId,
          }
      }
    }),
  )
  const createdAt = chat.createdAt.getTime()
  const lastUpdatedAt = Math.max(createdAt, ...requests.map((x) => x.timestamp))
  return {
    id: chatId,
    createdAt,
    lastUpdatedAt,
    requests,
  }
}

export async function loadFeedbackDynamicContent(
  progressId: string,
): Promise<FeedbackDynamicContent | undefined> {
  const client = await getPrismaClient()
  const snapshot = await client.peaSubmissionSnapshot.findFirstOrThrow({
    where: { progressId },
    orderBy: { timestamp: 'desc' },
    include: {
      text: true,
      feedback: {
        include: {
          feedbackMemberships: {
            select: {
              run: {
                select: {
                  createdAt: true,
                  renderedResult: true,
                },
              },
            },
          },
        },
      },
    },
  })

  if (!snapshot.feedback) {
    return undefined
  }

  const runs = snapshot.feedback.feedbackMemberships.map((x) => x.run)
  runs.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

  if (runs.length === 0) {
    return undefined
  }

  const submission = snapshot.text.text

  if (!runs[0].renderedResult) {
    return {
      submission,
      errored: true,
    }
  }
  return {
    submission,
    errored: false,
    feedback: runs[0].renderedResult,
  }
}

export async function loadLatestSubmission(progressId: string): Promise<string> {
  const client = await getPrismaClient()
  const row = await client.peaSubmissionSnapshot.findFirstOrThrow({
    where: { progressId },
    orderBy: {
      timestamp: 'desc',
    },
    select: {
      text: {
        select: {
          text: true,
        },
      },
    },
  })
  return row.text.text
}

export async function loadCustomChats(progressId: string): Promise<DynamicCustomChat[]> {
  const client = await getPrismaClient()

  const customChatRows = await client.peaProblemCustomChat.findMany({
    where: { progressId },
  })

  return await Promise.all(
    customChatRows.map(async (x) => {
      const chat = await loadDynamicChat(x.id)
      return { ...chat, title: x.title, sourcePresetId: x.sourcePresetId || undefined }
    }),
  )
}

export async function loadAllProblemSubmissions(testId: string): Promise<ProblemSubmissions[]> {
  const client = await getPrismaClient()
  const problemProgresses = await client.peaProblemProgress.findMany({
    where: { testId },
    include: {
      resolvedProblem: {
        select: {
          problemId: true,
        },
      },
      submissionSnapshots: {
        select: {
          id: true,
          timestamp: true,
          text: {
            select: {
              text: true,
            },
          },
        },
        orderBy: {
          timestamp: 'desc',
        },
      },
    },
  })

  return problemProgresses.map((progress) => ({
    problemId: progress.resolvedProblem.problemId,
    submissions: progress.submissionSnapshots.map((snapshot) => ({
      id: snapshot.id,
      timestamp: snapshot.timestamp.getTime(),
      content: snapshot.text.text,
    })),
  }))
}

export async function loadTestDynamicEvaluationContent(
  testId: string,
): Promise<ProblemFinalConfirmContent[]> {
  const client = await getPrismaClient()
  const test = await client.peaTest.findUniqueOrThrow({
    where: {
      id: testId,
    },
  })

  return Promise.all(
    test.problems.map(async ({ problem_id, progress_id }) => {
      if (!progress_id) {
        return { problemId: problem_id }
      }

      const submissionCount = await client.peaSubmissionSnapshot.count({
        where: { progressId: progress_id },
      })
      const hasSubmitted = submissionCount > 1
      const [feedback, labelPredGroupList] = await Promise.all([
        loadFeedbackDynamicContent(progress_id),
        loadLabelPredGroupList(progress_id),
      ])

      const isLabelPredGroupListEmpty = Object.keys(labelPredGroupList.texts).length === 0

      return {
        problemId: problem_id,
        hasSubmitted,
        progressId: progress_id,
        feedback,
        labelPredGroupList: isLabelPredGroupListEmpty ? undefined : labelPredGroupList,
      }
    }),
  )
}

export async function loadProblemDynamicContent({
  testId,
  n,
}: TestProblemIndex): Promise<ProblemDynamicContent> {
  const client = await getPrismaClient()
  const progressId = await getProblemProgressId(testId, n)
  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: {
      id: progressId,
    },
  })

  const [
    axiiaChat,
    customChats,
    latestSubmission,
    feedback,
    labelPredGroupList,
    allProblemSubmissions,
    allProblemFinalConfirm,
  ] = await Promise.all([
    loadDynamicChat(progress.axiiaChatId),
    loadCustomChats(progress.id),
    loadLatestSubmission(progress.id),
    loadFeedbackDynamicContent(progress.id),
    loadLabelPredGroupList(progress.id),
    loadAllProblemSubmissions(testId),
    loadTestDynamicEvaluationContent(testId),
  ])

  return {
    progressId,
    axiiaChat,
    customChats,
    feedback,
    labelPredGroupList,
    latestSubmission,
    allProblemSubmissions,
    allProblemFinalConfirm,
  }
}
