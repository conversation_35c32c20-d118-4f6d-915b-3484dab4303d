import { <PERSON>rism<PERSON>, PrismaClient } from '@prisma/client'
import { CustomChatRequest, SubmissionUploadResult } from '../models/problem-progress'
import { getProblemProgressId } from './test'
import { sha256ForS3Text } from './s3text'
import invariant from 'tiny-invariant'
import logger from '@common/deps.server/logger'
import getClockClient from '../deps.server/clock-client'
import { createChat, createChatRequest } from './chat'
import { TestProblemIndex } from '../components/common'
import getPrismaClient from '@deps/prisma-client'

export async function createCustomChat(
  { testId, n }: TestProblemIndex,
  request: CustomChatRequest,
) {
  const client = await getPrismaClient()
  // 1. Fetch the progress and check if it's the current progress in the test
  const progressId = await getProblemProgressId(testId, n)
  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: { id: progressId },
    include: {
      test: true,
      resolvedProblem: true,
    },
  })

  let systemPrompt: string
  if (request.prompt) {
    systemPrompt = request.prompt
  } else {
    const resolvedPreset = progress.resolvedProblem.resolvedPresets.find(
      (x) => x.id == request.sourcePresetId,
    )
    invariant(resolvedPreset)
    const row = await client.peaBotPreset.findUniqueOrThrow({
      where: {
        id_version: resolvedPreset,
      },
    })
    systemPrompt = row.prompt
  }

  // Create the chat
  const chatId = await createChat(systemPrompt, client)

  // Create the custom chat
  const customChat = await client.peaProblemCustomChat.create({
    data: {
      id: chatId,
      progressId,
      title: request.title,
      sourcePresetId: request.sourcePresetId,
    },
  })

  return customChat
}

export async function createChatRequestForProblem(
  { testId, n }: TestProblemIndex,
  chatId: string,
  input: string,
): Promise<void> {
  const client = await getPrismaClient()
  const progressId = await getProblemProgressId(testId, n)
  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: { id: progressId },
  })

  if (progress.axiiaChatId == chatId) {
    await createChatRequest(chatId, input, client)
    return
  }

  // Ensure this chat belongs to this problem.
  await client.peaProblemCustomChat.findFirstOrThrow({
    where: {
      progressId,
      id: chatId,
    },
  })
  await createChatRequest(chatId, input, client)
}

export async function renameCustomChat(
  { testId, n }: TestProblemIndex,
  chatId: string,
  newTitle: string,
): Promise<void> {
  const client = await getPrismaClient()
  const progressId = await getProblemProgressId(testId, n)
  await client.peaProblemCustomChat.update({
    where: {
      progressId,
      id: chatId,
    },
    data: {
      title: newTitle,
    },
  })
}

async function _uploadSubmission(
  { testId, n }: TestProblemIndex,
  submission: string,
  client: Prisma.TransactionClient,
): Promise<SubmissionUploadResult> {
  const progressId = await getProblemProgressId(testId, n)

  // Calculate sha256 and find existing snapshots
  const sha256 = await sha256ForS3Text(submission)
  const existingTextRow = await client.peaSubmissionTexts.findFirst({
    where: {
      progressId,
      sha256,
    },
    select: {
      id: true,
    },
  })
  const latestSnapshot = await client.peaSubmissionSnapshot.findFirst({
    where: {
      progressId,
    },
    orderBy: {
      timestamp: 'desc',
    },
  })

  // Check for matches and create new snapshot if necessary
  let snapshotId: string
  let changed: boolean

  const clockClient = await getClockClient()
  const now = clockClient.currentTime()

  if (existingTextRow && latestSnapshot) {
    if (latestSnapshot.textId === existingTextRow.id) {
      // Matching snapshot is the latest
      snapshotId = latestSnapshot.id
      changed = false
    } else {
      // Matching snapshot exists but is not the latest
      const newSnapshot = await client.peaSubmissionSnapshot.create({
        data: {
          timestamp: now,
          progressId,
          textId: existingTextRow.id,
          isValid: true,
        },
      })
      snapshotId = newSnapshot.id
      changed = true
    }
  } else {
    // No matching snapshot, create new text and snapshot
    const textRow = await client.peaSubmissionTexts.create({
      data: {
        sha256,
        text: submission,
        progressId,
      },
    })
    const newSnapshot = await client.peaSubmissionSnapshot.create({
      data: {
        timestamp: now,
        progressId,
        textId: textRow.id,
        isValid: true,
      },
    })
    snapshotId = newSnapshot.id
    changed = true
  }

  return {
    id: snapshotId,
    timestamp: now.getTime(),
    isValid: true,
    sha256: Buffer.from(sha256).toString('hex'),
    changed,
  }
}

export async function uploadSubmission(
  testProblemIndex: TestProblemIndex,
  submission: string,
): Promise<SubmissionUploadResult> {
  const client = await getPrismaClient()

  let i = 0
  while (true) {
    try {
      return await client.$transaction(
        async (tx) => _uploadSubmission(testProblemIndex, submission, tx),
        {
          isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
        },
      )
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2034') {
        if (i >= 10) {
          logger.error(
            `We have retried 10 times to create a submission due to serialization error. Something must be wrong.`,
          )
          throw error
        }

        logger.info(
          'A conflict occurred when creating a submission. This is generally safe to ignore',
        )
        i += 1
        continue
      }
      throw error
    }
  }
}

export async function loadSubmission(
  progressId: string,
  client: PrismaClient,
): Promise<{
  id: string
  timestamp: number
  isValid: boolean
  text: string
  hasFeedback: boolean
} | null> {
  const latestSnapshot = await client.peaSubmissionSnapshot.findFirstOrThrow({
    where: { progressId },
    orderBy: { timestamp: 'desc' },
    include: {
      text: true,
      progress: {
        include: {
          resolvedProblem: {
            include: {
              problem: true,
            },
          },
        },
      },
    },
  })

  const hasFeedback = latestSnapshot.progress.resolvedProblem.problem.vFeedbackSpec !== null

  return {
    id: latestSnapshot.id,
    timestamp: latestSnapshot.timestamp.getTime(),
    isValid: latestSnapshot.isValid,
    text: latestSnapshot.text.text,
    hasFeedback,
  }
}

export async function getLatestSubmission(progressId: string, client: PrismaClient) {
  const latestSnapshot = await client.peaSubmissionSnapshot.findFirst({
    where: { progressId },
    orderBy: { timestamp: 'desc' },
    include: {
      text: true,
    },
  })

  if (!latestSnapshot) {
    return null
  }

  return {
    id: latestSnapshot.id,
    timestamp: latestSnapshot.timestamp.getTime(),
    isValid: latestSnapshot.isValid,
    content: latestSnapshot.text.text,
  }
}

export async function resetProblemConfigChat(
  { testId, n }: TestProblemIndex,
  chatId: string,
): Promise<void> {
  const client = await getPrismaClient()
  const progressId = await getProblemProgressId(testId, n)

  // Verify the chat is a custom chat associated with this progress
  const customChat = await client.peaProblemCustomChat.findUnique({
    where: {
      id: chatId,
      progressId,
    },
    include: {
      chat: true,
    },
  })

  if (!customChat || customChat.sourcePresetId !== 'problem-config') {
    throw new Error('Cannot reset a chat that is not a problem-configured custom chat')
  }

  // Find the problem and resolved problem for this chat
  const progress = await client.peaProblemProgress.findUniqueOrThrow({
    where: { id: progressId },
    include: {
      resolvedProblem: {
        include: {
          problem: {
            include: {
              customChatList: {
                include: {
                  chats: {
                    include: {
                      chat: true,
                    },
                    orderBy: {
                      order: 'asc',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  })

  // Find the matching custom chat from the problem configuration
  const matchingCustomChat = progress.resolvedProblem.problem.customChatList?.chats.find(
    (chatMembership) => chatMembership.chat.title === customChat.title,
  )

  if (!matchingCustomChat) {
    throw new Error('Could not find matching custom chat in problem configuration')
  }

  const clockClient = await getClockClient()

  // Delete all existing chat requests
  await client.peaChatRequest.deleteMany({
    where: { chatId },
  })

  // Re-add initial messages if they exist
  if (matchingCustomChat.chat.initialMessage && matchingCustomChat.chat.initialMessage.length > 0) {
    for (let i = 0; i < matchingCustomChat.chat.initialMessage.length; i += 2) {
      if (i + 1 >= matchingCustomChat.chat.initialMessage.length) {
        break
      }

      const userMessage = JSON.parse(matchingCustomChat.chat.initialMessage[i])
      const assistantMessage = JSON.parse(matchingCustomChat.chat.initialMessage[i + 1])

      if (userMessage.role !== 'user' || assistantMessage.role !== 'assistant') {
        continue
      }

      await client.peaChatRequest.create({
        data: {
          chatId,
          createdAt: clockClient.currentTime(),
          updatedAt: clockClient.currentTime(),
          input: userMessage.content,
          output: assistantMessage.content,
        },
      })
    }
  }
}
