import { Prisma } from '@prisma/client'
import { MaterialInput, MaterialItem, MaterialVersion } from '../models/materials'
import { ProblemVersion } from '../models/problem'

export async function createOrUpdateMaterial(
  { problemId, name, title, content, copyable: copyableInput }: MaterialInput,
  client: Prisma.TransactionClient,
): Promise<{ version: MaterialVersion; changed: boolean }> {
  const copyable = copyableInput === undefined ? false : copyableInput

  const latestRow = await client.peaMaterial.findFirst({
    where: { problemId, name },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    if (
      latestRow.title === title &&
      latestRow.content === content &&
      latestRow.copyable === copyable
    ) {
      return { version: { problemId, name, version: latestRow.version }, changed: false }
    }
  }

  const version = latestRow ? latestRow.version + 1 : 0

  await client.peaMaterial.create({
    data: {
      problemId,
      name,
      version,
      title,
      content,
      copyable,
    },
  })

  return { version: { problemId, name, version }, changed: true }
}

export async function resolveLatestMaterialVersions(
  materials: { problemId: string; name: string }[],
  client: Prisma.TransactionClient,
): Promise<(MaterialVersion | undefined)[]> {
  const result = await client.peaMaterial.findMany({
    where: {
      OR: materials.map(({ problemId, name }) => ({ problemId, name })),
    },
    distinct: ['problemId', 'name'],
    orderBy: { version: 'desc' },
    select: {
      problemId: true,
      name: true,
      version: true,
    },
  })

  return materials.map(({ problemId, name }) => {
    const row = result.find((x) => x.problemId === problemId && x.name === name)
    if (!row) {
      return undefined
    }
    return {
      problemId: row.problemId,
      name: row.name,
      version: row.version,
    }
  })
}

export async function getMaterialItem(
  version: MaterialVersion,
  client: Prisma.TransactionClient,
): Promise<MaterialItem | undefined> {
  const row = await client.peaMaterial.findFirst({
    where: version,
    select: { title: true, copyable: true },
  })
  if (!row) return undefined
  return { ...version, title: row.title, copyable: row.copyable }
}

export async function getMaterialContent(
  version: MaterialVersion,
  client: Prisma.TransactionClient,
): Promise<string | undefined> {
  const row = await client.peaMaterial.findFirst({
    where: version,
  })
  if (!row) return undefined
  return row.content
}

export async function loadMaterialItems(
  problemVersion: ProblemVersion,
  client: Prisma.TransactionClient,
): Promise<MaterialItem[]> {
  const problem = await client.peaProblem.findUniqueOrThrow({
    where: { id_version: problemVersion },
    select: {
      materialList: {
        select: {
          materials: {
            include: {
              material: {
                select: {
                  title: true,
                  copyable: true,
                },
              },
            },
            orderBy: {
              order: 'asc',
            },
          },
        },
      },
    },
  })

  return problem.materialList.materials.map((x) => ({
    problemId: problemVersion.id,
    name: x.materialName,
    version: x.materialVersion,
    title: x.material.title,
    copyable: x.material.copyable,
  }))
}
