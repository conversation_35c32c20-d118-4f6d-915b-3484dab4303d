import { Prisma } from '@prisma/client'
import {
  SubmissionSpecInput,
  SubmissionSpec,
  SubmissionSpecVersion,
} from '../models/submission-spec'
import { deepEqual } from '@common/utils'

export async function createOrUpdateSubmissionSpec(
  { problemId, requiredPhrases, maxLength, prefill }: SubmissionSpecInput,
  client: Prisma.TransactionClient,
): Promise<{ version: SubmissionSpecVersion; changed: boolean }> {
  const latestRow = await client.peaSubmissionSpec.findFirst({
    where: { problemId },
    select: { version: true, requiredPhrases: true, maxLength: true, prefill: true },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    const normalizedMaxLength = maxLength === undefined ? null : maxLength

    if (
      latestRow.prefill === prefill &&
      deepEqual(latestRow.requiredPhrases, requiredPhrases) &&
      latestRow.maxLength === normalizedMaxLength
    ) {
      return { version: { problemId, version: latestRow.version }, changed: false }
    }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaSubmissionSpec.create({
    data: {
      problemId,
      version: newVersion,
      requiredPhrases,
      maxLength,
      prefill,
    },
  })

  return { version: { problemId, version: newVersion }, changed: true }
}

export async function resolveLatestSubmissionSpecVersion(
  problemId: string,
  client: Prisma.TransactionClient,
): Promise<SubmissionSpecVersion | undefined> {
  const row = await client.peaSubmissionSpec.findFirst({
    where: { problemId },
    select: { version: true },
    orderBy: { version: 'desc' },
  })
  if (row === null) {
    return undefined
  }
  return { problemId, version: row.version }
}

export async function getSubmissionSpec(
  version: SubmissionSpecVersion,
  client: Prisma.TransactionClient,
): Promise<SubmissionSpec | undefined> {
  const row = await client.peaSubmissionSpec.findFirst({
    where: version,
    select: {
      requiredPhrases: true,
      maxLength: true,
      prefill: true,
    },
  })
  if (!row) {
    return
  }

  return {
    ...version,
    requiredPhrases: row.requiredPhrases,
    maxLength: row.maxLength,
    prefill: row.prefill,
  }
}
