import { Prisma } from '@prisma/client'
import { AxiiaMetadataInput, AxiiaMetadata, AxiiaMetadataVersion } from '../models/axiia-metadata'
import invariant from 'tiny-invariant'

export async function createOrUpdateAxiiaMetadata(
  { problemId, initialMessage, prompt }: AxiiaMetadataInput,
  client: Prisma.TransactionClient,
): Promise<{ version: AxiiaMetadataVersion; changed: boolean }> {
  invariant(prompt.replacementKeys.length === 1)

  const latestRow = await client.peaAxiiaMetadata.findFirst({
    where: { problemId },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    if (
      latestRow.initialMessage === initialMessage &&
      latestRow.prompt === prompt.content &&
      latestRow.promptReplacementKey === prompt.replacementKeys[0]
    ) {
      return { version: { problemId, version: latestRow.version }, changed: false }
    }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaAxiiaMetadata.create({
    data: {
      problemId,
      version: newVersion,
      initialMessage,
      prompt: prompt.content,
      promptReplacementKey: prompt.replacementKeys[0],
    },
  })

  return { version: { problemId, version: newVersion }, changed: true }
}

export async function resolveLatestAxiiaMetadataVersion(
  problemId: string,
  client: Prisma.TransactionClient,
): Promise<AxiiaMetadataVersion | undefined> {
  const row = await client.peaAxiiaMetadata.findFirst({
    where: { problemId },
    select: { version: true },
    orderBy: { version: 'desc' },
  })
  if (row === null) {
    return undefined
  }
  return { problemId, version: row.version }
}

export async function getAxiiaMetadata(
  version: AxiiaMetadataVersion,
  client: Prisma.TransactionClient,
): Promise<AxiiaMetadata | undefined> {
  const row = await client.peaAxiiaMetadata.findFirst({ where: version })
  if (!row) {
    return
  }

  const { initialMessage, prompt, promptReplacementKey } = row

  return {
    ...version,
    initialMessage,
    prompt,
    promptReplacementKey,
  }
}
