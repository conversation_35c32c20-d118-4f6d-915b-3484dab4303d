import { Prisma } from '@prisma/client'
import { deepEqual } from '@common/utils'
import { ProblemInput, ProblemVersion } from '../models/problem'
import { MaterialInput } from '../models/materials'
import { createOrUpdateAxiiaMetadata } from './axiia-metadata'
import { createOrUpdateMaterial } from './materials'
import { createOrUpdateSubmissionSpec } from './submission-spec'
import { createOrUpdateFeedbackSpec } from './feedback-spec'
import { createOrUpdateLabelPredGroup } from './label-pred'
import { LabelPredGroupInput } from '../models/label-pred'
import { createOrUpdateCustomChatList } from './custom-chat'

export async function createOrUpdateProblem(
  {
    id,
    axiiaMetadata,
    botPresetList,
    materialList,
    submissionSpec,
    feedbackSpec,
    labelPredGroupList,
    customChatList,
  }: ProblemInput,
  client: Prisma.TransactionClient,
): Promise<{ version: ProblemVersion; changed: boolean }> {
  const latestRow = await client.peaProblem.findFirst({
    where: { id },
    orderBy: { version: 'desc' },
  })

  const [
    { version: vAxiiaMetdata, changed: axiiaMetadataChanged },
    { version: vBotPresetList, changed: botPresetListChanged },
    { version: vMaterialList, changed: materialListChanged },
    { version: vSubmissionSpec, changed: submissionSpecChanged },
    { version: vFeedbackSpec, changed: feedbackSpecChanged },
    { version: vLabelPredictionGroupList, changed: labelPredGroupListChanged },
    { version: vCustomChatList, changed: customChatListChanged },
  ] = await Promise.all([
    createOrUpdateAxiiaMetadata(axiiaMetadata, client),
    createOrUpdateBotPresetList(id, botPresetList, client),
    createOrUpdateMaterialList(id, materialList, client),
    createOrUpdateSubmissionSpec(submissionSpec, client),
    feedbackSpec
      ? createOrUpdateFeedbackSpec(feedbackSpec, client)
      : Promise.resolve({ version: undefined, changed: false }),
    labelPredGroupList
      ? createOrUpdateLabelPredGroupList(id, labelPredGroupList, client)
      : Promise.resolve({ version: undefined, changed: false }),
    customChatList
      ? createOrUpdateCustomChatList(id, customChatList, client)
      : Promise.resolve({ version: undefined, changed: false }),
  ])

  const hasChanges = (() => {
    if (!latestRow) {
      return true
    }

    if (
      axiiaMetadataChanged ||
      botPresetListChanged ||
      materialListChanged ||
      submissionSpecChanged ||
      feedbackSpecChanged ||
      labelPredGroupListChanged ||
      customChatListChanged
    ) {
      return true
    }

    if (
      latestRow.vAxiiaMetdata !== vAxiiaMetdata.version ||
      latestRow.vBotPresetList !== vBotPresetList ||
      latestRow.vMaterialList !== vMaterialList ||
      latestRow.vSubmissionSpec !== vSubmissionSpec.version
    ) {
      return true
    }

    // They should be both missing or present.
    if ((latestRow.vFeedbackSpec === null) !== (feedbackSpec === undefined)) {
      return true
    }
    // If they are both present, they should match.
    if (latestRow.vFeedbackSpec !== null && latestRow.vFeedbackSpec !== vFeedbackSpec?.version) {
      return true
    }

    // Check for label prediction group list changes
    if ((latestRow.vLabelPredictionGroupList === null) !== (labelPredGroupList === undefined)) {
      return true
    }
    if (
      latestRow.vLabelPredictionGroupList !== null &&
      latestRow.vLabelPredictionGroupList !== vLabelPredictionGroupList
    ) {
      return true
    }

    // Check for custom chat list changes
    if ((latestRow.vCustomChatList === null) !== (customChatList === undefined)) {
      return true
    }
    if (latestRow.vCustomChatList !== null && latestRow.vCustomChatList !== vCustomChatList) {
      return true
    }

    return false
  })()

  if (latestRow && !hasChanges) {
    return { version: { id, version: latestRow.version }, changed: false }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaProblem.create({
    data: {
      id,
      version: newVersion,
      vAxiiaMetdata: vAxiiaMetdata.version,
      vBotPresetList,
      vMaterialList,
      vSubmissionSpec: vSubmissionSpec.version,
      vFeedbackSpec: vFeedbackSpec?.version,
      vLabelPredictionGroupList,
      vCustomChatList,
    },
  })

  return { version: { id, version: newVersion }, changed: true }
}

async function createOrUpdateBotPresetList(
  problemId: string,
  presets: string[],
  client: Prisma.TransactionClient,
): Promise<{ version: number; changed: boolean }> {
  const latestRow = await client.peaProblemBotPresetList.findFirst({
    where: { problemId },
    select: { version: true, presets: true },
    orderBy: { version: 'desc' },
  })

  if (latestRow && deepEqual(latestRow.presets, presets)) {
    return { version: latestRow.version, changed: false }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaProblemBotPresetList.create({
    data: {
      problemId,
      version: newVersion,
      presets,
    },
  })

  return { version: newVersion, changed: true }
}

async function createOrUpdateMaterialList(
  problemId: string,
  materials: MaterialInput[],
  client: Prisma.TransactionClient,
): Promise<{ version: number; changed: boolean }> {
  const latestRow = await client.peaProblemMaterialList.findFirst({
    where: { problemId },
    select: {
      version: true,
      materials: {
        select: { material: { select: { name: true, version: true } }, order: true },
        orderBy: {
          order: 'asc',
        },
      },
    },
    orderBy: { version: 'desc' },
  })

  const newMaterials = await Promise.all(
    materials.map((material) => createOrUpdateMaterial(material, client)),
  )

  const oldMaterialIdentities = latestRow?.materials.map((x) => [
    x.material.name,
    x.material.version,
    x.order,
  ])
  const newMaterialIdentities = newMaterials.map(({ version }, index) => [
    version.name,
    version.version,
    index,
  ])

  const newMaterialsData = newMaterials.map((material, index) => ({
    material: {
      connect: {
        problemId_name_version: {
          problemId: material.version.problemId,
          name: material.version.name,
          version: material.version.version,
        },
      },
    },
    order: index,
  }))

  if (latestRow && deepEqual(oldMaterialIdentities, newMaterialIdentities)) {
    return { version: latestRow.version, changed: false }
  }

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaProblemMaterialList.create({
    data: {
      problemId,
      version: newVersion,
      materials: {
        create: newMaterialsData,
      },
    },
  })

  return { version: newVersion, changed: true }
}

async function createOrUpdateLabelPredGroupList(
  problemId: string,
  groups: LabelPredGroupInput[],
  client: Prisma.TransactionClient,
): Promise<{ version: number; changed: boolean }> {
  const latestRow = await client.peaLabelPredictionGroupList.findFirst({
    where: { problemId },
    select: {
      version: true,
      groups: {
        select: { group: { select: { name: true, version: true } }, order: true },
        orderBy: {
          order: 'asc',
        },
      },
    },
    orderBy: { version: 'desc' },
  })

  const newGroups = await Promise.all(
    groups.map((group) => createOrUpdateLabelPredGroup(problemId, group, client)),
  )

  const oldGroupIdentities = latestRow?.groups.map((x) => [x.group.name, x.group.version, x.order])
  const newGroupIdentities = newGroups.map(({ version }, index) => [
    version.name,
    version.version,
    index,
  ])

  if (latestRow && deepEqual(oldGroupIdentities, newGroupIdentities)) {
    return { version: latestRow.version, changed: false }
  }

  const newGroupsData = newGroups.map((group, index) => ({
    group: {
      connect: {
        problemId_name_version: {
          problemId: group.version.problemId,
          name: group.version.name,
          version: group.version.version,
        },
      },
    },
    order: index,
  }))

  const newVersion = latestRow ? latestRow.version + 1 : 0

  await client.peaLabelPredictionGroupList.create({
    data: {
      problemId,
      version: newVersion,
      groups: {
        create: newGroupsData,
      },
    },
  })

  return { version: newVersion, changed: true }
}
