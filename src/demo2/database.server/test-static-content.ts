import getPrismaClient from '@deps/prisma-client'
import {
  ProblemStaticContent,
  SubmissionStaticContent,
  TestStaticContent,
  LabelPredGroupStaticContent,
  VisibleLabelPredPair,
  MaterialStaticContent,
} from '../models/test-static-content'
import { loadResolvedPresets } from './test'
import logger from '@deps/logger'
import { assertWithHttpError } from '@common/utils'

async function loadLabelPredGroups(
  problemId: string,
  version: number | null,
): Promise<LabelPredGroupStaticContent[] | undefined> {
  if (version === null) {
    return
  }

  const client = await getPrismaClient()

  const groupList = await client.peaLabelPredictionGroupList.findUnique({
    where: { problemId_version: { problemId, version } },
    include: {
      groups: {
        include: {
          group: {
            include: {
              pairs: {
                select: {
                  pair: {
                    select: {
                      name: true,
                      input: true,
                      output: true,
                    },
                  },
                },
                orderBy: {
                  order: 'asc',
                },
              },
            },
          },
        },
        orderBy: {
          order: 'asc',
        },
      },
    },
  })

  if (!groupList) {
    return []
  }

  return await Promise.all(
    groupList.groups.map(async ({ group }): Promise<LabelPredGroupStaticContent> => {
      if (group.hidesInput) {
        return {
          name: group.name,
          title: group.title,
          hidesInput: true,
          pairs: group.pairs.map((x) => ({ name: x.pair.name })),
        }
      }

      const pairs: VisibleLabelPredPair[] = await Promise.all(
        group.pairs.map(async ({ pair }) => {
          return {
            name: pair.name,
            input: pair.input,
            output: pair.output,
          }
        }),
      )

      return {
        name: group.name,
        title: group.title,
        hidesInput: false,
        pairs,
      }
    }),
  )
}

export async function loadMaterials(
  problemId: string,
  listVersion: number,
): Promise<MaterialStaticContent[]> {
  const client = await getPrismaClient()

  const rows = await client.peaMaterialMembership.findMany({
    where: { problemId, listVersion },
    orderBy: { order: 'asc' },
    include: { material: true },
  })

  return rows.map((x) => ({
    name: x.material.name,
    title: x.material.title,
    content: x.material.content,
    copyable: x.material.copyable,
  }))
}

export async function loadProblemStaticContent(
  testId: string,
  n: number,
): Promise<ProblemStaticContent> {
  assertWithHttpError(n >= 0, 404)
  const client = await getPrismaClient()
  const test = await client.peaTest.findUniqueOrThrow({
    where: { id: testId },
  })
  assertWithHttpError(n < test.problems.length, 404)

  const problem = await client.peaProblem.findUniqueOrThrow({
    where: {
      id_version: {
        id: test.problems[n].problem_id,
        version: test.problems[n].problem_version,
      },
    },
    include: {
      feedbackSpec: true,
      submissionSpec: true,
      axiiaMetadata: {
        select: {
          initialMessage: true,
        },
      },
    },
  })

  logger.info('static content problem loaded')

  const [botPresets, materials, labelPredGroups] = await Promise.all([
    loadResolvedPresets(test.problems[n].resolved_problem_id, client),
    loadMaterials(problem.id, problem.vMaterialList),
    loadLabelPredGroups(problem.id, problem.vLabelPredictionGroupList),
  ])

  const submission: SubmissionStaticContent = {
    hasFeedback: problem.feedbackSpec !== null,
    prefill: problem.submissionSpec.prefill,
    requiredPhrases: problem.submissionSpec.requiredPhrases,
  }

  return {
    testId,
    problemVersion: {
      id: problem.id,
      version: problem.version,
    },
    axiiaInitialMessage: problem.axiiaMetadata.initialMessage,
    botPresets,
    materials,
    submission,
    labelPredGroups,
  }
}

export async function loadTestStaticContent(testId: string): Promise<TestStaticContent> {
  const client = await getPrismaClient()
  const test = await client.peaTest.findUniqueOrThrow({
    where: {
      id: testId,
    },
    include: {
      problemSet: true,
      progresses: {
        orderBy: {
          createdAt: 'asc',
        },
        take: 1,
        select: {
          createdAt: true,
        },
      },
    },
  })

  const allProblems = test.problems.map(({ problem_id, problem_version }) => ({
    id: problem_id,
    version: problem_version,
  }))

  logger.info('static content test loaded')

  const firstProgressCreatedAt =
    test.progresses.length > 0 ? test.progresses[0].createdAt.toISOString() : undefined

  const expireMinutes = test.problemSet?.expireMinutes ?? undefined

  return {
    allProblems,
    isComplete: test.isComplete,
    firstProgressCreatedAt,
    problemSet: test.problemSet
      ? {
          id: test.problemSet.id,
          expireMinutes,
        }
      : undefined,
  }
}
