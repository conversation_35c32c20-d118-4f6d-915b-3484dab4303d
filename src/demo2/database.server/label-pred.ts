import { Prisma } from '@prisma/client'
import {
  LabelPredGroupInput,
  LabelPredGroupVersion,
  LabelPredPairInput,
  LabelPredPairVersion,
} from '../models/label-pred'
import { deepEqual } from '@common/utils'

export async function createOrUpdateLabelPredPair(
  problemId: string,
  groupName: string,
  { name, input, output }: LabelPredPairInput,
  client: Prisma.TransactionClient,
): Promise<{ version: LabelPredPairVersion; changed: boolean }> {
  const latestRow = await client.peaLabelPredictionPair.findFirst({
    where: { problemId, groupName, name },
    select: { id: true, version: true, input: true, output: true },
    orderBy: { version: 'desc' },
  })

  if (latestRow) {
    if (latestRow.input === input && latestRow.output === output) {
      return {
        version: {
          id: latestRow.id,
          name: name,
          version: latestRow.version,
        },
        changed: false,
      }
    }
  }

  const version = latestRow ? latestRow.version + 1 : 0

  const newPair = await client.peaLabelPredictionPair.create({
    data: {
      problemId,
      groupName,
      name,
      version,
      output,
      input,
    },
  })

  return {
    version: {
      id: newPair.id,
      name: name,
      version: version,
    },
    changed: true,
  }
}

export async function createOrUpdateLabelPredGroup(
  problemId: string,
  { name, title, hidesInput, pairs }: LabelPredGroupInput,
  client: Prisma.TransactionClient,
): Promise<{ version: LabelPredGroupVersion; changed: boolean }> {
  // Save each pair
  const pairResults = await Promise.all(
    pairs.map((pair) => createOrUpdateLabelPredPair(problemId, name, pair, client)),
  )

  const latestGroup = await client.peaLabelPredictionGroup.findFirst({
    where: { problemId, name },
    include: {
      pairs: {
        orderBy: { order: 'asc' },
        select: { pairName: true, pairVersion: true },
      },
    },
    orderBy: { version: 'desc' },
  })

  const pairsMatch = latestGroup?.pairs.every((pair, index) => {
    const currentPair = pairResults[index].version
    return pair.pairName === currentPair.name && deepEqual(pair.pairVersion, currentPair.version)
  })

  if (
    latestGroup &&
    latestGroup.title === title &&
    latestGroup.hidesInput === hidesInput &&
    pairsMatch &&
    latestGroup.pairs.length === pairs.length
  ) {
    return {
      version: {
        problemId,
        name,
        version: latestGroup.version,
      },
      changed: false,
    }
  }

  const newVersion = (latestGroup?.version ?? -1) + 1

  const newGroup = await client.peaLabelPredictionGroup.create({
    data: {
      problemId,
      name,
      version: newVersion,
      title,
      hidesInput,
      pairs: {
        create: pairs.map((pair, index) => ({
          pairName: pair.name,
          pairVersion: pairResults[index].changed ? newVersion : newVersion - 1,
          order: index,
        })),
      },
    },
  })

  return {
    version: {
      problemId,
      name,
      version: newGroup.version,
    },
    changed: true,
  }
}
