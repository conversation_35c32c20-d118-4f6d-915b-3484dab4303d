import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import {
  createOrUpdateBotPreset,
  getBotPresetItem,
  getBotPresetPrompt,
  resolveLatestBotPresetVersions,
} from '../bot-presets'

function genId(): string {
  return crypto.randomUUID()
}

describe('Bot Preset Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a record and retrieve it via getItem and getPrompt', async () => {
    const id = genId()
    await prisma.$transaction(async (tx) => {
      const input = { id, title: 'Test Preset 1', prompt: 'Test prompt 1' }
      const { version: savedVersion } = await createOrUpdateBotPreset(input, tx)

      const retrievedItem = await getBotPresetItem(savedVersion, tx)
      expect(retrievedItem).toEqual({ id, version: savedVersion.version, title: 'Test Preset 1' })

      const retrievedPrompt = await getBotPresetPrompt(savedVersion, tx)
      expect(retrievedPrompt).toBe('Test prompt 1')
    })
  })

  it('should save two records at the same time without interference', async () => {
    const id1 = genId()
    const id2 = genId()
    await prisma.$transaction(async (tx) => {
      const input1 = { id: id1, title: 'Test Preset 2', prompt: 'Test prompt 2' }
      const input2 = { id: id2, title: 'Test Preset 3', prompt: 'Test prompt 3' }

      const { version: savedVersion1, changed: changed1 } = await createOrUpdateBotPreset(
        input1,
        tx,
      )
      const { version: savedVersion2, changed: changed2 } = await createOrUpdateBotPreset(
        input2,
        tx,
      )

      expect(changed1).toBeTruthy()
      expect(changed2).toBeTruthy()

      const retrievedItem1 = await getBotPresetItem(savedVersion1, tx)
      const retrievedItem2 = await getBotPresetItem(savedVersion2, tx)

      expect(retrievedItem1).toEqual({
        id: id1,
        version: savedVersion1.version,
        title: 'Test Preset 2',
      })
      expect(retrievedItem2).toEqual({
        id: id2,
        version: savedVersion2.version,
        title: 'Test Preset 3',
      })

      const retrievedPrompt1 = await getBotPresetPrompt(savedVersion1, tx)
      const retrievedPrompt2 = await getBotPresetPrompt(savedVersion2, tx)

      expect(retrievedPrompt1).toBe('Test prompt 2')
      expect(retrievedPrompt2).toBe('Test prompt 3')
    })
  })

  it('should update a record when saving with the same id', async () => {
    const id = genId()
    await prisma.$transaction(async (tx) => {
      const initialInput = { id, title: 'Initial Preset', prompt: 'Initial prompt' }
      const updatedInput = { id, title: 'Updated Preset', prompt: 'Updated prompt' }

      const { version: initialVersion } = await createOrUpdateBotPreset(initialInput, tx)
      const { version: updatedVersion } = await createOrUpdateBotPreset(updatedInput, tx)

      expect(updatedVersion.version).toBeGreaterThan(initialVersion.version)

      const retrievedItem = await getBotPresetItem(updatedVersion, tx)
      expect(retrievedItem).toEqual({
        id,
        version: updatedVersion.version,
        title: 'Updated Preset',
      })

      const retrievedPrompt = await getBotPresetPrompt(updatedVersion, tx)
      expect(retrievedPrompt).toBe('Updated prompt')
    })
  })

  it('should resolve latest versions correctly and preserve input order', async () => {
    const id1 = genId()
    const id2 = genId()
    const id3 = genId()
    await prisma.$transaction(async (tx) => {
      // Create multiple versions of presets

      await createOrUpdateBotPreset({ id: id1, title: 'Preset 1 v1', prompt: 'Prompt 1 v1' }, tx)
      const { version: v2 } = await createOrUpdateBotPreset(
        { id: id1, title: 'Preset 1 v2', prompt: 'Prompt 1 v2' },
        tx,
      )
      const { version: v3 } = await createOrUpdateBotPreset(
        { id: id2, title: 'Preset 2 v1', prompt: 'Prompt 2 v1' },
        tx,
      )
      await createOrUpdateBotPreset({ id: id3, title: 'Preset 3 v1', prompt: 'Prompt 3 v1' }, tx)
      const { version: v5 } = await createOrUpdateBotPreset(
        { id: id3, title: 'Preset 3 v2', prompt: 'Prompt 3 v2' },
        tx,
      )

      // Test with a different order than creation
      const inputOrder = [id2, id3, id1]
      const latestVersions = await resolveLatestBotPresetVersions(inputOrder, tx)

      expect(latestVersions).toEqual([
        { id: id2, version: v3.version },
        { id: id3, version: v5.version },
        { id: id1, version: v2.version },
      ])
    })
  })

  it('should not create a new version when passing the same input', async () => {
    const id = genId()
    const input = { id, title: 'Test Preset', prompt: 'Test prompt' }

    await prisma.$transaction(async (tx) => {
      // Create initial version
      const { version: initialVersion, changed: initialChanged } = await createOrUpdateBotPreset(
        input,
        tx,
      )

      expect(initialChanged).toBe(true)

      // Try to create/update with the same input
      const { version: updatedVersion, changed: updatedChanged } = await createOrUpdateBotPreset(
        input,
        tx,
      )

      expect(updatedChanged).toBe(false)
      expect(updatedVersion).toEqual(initialVersion)
    })
  })
})
