import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import {
  createOrUpdateSubmissionSpec,
  getSubmissionSpec,
  resolveLatestSubmissionSpecVersion,
} from '../submission-spec'

function genId(): string {
  return crypto.randomUUID()
}

describe('Submission Spec Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a submission spec record and retrieve it', async () => {
    const problemId = genId()
    const requiredPhrases = ['phrase1', 'phrase2']
    const maxLength = 1000
    const prefill = 'Prefill content'
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateSubmissionSpec(
        { problemId, requiredPhrases, maxLength, prefill },
        tx,
      )

      expect(changed).toBe(true)

      const retrievedSpec = await getSubmissionSpec(savedVersion, tx)
      expect(retrievedSpec).toEqual({
        problemId,
        version: savedVersion.version,
        requiredPhrases: ['phrase1', 'phrase2'],
        maxLength: 1000,
        prefill: 'Prefill content',
      })
    })
  })

  it('should update a submission spec record when saving with the same problemId', async () => {
    const problemId = genId()
    const initialRequiredPhrases = ['phrase1', 'phrase2']
    const initialMaxLength = 1000
    const initialPrefill = 'Initial prefill content'
    const updatedRequiredPhrases = ['phrase3']
    const updatedMaxLength = 500
    const updatedPrefill = 'Updated prefill content'
    await prisma.$transaction(async (tx) => {
      const { version: initialVersion, changed: initialChanged } =
        await createOrUpdateSubmissionSpec(
          {
            problemId,
            requiredPhrases: initialRequiredPhrases,
            maxLength: initialMaxLength,
            prefill: initialPrefill,
          },
          tx,
        )
      expect(initialChanged).toBe(true)

      const { version: updatedVersion, changed: updatedChanged } =
        await createOrUpdateSubmissionSpec(
          {
            problemId,
            requiredPhrases: updatedRequiredPhrases,
            maxLength: updatedMaxLength,
            prefill: updatedPrefill,
          },
          tx,
        )
      expect(updatedChanged).toBe(true)

      expect(updatedVersion.version).toBeGreaterThan(initialVersion.version)
      const retrievedInitialSpec = await getSubmissionSpec(initialVersion, tx)
      expect(retrievedInitialSpec).toEqual({
        problemId,
        version: initialVersion.version,
        requiredPhrases: ['phrase1', 'phrase2'],
        maxLength: 1000,
        prefill: 'Initial prefill content',
      })

      const retrievedUpdatedSpec = await getSubmissionSpec(updatedVersion, tx)
      expect(retrievedUpdatedSpec).toEqual({
        problemId,
        version: updatedVersion.version,
        requiredPhrases: ['phrase3'],
        maxLength: 500,
        prefill: 'Updated prefill content',
      })
    })
  })

  it('should resolve the latest version of a submission spec record', async () => {
    const problemId = genId()
    const requiredPhrases = ['phrase1', 'phrase2']
    const maxLength = 1000
    const prefill = 'Prefill content'
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateSubmissionSpec(
        { problemId, requiredPhrases, maxLength, prefill },
        tx,
      )
      expect(changed).toBe(true)

      const latestVersion = await resolveLatestSubmissionSpecVersion(problemId, tx)
      expect(latestVersion).toEqual({ problemId, version: savedVersion.version })
    })
  })

  it('should handle optional maxLength', async () => {
    const problemId = genId()
    const requiredPhrases = ['phrase1']
    const prefill = 'Prefill content'
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateSubmissionSpec(
        { problemId, requiredPhrases, prefill },
        tx,
      )
      expect(changed).toBe(true)

      const retrievedSpec = await getSubmissionSpec(savedVersion, tx)
      expect(retrievedSpec).toEqual({
        problemId,
        version: savedVersion.version,
        requiredPhrases: ['phrase1'],
        maxLength: null,
        prefill: 'Prefill content',
      })
    })
  })

  it('should not create a new version when passing the same input', async () => {
    const problemId = genId()
    const requiredPhrases = ['phrase1']
    const maxLength = 1000
    const prefill = 'Test prefill'
    const input = { problemId, requiredPhrases, maxLength, prefill }

    await prisma.$transaction(async (tx) => {
      // Create initial version
      const { version: initialVersion, changed: initialChanged } =
        await createOrUpdateSubmissionSpec(input, tx)

      expect(initialChanged).toBe(true)

      // Try to create/update with the same input
      const { version: updatedVersion, changed: updatedChanged } =
        await createOrUpdateSubmissionSpec(input, tx)

      expect(updatedChanged).toBe(false)
      expect(updatedVersion).toEqual(initialVersion)
    })
  })
})
