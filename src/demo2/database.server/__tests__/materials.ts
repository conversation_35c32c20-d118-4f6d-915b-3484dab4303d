import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import {
  createOrUpdateMaterial,
  getMaterialItem,
  getMaterialContent,
  resolveLatestMaterialVersions,
} from '../materials'
import { MaterialInput } from '../../models/materials'

function genId(): string {
  return crypto.randomUUID()
}

describe('Material Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a record and retrieve it via getMaterialItem and getMaterialContent', async () => {
    const problemId = genId()
    await prisma.$transaction(async (tx) => {
      const input: MaterialInput = {
        problemId,
        name: 'material1',
        title: 'Test Material 1',
        content: 'Test content 1',
      }
      const { version: savedVersion } = await createOrUpdateMaterial(input, tx)

      const retrievedItem = await getMaterialItem(savedVersion, tx)
      expect(retrievedItem).toEqual({
        problemId,
        name: 'material1',
        version: savedVersion.version,
        title: 'Test Material 1',
        copyable: false,
      })

      const retrievedContent = await getMaterialContent(savedVersion, tx)
      expect(retrievedContent).toBe('Test content 1')
    })
  })

  it('should save two records at the same time without interference', async () => {
    const problemId = genId()
    await prisma.$transaction(async (tx) => {
      const input1: MaterialInput = {
        problemId,
        name: 'material2',
        title: 'Test Material 2',
        content: 'Test content 2',
      }
      const input2: MaterialInput = {
        problemId,
        name: 'material3',
        title: 'Test Material 3',
        content: 'Test content 3',
      }

      const { version: savedVersion1, changed: changed1 } = await createOrUpdateMaterial(input1, tx)
      const { version: savedVersion2, changed: changed2 } = await createOrUpdateMaterial(input2, tx)

      expect(changed1).toBeTruthy()
      expect(changed2).toBeTruthy()

      const retrievedItem1 = await getMaterialItem(savedVersion1, tx)
      const retrievedItem2 = await getMaterialItem(savedVersion2, tx)

      expect(retrievedItem1).toEqual({
        problemId,
        name: 'material2',
        version: savedVersion1.version,
        title: 'Test Material 2',
        copyable: false,
      })
      expect(retrievedItem2).toEqual({
        problemId,
        name: 'material3',
        version: savedVersion2.version,
        title: 'Test Material 3',
        copyable: false,
      })

      const retrievedContent1 = await getMaterialContent(savedVersion1, tx)
      const retrievedContent2 = await getMaterialContent(savedVersion2, tx)

      expect(retrievedContent1).toBe('Test content 2')
      expect(retrievedContent2).toBe('Test content 3')
    })
  })

  it('should update a record when saving with the same problemId and name', async () => {
    const problemId = genId()
    await prisma.$transaction(async (tx) => {
      const initialInput: MaterialInput = {
        problemId,
        name: 'material4',
        title: 'Initial Material',
        content: 'Initial content',
      }
      const updatedInput: MaterialInput = {
        problemId,
        name: 'material4',
        title: 'Updated Material',
        content: 'Updated content',
      }

      const { version: initialVersion } = await createOrUpdateMaterial(initialInput, tx)
      const { version: updatedVersion } = await createOrUpdateMaterial(updatedInput, tx)

      expect(updatedVersion.version).toBeGreaterThan(initialVersion.version)

      const retrievedItem = await getMaterialItem(updatedVersion, tx)
      expect(retrievedItem).toEqual({
        problemId,
        name: 'material4',
        version: updatedVersion.version,
        title: 'Updated Material',
        copyable: false,
      })

      const retrievedContent = await getMaterialContent(updatedVersion, tx)
      expect(retrievedContent).toBe('Updated content')
    })
  })

  it('should resolve latest versions correctly and preserve input order', async () => {
    const problemId = genId()
    await prisma.$transaction(async (tx) => {
      // Create multiple versions of materials
      await createOrUpdateMaterial(
        { problemId, name: 'material1', title: 'Material 1 v1', content: 'Content 1 v1' },
        tx,
      )
      const { version: v2 } = await createOrUpdateMaterial(
        { problemId, name: 'material1', title: 'Material 1 v2', content: 'Content 1 v2' },
        tx,
      )
      const { version: v3 } = await createOrUpdateMaterial(
        { problemId, name: 'material2', title: 'Material 2 v1', content: 'Content 2 v1' },
        tx,
      )
      await createOrUpdateMaterial(
        { problemId, name: 'material3', title: 'Material 3 v1', content: 'Content 3 v1' },
        tx,
      )
      const { version: v5 } = await createOrUpdateMaterial(
        { problemId, name: 'material3', title: 'Material 3 v2', content: 'Content 3 v2' },
        tx,
      )

      // Test with a different order than creation
      const inputOrder = [
        { problemId, name: 'material2' },
        { problemId, name: 'material3' },
        { problemId, name: 'material1' },
      ]
      const latestVersions = await resolveLatestMaterialVersions(inputOrder, tx)

      expect(latestVersions).toEqual([
        { problemId, name: 'material2', version: v3.version },
        { problemId, name: 'material3', version: v5.version },
        { problemId, name: 'material1', version: v2.version },
      ])
    })
  })

  it('should not create a new version when passing the same input', async () => {
    const problemId = genId()
    const input: MaterialInput = {
      problemId,
      name: 'material5',
      title: 'Test Material',
      content: 'Test content',
    }

    await prisma.$transaction(async (tx) => {
      // Create initial version
      const { version: initialVersion, changed: initialChanged } = await createOrUpdateMaterial(
        input,
        tx,
      )

      expect(initialChanged).toBe(true)

      // Try to create/update with the same input
      const { version: updatedVersion, changed: updatedChanged } = await createOrUpdateMaterial(
        input,
        tx,
      )

      expect(updatedChanged).toBe(false)
      expect(updatedVersion).toEqual(initialVersion)
    })
  })
})
