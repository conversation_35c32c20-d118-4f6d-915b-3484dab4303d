import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import {
  createOrUpdateFeedbackSpec,
  getFeedbackSpec,
  resolveLatestFeedbackSpecVersion,
} from '../feedback-spec'

function genId(): string {
  return crypto.randomUUID()
}

describe('Feedback Spec Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a feedback spec and retrieve it', async () => {
    const problemId = genId()
    const keys = ['key1', 'key2']
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    const template = 'Template content'
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateFeedbackSpec(
        { problemId, keys, prompt, template },
        tx,
      )

      expect(changed).toBe(true)

      const retrievedSpec = await getFeedbackSpec(savedVersion, tx)
      expect(retrievedSpec).toEqual({
        problemId,
        version: savedVersion.version,
        keys: ['key1', 'key2'],
        template: 'Template content',
        prompt: 'Prompt text content',
        promptReplacementKey: 'key',
      })
    })
  })

  it('should update a feedback spec when saving with the same problemId', async () => {
    const problemId = genId()
    const keys = ['key1', 'key2']
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    const template = 'Template content'
    const updatedKeys = ['key3']
    const updatedPrompt = { content: 'Updated prompt text', replacementKeys: ['new-key'] }
    const updatedTemplate = 'Updated template content'
    await prisma.$transaction(async (tx) => {
      const { version: initialVersion, changed: initialChanged } = await createOrUpdateFeedbackSpec(
        { problemId, keys, prompt, template },
        tx,
      )
      expect(initialChanged).toBe(true)

      const { version: updatedVersion, changed: updatedChanged } = await createOrUpdateFeedbackSpec(
        { problemId, keys: updatedKeys, prompt: updatedPrompt, template: updatedTemplate },
        tx,
      )
      expect(updatedChanged).toBe(true)

      expect(updatedVersion.version).toBeGreaterThan(initialVersion.version)
      const retrievedInitialSpec = await getFeedbackSpec(initialVersion, tx)
      expect(retrievedInitialSpec).toEqual({
        problemId,
        version: initialVersion.version,
        keys: ['key1', 'key2'],
        template: 'Template content',
        prompt: 'Prompt text content',
        promptReplacementKey: 'key',
      })

      const retrievedUpdatedSpec = await getFeedbackSpec(updatedVersion, tx)
      expect(retrievedUpdatedSpec).toEqual({
        problemId,
        version: updatedVersion.version,
        keys: ['key3'],
        template: 'Updated template content',
        prompt: 'Updated prompt text',
        promptReplacementKey: 'new-key',
      })
    })
  })

  it('should resolve the latest version of a feedback spec', async () => {
    const problemId = genId()
    const keys = ['key1', 'key2']
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    const template = 'Template content'
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateFeedbackSpec(
        { problemId, keys, prompt, template },
        tx,
      )
      expect(changed).toBe(true)

      const latestVersion = await resolveLatestFeedbackSpecVersion(problemId, tx)
      expect(latestVersion).toEqual({ problemId, version: savedVersion.version })
    })
  })

  it('should not create a new version when passing the same input', async () => {
    const problemId = genId()
    const keys = ['key1', 'key2']
    const prompt = { content: 'Test prompt', replacementKeys: ['key'] }
    const template = 'Test template'
    const input = { problemId, keys, prompt, template }

    await prisma.$transaction(async (tx) => {
      // Create initial version
      const { version: initialVersion, changed: initialChanged } = await createOrUpdateFeedbackSpec(
        input,
        tx,
      )

      expect(initialChanged).toBe(true)

      // Try to create/update with the same input
      const { version: updatedVersion, changed: updatedChanged } = await createOrUpdateFeedbackSpec(
        input,
        tx,
      )

      expect(updatedChanged).toBe(false)
      expect(updatedVersion).toEqual(initialVersion)
    })
  })
})
