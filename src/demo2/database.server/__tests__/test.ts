import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { createTest, createTestFromDynamicProblems, loadResolvedPresets } from '../test'
import { createOrUpdateProblem } from '../problem'
import { createOrUpdateBotPreset } from '../bot-presets'
import { ProblemInput } from '../../models/problem'
import { BotPresetInput } from '../../models/bot-presets'

describe('Test Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  function generateProblemInput(): ProblemInput {
    const problemId = crypto.randomUUID()
    return {
      id: problemId,
      axiiaMetadata: {
        problemId,
        initialMessage: 'Initial message for the test problem',
        prompt: {
          content: 'Prompt text for the test problem',
          replacementKeys: ['key'],
        },
      },
      botPresetList: [],
      materialList: [],
      submissionSpec: {
        problemId,
        requiredPhrases: [],
        maxLength: 1000,
        prefill: '',
      },
    }
  }

  function generateBotPresetInput(): BotPresetInput {
    return {
      id: crypto.randomUUID(),
      title: `Bot Preset ${crypto.randomUUID().slice(0, 8)}`,
      prompt: `This is a test prompt for bot preset ${crypto.randomUUID().slice(0, 8)}`,
    }
  }

  it('should create a test with resolved bot presets', async () => {
    await prisma.$transaction(async (tx) => {
      // Create a problem
      const problemInput = generateProblemInput()
      await createOrUpdateProblem(problemInput, tx)

      // Create two bot presets
      const botPreset1 = generateBotPresetInput()
      const botPreset2 = generateBotPresetInput()
      const {
        version: { version: vBotPreset1 },
      } = await createOrUpdateBotPreset(botPreset1, tx)
      const {
        version: { version: vBotPreset2 },
      } = await createOrUpdateBotPreset(botPreset2, tx)

      // Update problem with bot presets
      const updatedProblemInput = {
        ...problemInput,
        botPresetList: [botPreset1.id, botPreset2.id],
      }
      const { version: updatedProblemVersion } = await createOrUpdateProblem(
        updatedProblemInput,
        tx,
      )

      // Create test
      const testId = await createTest([updatedProblemVersion], tx)
      const test = await tx.peaTest.findUniqueOrThrow({
        where: { id: testId },
      })

      // Verify resolved bot presets
      const resolvedPresets = await loadResolvedPresets(test.problems[0].resolved_problem_id, tx)

      expect(resolvedPresets).toHaveLength(2)
      expect(resolvedPresets[0]).toEqual({
        id: botPreset1.id,
        version: vBotPreset1,
        title: botPreset1.title,
        prompt: expect.any(String),
      })
      expect(resolvedPresets[1]).toEqual({
        id: botPreset2.id,
        version: vBotPreset2,
        title: botPreset2.title,
        prompt: expect.any(String),
      })
    })
  })

  it('creates a test from dynamic problems with correct versions', async () => {
    // Create two problems
    const problem1Input = generateProblemInput()
    const problem1 = await createOrUpdateProblem(problem1Input, prisma)

    const problem2Input = generateProblemInput()
    const problem2 = await createOrUpdateProblem(problem2Input, prisma)

    // Update each problem once
    const updatedProblem1Input = {
      ...problem1Input,
      axiiaMetadata: {
        ...problem1Input.axiiaMetadata,
        initialMessage: 'Updated message for problem1',
      },
    }
    const updatedProblem1 = await createOrUpdateProblem(updatedProblem1Input, prisma)

    const updatedProblem2Input = {
      ...problem2Input,
      axiiaMetadata: {
        ...problem2Input.axiiaMetadata,
        initialMessage: 'Updated message for problem2',
      },
    }
    const updatedProblem2 = await createOrUpdateProblem(updatedProblem2Input, prisma)

    // Create the test from the problem IDs
    const testId = await createTestFromDynamicProblems(
      [problem1.version.id, problem2.version.id],
      prisma,
    )

    // Verify the test and its resolved problems
    const test = await prisma.peaTest.findUniqueOrThrow({
      where: { id: testId },
    })

    expect(test.problems).toHaveLength(2)

    const resolvedProblem1 = await prisma.peaResolvedProblem.findUniqueOrThrow({
      where: { id: test.problems[0].resolved_problem_id },
    })
    const resolvedProblem2 = await prisma.peaResolvedProblem.findUniqueOrThrow({
      where: { id: test.problems[1].resolved_problem_id },
    })

    expect(resolvedProblem1.problemVersion).toEqual(updatedProblem1.version.version)
    expect(resolvedProblem2.problemVersion).toEqual(updatedProblem2.version.version)
    expect(test.problems[0].problem_version).toEqual(updatedProblem1.version.version)
    expect(test.problems[1].problem_version).toEqual(updatedProblem2.version.version)
  })
})
