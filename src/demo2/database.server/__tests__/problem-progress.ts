import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { createCustomChat, uploadSubmission } from '../problem-progress'
import { createTest } from '../test'
import { createOrUpdateProblem } from '../problem'
import { createOrUpdateBotPreset } from '../bot-presets'
import { ProblemInput } from '../../models/problem'
import { BotPresetInput } from '../../models/bot-presets'
import invariant from 'tiny-invariant'
import { loadProblemDynamicContent } from '../test-dynamic-content'

describe('Problem Progress Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  function generateProblemInput(): ProblemInput {
    const problemId = crypto.randomUUID()
    return {
      id: problemId,
      axiiaMetadata: {
        problemId,
        initialMessage: 'Initial message for the test problem',
        prompt: {
          content: 'Prompt text for the test problem',
          replacementKeys: ['key'],
        },
      },
      botPresetList: [],
      materialList: [],
      submissionSpec: {
        problemId,
        requiredPhrases: ['required phrase'],
        maxLength: 1000,
        prefill: 'Prefill text',
      },
    }
  }

  function generateBotPresetInput(): BotPresetInput {
    return {
      id: crypto.randomUUID(),
      title: `Bot Preset ${crypto.randomUUID().slice(0, 8)}`,
      prompt: `This is a test prompt for bot preset ${crypto.randomUUID().slice(0, 8)}`,
    }
  }

  it('should create a custom chat', async () => {
    // Create a problem and a bot preset
    const problemInput = generateProblemInput()
    const botPresetInput = generateBotPresetInput()
    problemInput.botPresetList = [botPresetInput.id]

    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
    await createOrUpdateBotPreset(botPresetInput, prisma)

    // Create and start a test
    const testId = await createTest([problemVersion], prisma)
    const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })

    // Get the current problem progress
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: { test: true },
    })

    // Create a custom chat
    const customChat = await createCustomChat(
      { testId, n: 0 },
      {
        title: 'Test Custom Chat',
        sourcePresetId: botPresetInput.id,
      },
    )

    // Verify the custom chat
    expect(customChat).toEqual({
      id: expect.any(String),
      progressId: progress!.id,
      title: 'Test Custom Chat',
      sourcePresetId: botPresetInput.id,
    })

    // Load the custom chat's related PeaChat row
    const chatRow = await prisma.peaChat.findUnique({
      where: { id: customChat.id },
    })
    expect(chatRow).not.toBeNull()

    // Fetch the system prompt content
    const systemPromptContent = chatRow!.systemPrompt

    // Fetch the original bot preset content for comparison
    const botPreset = await prisma.peaBotPreset.findFirst({
      where: { id: botPresetInput.id },
    })
    expect(botPreset).not.toBeNull()
    const botPresetContent = botPreset!.prompt

    // Verify that the system prompt matches the bot preset content
    expect(systemPromptContent).toBe(botPresetContent)
  })

  it('should create a custom chat with a provided prompt', async () => {
    // Create and start a test with a problem
    const problemInput = generateProblemInput()
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
    const testId = await createTest([problemVersion], prisma)
    const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })

    // Get the current problem progress
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: { test: true },
    })

    // Create a custom chat with a provided prompt
    const customChat = await createCustomChat(
      { testId, n: 0 },
      {
        title: 'Test Custom Chat with Prompt',
        prompt: 'This is a custom prompt for the chat.',
      },
    )

    // Verify the custom chat
    expect(customChat).toEqual({
      id: expect.any(String),
      progressId: progress!.id,
      title: 'Test Custom Chat with Prompt',
      sourcePresetId: null,
    })

    // Load the custom chat's related PeaChat row
    const chatRow = await prisma.peaChat.findUnique({
      where: { id: customChat.id },
    })
    expect(chatRow).not.toBeNull()

    // Fetch the system prompt content
    const systemPromptContent = chatRow!.systemPrompt

    // Verify that the system prompt matches the provided prompt
    expect(systemPromptContent).toBe('This is a custom prompt for the chat.')
  })

  it('should upload a valid submission', async () => {
    // Create and start a test with a problem
    const problemInput = generateProblemInput()
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
    const testId = await createTest([problemVersion], prisma)
    await loadProblemDynamicContent({ testId, n: 0 })

    // Upload the submission
    const result = await uploadSubmission(
      { testId, n: 0 },
      'This is a valid submission containing the required phrase',
    )

    // Verify the result
    expect(result).toEqual({
      id: expect.any(String),
      timestamp: expect.any(Number),
      isValid: true,
      sha256: expect.any(String),
      changed: true,
    })
  })

  it('should create separate text entries for identical submissions in different tests', async () => {
    const problemInput = generateProblemInput()
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
    // Create and start two separate tests
    const testId1 = await createTest([problemVersion], prisma)
    const testId2 = await createTest([problemVersion], prisma)
    await loadProblemDynamicContent({ testId: testId1, n: 0 })
    await loadProblemDynamicContent({ testId: testId2, n: 0 })

    // Create identical submission upload for both tests
    const submissionContent = 'This is a valid submission containing the required phrase'
    const result1 = await uploadSubmission({ testId: testId1, n: 0 }, submissionContent)
    const result2 = await uploadSubmission({ testId: testId2, n: 0 }, submissionContent)

    // Verify that both submissions are valid and have different IDs
    expect(result1.isValid).toBe(true)
    expect(result2.isValid).toBe(true)
    invariant(result1.isValid && result2.isValid)
    expect(result1.id).not.toBe(result2.id)

    // Fetch the text entries for both submissions
    const snapshot1 = await prisma.peaSubmissionSnapshot.findUnique({
      where: { id: result1.id },
      include: { text: true },
    })
    const snapshot2 = await prisma.peaSubmissionSnapshot.findUnique({
      where: { id: result2.id },
      include: { text: true },
    })
    invariant(snapshot1 && snapshot2)

    // Verify that the text entries exist and have different IDs
    expect(snapshot1.text).not.toBeNull()
    expect(snapshot2.text).not.toBeNull()
    expect(snapshot1!.id).not.toBe(snapshot2!.id)
  })

  it('should handle identical submissions within a single test correctly', async () => {
    const problemInput = generateProblemInput()
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
    // Create and start a test
    const testId = await createTest([problemVersion], prisma)
    await loadProblemDynamicContent({ testId, n: 0 })

    // Create two different submission contents
    const submissionContentA = 'This is submission A containing the required phrase'
    const submissionContentB = 'This is submission B containing the required phrase'

    // 1. Upload submission A
    const resultA1 = await uploadSubmission({ testId, n: 0 }, submissionContentA)
    expect(resultA1.isValid).toBe(true)
    invariant(resultA1.isValid)

    // 2. Upload submission B
    const resultB1 = await uploadSubmission({ testId, n: 0 }, submissionContentB)
    expect(resultB1.isValid).toBe(true)
    invariant(resultB1.isValid)
    expect(resultB1.changed).toBe(true)
    expect(resultB1.id).not.toBe(resultA1.id)

    // 3. Upload submission B again
    const resultB2 = await uploadSubmission({ testId, n: 0 }, submissionContentB)
    expect(resultB2.isValid).toBe(true)
    invariant(resultB2.isValid)
    expect(resultB2.changed).toBe(false)
    expect(resultB2.id).toBe(resultB1.id)

    // 4. Upload submission A again
    const resultA2 = await uploadSubmission({ testId, n: 0 }, submissionContentA)
    expect(resultA2.isValid).toBe(true)
    invariant(resultA2.isValid)
    expect(resultA2.changed).toBe(true)
    expect(resultA2.id).not.toBe(resultA1.id)
    expect(resultA2.id).not.toBe(resultB1.id)

    // Verify that the underlying text for A1 and A2 are the same
    const snapshotA1 = await prisma.peaSubmissionSnapshot.findUnique({ where: { id: resultA1.id } })
    const snapshotA2 = await prisma.peaSubmissionSnapshot.findUnique({ where: { id: resultA2.id } })
    invariant(snapshotA1 && snapshotA2)
    expect(snapshotA1.textId).toBe(snapshotA2.textId)
  })
})
