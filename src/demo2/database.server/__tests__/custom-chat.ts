import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { createOrUpdateProblem } from '../problem'
import { createTest, createTestFromDynamicProblems, getProblemProgressId } from '../test'
import { ProblemInput } from '../../models/problem'
import { CustomChatInput } from '../../models/custom-chat'

describe('Custom Chat Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  function generateProblemInput(customChats?: CustomChatInput[]): ProblemInput {
    const problemId = crypto.randomUUID()
    return {
      id: problemId,
      axiiaMetadata: {
        problemId,
        initialMessage: 'Initial message for the test problem',
        prompt: {
          content: 'Prompt text for the test problem',
          replacementKeys: ['key'],
        },
      },
      botPresetList: [],
      materialList: [],
      submissionSpec: {
        problemId,
        requiredPhrases: [],
        maxLength: 1000,
        prefill: '',
      },
      customChatList: customChats,
    }
  }

  function generateCustomChatInput(): CustomChatInput {
    return {
      title: `Custom Chat ${crypto.randomUUID().slice(0, 8)}`,
      prompt: `System prompt for custom chat ${crypto.randomUUID().slice(0, 8)}`,
      initialMessage: [
        {
          role: 'user',
          content: 'Initial user message',
        },
        {
          role: 'assistant',
          content: 'Initial assistant response',
        },
      ],
    }
  }

  it('should create a test with custom chats', async () => {
    await prisma.$transaction(async (tx) => {
      // Create a problem with custom chats
      const customChat1 = generateCustomChatInput()
      const customChat2 = generateCustomChatInput()
      const problemInput = generateProblemInput([customChat1, customChat2])

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, tx)

      // Create test
      const testId = await createTest([problemVersion], tx)

      // Verify the test was created
      const test = await tx.peaTest.findUniqueOrThrow({
        where: { id: testId },
      })

      expect(test).toBeDefined()
      expect(test.problems).toHaveLength(1)
    })
  })

  it('should initialize custom chats when starting a problem', async () => {
    // Create a problem with custom chats
    const customChat = generateCustomChatInput()
    customChat.initialMessage = [
      { role: 'user', content: 'First user message' },
      { role: 'assistant', content: 'First assistant response' },
      { role: 'user', content: 'Second user message' },
      { role: 'assistant', content: 'Second assistant response' },
    ]

    const problemInput = generateProblemInput([customChat])
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)

    // Create the test
    const testId = await createTestFromDynamicProblems([problemVersion.id], prisma)

    // Initialize the first problem by getting its progress ID
    const progressId = await getProblemProgressId(testId, 0)

    // Verify custom chat was created
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: {
        customChats: {
          include: {
            chat: {
              include: {
                chatRequests: true,
              },
            },
          },
        },
      },
    })

    // Should have one custom chat
    expect(progress.customChats).toHaveLength(1)

    const customChatInstance = progress.customChats[0]
    expect(customChatInstance.title).toBe(customChat.title)

    // Should have system prompt
    expect(customChatInstance.chat.systemPrompt).toBe(customChat.prompt)

    // Should have two chat requests (one for each user/assistant pair)
    expect(customChatInstance.chat.chatRequests).toHaveLength(2)

    // First chat request should contain the first user/assistant pair
    const firstRequest = customChatInstance.chat.chatRequests[0]
    expect(firstRequest.input).toBe('First user message')
    expect(firstRequest.output).toBe('First assistant response')

    // Second chat request should contain the second user/assistant pair
    const secondRequest = customChatInstance.chat.chatRequests[1]
    expect(secondRequest.input).toBe('Second user message')
    expect(secondRequest.output).toBe('Second assistant response')
  })

  it('should skip unpaired messages', async () => {
    // Create a problem with custom chats containing unpaired messages
    const customChat = generateCustomChatInput()

    // 1 complete pair, then an extra user message, then 1 more complete pair
    customChat.initialMessage = [
      { role: 'user', content: 'First user message' },
      { role: 'assistant', content: 'First assistant response' },
      { role: 'user', content: 'Unpaired user message' }, // This should be skipped
      { role: 'user', content: 'Another user message' },
      { role: 'assistant', content: 'Second assistant response' },
    ]

    const problemInput = generateProblemInput([customChat])
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)

    // Create the test
    const testId = await createTestFromDynamicProblems([problemVersion.id], prisma)

    // Initialize the first problem by getting its progress ID
    const progressId = await getProblemProgressId(testId, 0)

    // Verify custom chat was created
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: {
        customChats: {
          include: {
            chat: {
              include: {
                chatRequests: true,
              },
            },
          },
        },
      },
    })

    // Should have one custom chat
    expect(progress.customChats).toHaveLength(1)

    const customChatInstance = progress.customChats[0]

    // Due to the way messages are processed in pairs (i += 2),
    // we'll only have 1 chat request for the first valid pair.
    // The second and third messages together aren't a valid user-assistant pair,
    // and the fourth and fifth messages aren't processed because i skips directly from 0 to 2.
    expect(customChatInstance.chat.chatRequests).toHaveLength(1)

    // The only chat request should contain the first user/assistant pair
    const firstRequest = customChatInstance.chat.chatRequests[0]
    expect(firstRequest.input).toBe('First user message')
    expect(firstRequest.output).toBe('First assistant response')
  })

  it('should handle custom chats with no initial messages', async () => {
    // Create a problem with custom chats but no initial messages
    const customChat = generateCustomChatInput()
    customChat.initialMessage = [] // Empty initial messages

    const problemInput = generateProblemInput([customChat])
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)

    // Create the test
    const testId = await createTestFromDynamicProblems([problemVersion.id], prisma)

    // Initialize the first problem by getting its progress ID
    const progressId = await getProblemProgressId(testId, 0)

    // Verify custom chat was created
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: {
        customChats: {
          include: {
            chat: {
              include: {
                chatRequests: true,
              },
            },
          },
        },
      },
    })

    // Should have one custom chat
    expect(progress.customChats).toHaveLength(1)

    const customChatInstance = progress.customChats[0]
    expect(customChatInstance.title).toBe(customChat.title)

    // Should have system prompt
    expect(customChatInstance.chat.systemPrompt).toBe(customChat.prompt)

    // Should have no chat requests
    expect(customChatInstance.chat.chatRequests).toHaveLength(0)
  })

  it('should process message pairs with the proper loop increment', async () => {
    // Create a problem with specific message pairs pattern
    const customChat = generateCustomChatInput()

    // This pattern tests how the i+=2 pattern behaves
    customChat.initialMessage = [
      // First pair (will be processed)
      { role: 'user', content: 'First user message' },
      { role: 'assistant', content: 'First assistant response' },

      // Second pair (will be processed)
      { role: 'user', content: 'Second user message' },
      { role: 'assistant', content: 'Second assistant response' },

      // Third pair (will be processed)
      { role: 'user', content: 'Third user message' },
      { role: 'assistant', content: 'Third assistant response' },
    ]

    const problemInput = generateProblemInput([customChat])
    const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)

    // Create the test
    const testId = await createTestFromDynamicProblems([problemVersion.id], prisma)

    // Initialize the first problem by getting its progress ID
    const progressId = await getProblemProgressId(testId, 0)

    // Verify custom chat was created
    const progress = await prisma.peaProblemProgress.findUniqueOrThrow({
      where: { id: progressId },
      include: {
        customChats: {
          include: {
            chat: {
              include: {
                chatRequests: {
                  orderBy: {
                    createdAt: 'asc',
                  },
                },
              },
            },
          },
        },
      },
    })

    // Should have one custom chat
    expect(progress.customChats).toHaveLength(1)

    const customChatInstance = progress.customChats[0]

    // Should have all three chat requests
    expect(customChatInstance.chat.chatRequests).toHaveLength(3)

    // Verify each request contains the correct message pair
    const requests = customChatInstance.chat.chatRequests

    expect(requests[0].input).toBe('First user message')
    expect(requests[0].output).toBe('First assistant response')

    expect(requests[1].input).toBe('Second user message')
    expect(requests[1].output).toBe('Second assistant response')

    expect(requests[2].input).toBe('Third user message')
    expect(requests[2].output).toBe('Third assistant response')
  })
})
