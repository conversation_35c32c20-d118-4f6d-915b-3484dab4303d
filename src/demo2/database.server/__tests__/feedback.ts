import { PeaFeedbackRun, PrismaClient } from '@prisma/client'
import { describe, expect, it, vi } from 'vitest'
import {
  DynamicLLMInterface,
  LLMClient,
  default as getLLMClient,
} from '../../deps.server/llm-client'
import { ProblemInput } from '../../models/problem'
import {
  cancelFeedback,
  createAndWaitForFeedback,
  extractFields,
  feedbackRunStatus,
  loadOrCreateFeedback,
} from '../feedback'
import { createOrUpdateProblem } from '../problem'
import { createTest, endTest } from '../test'
import { uploadSubmission } from '../problem-progress'
import invariant from 'tiny-invariant'
import { delay } from '@common/utils'
import {
  ClockClient,
  ControllableClockInterface,
  default as getClockClient,
  SystemClockInterface,
} from '../../deps.server/clock-client'
import { loadProblemDynamicContent } from '../test-dynamic-content'

vi.mock('../../deps.server/clock-client', async () => {
  const actual = await vi.importActual('../../deps.server/clock-client')
  return {
    ...actual,
    default: vi.fn(),
  }
})

function generateProblemInput(): ProblemInput {
  const problemId = crypto.randomUUID()
  return {
    id: problemId,
    axiiaMetadata: {
      problemId,
      initialMessage: 'Initial message for the test problem',
      prompt: {
        content: 'Prompt text for the test problem',
        replacementKeys: ['key'],
      },
    },
    botPresetList: [],
    materialList: [],
    submissionSpec: {
      problemId,
      requiredPhrases: [],
      maxLength: 1000,
      prefill: 'Prefill text',
    },
    feedbackSpec: {
      problemId,
      keys: [],
      prompt: {
        content: 'Feedback prompt',
        replacementKeys: ['key'],
      },
      template: 'Feedback template',
    },
  }
}

describe('Feedback Database Tests', () => {
  beforeEach(() => {
    vi.mocked(getClockClient).mockResolvedValue(new ClockClient(new SystemClockInterface()))
  })

  describe('extractFields', () => {
    it('should extract fields correctly when all keys are present', () => {
      const output = '<key1>Value 1</key1><key2>Value 2</key2><key3>Value 3</key3>'
      const keys = ['key1', 'key2', 'key3']

      const result = extractFields(output, keys)

      expect(result).toEqual({
        status: 'ok',
        dict: {
          key1: 'Value 1',
          key2: 'Value 2',
          key3: 'Value 3',
        },
      })
    })

    it('should return errored status when some keys are missing', () => {
      const output = '<key1>Value 1</key1><key3>Value 3</key3>'
      const keys = ['key1', 'key2', 'key3']

      const result = extractFields(output, keys)

      expect(result).toEqual({
        status: 'errored',
        missingKeys: ['key2'],
      })
    })

    it('should extract the last occurrence of a key if it appears multiple times', () => {
      const output = '<key1>First Value</key1><key2>Value 2</key2><key1>Last Value</key1>'
      const keys = ['key1', 'key2']

      const result = extractFields(output, keys)

      expect(result).toEqual({
        status: 'ok',
        dict: {
          key1: 'Last Value',
          key2: 'Value 2',
        },
      })
    })

    it('should handle empty values correctly', () => {
      const output = '<key1></key1><key2>Value 2</key2>'
      const keys = ['key1', 'key2']

      const result = extractFields(output, keys)

      expect(result).toEqual({
        status: 'ok',
        dict: {
          key1: '',
          key2: 'Value 2',
        },
      })
    })

    it('should handle keys with special characters', () => {
      const output = '<key-1>Value 1</key-1><key_2>Value 2</key_2>'
      const keys = ['key-1', 'key_2']

      const result = extractFields(output, keys)

      expect(result).toEqual({
        status: 'ok',
        dict: {
          'key-1': 'Value 1',
          key_2: 'Value 2',
        },
      })
    })
  })

  describe('feedbackRunStatus', () => {
    const now = new Date()
    const baseRun: PeaFeedbackRun = {
      id: '1',
      problemId: '1',
      problemVersion: 1,
      submissionTextId: '1',
      createdAt: now,
      updatedAt: now,
      upstreamRequestId: '1',
      output: null,
      renderedResult: null,
      extractedFields: null,
      upstreamErrored: false,
      terminatedByUser: false,
      taskObservationId: null,
    }

    it('should return "complete" if renderedResultId is present', () => {
      const run: PeaFeedbackRun = {
        ...baseRun,
        output: '1',
        renderedResult: '1',
      }
      expect(feedbackRunStatus(run, now)).toBe('complete')
    })

    it('should return "errored" if outputId is present but renderedResultId is missing', () => {
      const run: PeaFeedbackRun = {
        ...baseRun,
        output: '1',
        renderedResult: null,
      }
      expect(feedbackRunStatus(run, now)).toBe('errored')
    })

    it('should return "errored" if upstreamErrored is true', () => {
      const run: PeaFeedbackRun = {
        ...baseRun,
        upstreamErrored: true,
      }
      expect(feedbackRunStatus(run, now)).toBe('errored')
    })

    it('should return "cancelled" if terminatedByUser is true', () => {
      const run: PeaFeedbackRun = {
        ...baseRun,
        terminatedByUser: true,
      }
      expect(feedbackRunStatus(run, now)).toBe('cancelled')
    })

    it('should return "errored" if last update was more than 60 seconds ago', () => {
      const oldDate = new Date(Date.now() - 61 * 1000) // 61 seconds ago
      const now = new Date()
      const run: PeaFeedbackRun = {
        ...baseRun,
        updatedAt: oldDate,
      }
      expect(feedbackRunStatus(run, now)).toBe('errored')
    })

    it('should return "in-progress" for an ongoing run', () => {
      const recentDate = new Date(Date.now() - 30 * 1000) // 30 seconds ago
      const run: PeaFeedbackRun = {
        ...baseRun,
        updatedAt: recentDate,
      }
      expect(feedbackRunStatus(run, now)).toBe('in-progress')
    })
  })

  describe('loadOrCreateFeedback', () => {
    let prisma: PrismaClient

    beforeAll(async () => {
      prisma = new PrismaClient()
      await prisma.$connect()
    })

    afterAll(async () => {
      await prisma.$disconnect()
    })

    it('should create feedback when every condition is met', async () => {
      const problemInput = generateProblemInput()
      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Dummy submission content')

      const progress = await prisma.peaProblemProgress.findFirstOrThrow({
        where: { testId },
        include: {
          submissionSnapshots: true,
        },
      })
      const latestSnapshot = progress.submissionSnapshots[progress.submissionSnapshots.length - 1]
      const [feedback, feedbackSpec] = await loadOrCreateFeedback(progressId, prisma)

      expect(feedback).not.toBeNull()
      expect(feedbackSpec).not.toBeNull()
      expect(feedback.snapshotId).toBe(latestSnapshot.id)
    })

    it('should not create feedback when the test is ended', async () => {
      const problemInput = generateProblemInput()
      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await uploadSubmission({ testId, n: 0 }, 'Dummy submission content')
      await endTest(testId)
      const result = await createAndWaitForFeedback({ testId, n: 0 })
      expect(result.type).toBe('errored')
    })

    it('should not create feedback when feedbackSpec is missing', async () => {
      const problemInput = generateProblemInput()
      problemInput.feedbackSpec = undefined // Remove feedback spec
      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      const { id: progressId } = await prisma.peaProblemProgress.findFirstOrThrow({
        where: { testId },
      })
      await uploadSubmission({ testId, n: 0 }, 'Dummy submission content')

      await expect(loadOrCreateFeedback(progressId, prisma)).rejects.toThrow(
        'The current problem does not support feedback.',
      )
    })

    it('should reuse feedback runs', async () => {
      const problemInput = generateProblemInput()
      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })

      // Upload submission A and create feedback
      await uploadSubmission({ testId, n: 0 }, 'Submission A')
      const [feedbackA1] = await loadOrCreateFeedback(progressId, prisma)

      // Manually insert a feedback run
      const snapshotA1 = await prisma.peaSubmissionSnapshot.findUniqueOrThrow({
        where: { id: feedbackA1.snapshotId },
        select: { textId: true },
      })
      const feedbackRun = await prisma.peaFeedbackRun.create({
        data: {
          createdAt: new Date(),
          updatedAt: new Date(),
          problemId: problemInput.id,
          problemVersion: problemVersion.version,
          submissionTextId: snapshotA1.textId,
        },
      })

      // Create a feedback run membership
      await prisma.peaFeedbackRunMembership.create({
        data: {
          runId: feedbackRun.id,
          feedbackId: feedbackA1.id,
        },
      })

      // Upload submission B
      await uploadSubmission({ testId, n: 0 }, 'Submission B')

      // Upload submission A again
      await uploadSubmission({ testId, n: 0 }, 'Submission A')

      const [feedbackA2] = await loadOrCreateFeedback(progressId, prisma)

      // Load feedback runs for feedbackA2
      const feedbackRunsA2 = await prisma.peaFeedbackRun.findMany({
        where: {
          feedbackMemberships: {
            some: {
              feedbackId: feedbackA2.id,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      // Check if the feedback runs for A2 match the manually created one
      expect(feedbackRunsA2).toHaveLength(1)
      expect(feedbackRunsA2[0].id).toBe(feedbackRun.id)
      expect(feedbackRunsA2[0].problemId).toBe(problemInput.id)
      expect(feedbackRunsA2[0].problemVersion).toBe(problemVersion.version)
      expect(feedbackRunsA2[0].submissionTextId).toBe(feedbackRun.submissionTextId)
    })
  })

  vi.mock('../../deps.server/llm-client', async () => {
    const actual = await vi.importActual('../../deps.server/llm-client')
    return {
      ...actual,
      default: vi.fn(),
    }
  })

  describe('createAndWaitForFeedback', () => {
    let prisma: PrismaClient

    beforeAll(async () => {
      prisma = new PrismaClient()
      await prisma.$connect()
    })

    afterAll(async () => {
      await prisma.$disconnect()
    })

    beforeEach(() => {
      vi.useRealTimers()
    })

    it('should create and wait for feedback successfully', async () => {
      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        return input.input + '\n<overall>good</overall>'
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })

      expect(feedbackResult.type).toBe('complete')
      invariant(feedbackResult.type === 'complete')

      expect(feedbackResult.output).toBeDefined()
      expect(feedbackResult.output).toContain('Feedback: good')
    })

    it('should retry and succeed on LLM failure', async () => {
      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      let attemptCount = 0
      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        attemptCount += 1
        if (attemptCount === 1) {
          throw new Error('LLM failure')
        } else {
          return input.input + '\n<overall>good</overall>'
        }
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })

      expect(attemptCount).toBe(2)
      expect(feedbackResult.type).toBe('complete')
      invariant(feedbackResult.type === 'complete')

      expect(feedbackResult.output).toBeDefined()
      expect(feedbackResult.output).toContain('Feedback: good')
    })

    it('should retry once when XML tag is missing', async () => {
      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      let attemptCount = 0
      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        attemptCount += 1
        if (attemptCount === 1) {
          return input.input // Missing XML tag
        } else {
          return input.input + '\n<overall>good</overall>'
        }
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })

      expect(attemptCount).toBe(2)
      expect(feedbackResult.type).toBe('complete')
      invariant(feedbackResult.type === 'complete')

      expect(feedbackResult.output).toBeDefined()
      expect(feedbackResult.output).toContain('Feedback: good')
    })

    it('should timeout and retry after 60 seconds', async () => {
      const clock = new ControllableClockInterface()
      vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      let attemptCount = 0
      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        attemptCount += 1
        if (attemptCount === 1) {
          await clock.sleep(120_000) // Wait for 120 seconds on first attempt
          return input.input // Missing XML tag
        } else {
          return input.input + '\n<overall>good</overall>'
        }
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      await Promise.all([
        // Here we push forward the test via await createAndWaitForFeedback
        (async () => {
          const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })
          expect(attemptCount).toBe(2)
          expect(feedbackResult.type).toBe('complete')
          invariant(feedbackResult.type === 'complete')

          expect(feedbackResult.output).toBeDefined()
          expect(feedbackResult.output).toContain('Feedback: good')
        })(),
        // Here we control time
        (async () => {
          const now = clock.currentTime().getTime()
          const after60s = now + 60_000
          // This loop is extremely evil. We loop to wait for a task that needs to sleep 60 seconds.
          // This task must be the task that is waiting for LLM to resolve.
          // Once this task is registered, we are good to move forward time.
          for (let i = 0; i < 1000; i += 1) {
            await delay(10 * i)
            let found = false
            for (const promise of clock.sleepPromises) {
              if (promise.resolveTime + 1000 >= after60s) {
                found = true
              }
            }
            if (found) {
              break
            }
          }
          clock.step(70_000)
        })(),
      ])
    })

    it('should return error after three failed attempts', async () => {
      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      let attemptCount = 0
      const mockLLMInterface = new DynamicLLMInterface(async () => {
        attemptCount += 1
        throw new Error('LLM failure')
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })

      expect(attemptCount).toBe(3)
      expect(feedbackResult.type).toBe('errored')
      invariant(feedbackResult.type === 'errored')

      expect(feedbackResult.id).toBeDefined()
      expect(feedbackResult.snapshotId).toBeDefined()
    })

    it('should instantiate prompt correctly', async () => {
      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: [],
        prompt: {
          content: 'Provide feedback for the following submission: {{submission}}',
          replacementKeys: ['submission'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        return input.input
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      await createAndWaitForFeedback({ testId, n: 0 })

      const latestFeedbackRun = await prisma.peaFeedbackRun.findFirst({
        where: {
          feedbackMemberships: {
            some: {
              feedback: {
                snapshot: {
                  progressId,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      expect(latestFeedbackRun).toBeDefined()
      expect(latestFeedbackRun?.output).toBeDefined()

      if (latestFeedbackRun?.output) {
        expect(latestFeedbackRun?.output).toBe(
          'Provide feedback for the following submission: Test submission',
        )
      }
    })

    it('should support feedback cancellation and task did not timeout', async () => {
      const clock = new ControllableClockInterface()
      vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        await clock.sleep(20_000) // Wait for 20 seconds
        return input.input + '\n<overall>good</overall>'
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      await Promise.all([
        // Here we push forward the test via await createAndWaitForFeedback
        (async () => {
          const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })
          expect(feedbackResult.type).toBe('cancelled')
        })(),
        // Here we control time
        (async () => {
          const now = clock.currentTime().getTime()
          const after60s = now + 60_000
          // This loop is extremely evil. We loop to wait for a task that needs to sleep 60 seconds.
          // This task must be the task that is waiting for LLM to resolve.
          // Once this task is registered, we are good to move forward time.
          for (let i = 0; i < 1000; i += 1) {
            await delay(10 * i)
            let found = false
            for (const promise of clock.sleepPromises) {
              if (promise.resolveTime + 1000 >= after60s) {
                found = true
              }
            }
            if (found) {
              break
            }
          }
          clock.step(10_000)
          await cancelFeedback(progressId)
          clock.step(10_000)
        })(),
      ])
    })

    it('should support feedback cancellation and task did timeout', async () => {
      const clock = new ControllableClockInterface()
      vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        await clock.sleep(120_000) // Wait for 120 seconds
        return input.input + '\n<overall>good</overall>'
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      await Promise.all([
        // Here we push forward the test via await createAndWaitForFeedback
        (async () => {
          const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })
          expect(feedbackResult.type).toBe('cancelled')
        })(),
        // Here we control time
        (async () => {
          const now = clock.currentTime().getTime()
          const after60s = now + 60_000
          // This loop is extremely evil. We loop to wait for a task that needs to sleep 60 seconds.
          // This task must be the task that is waiting for LLM to resolve.
          // Once this task is registered, we are good to move forward time.
          for (let i = 0; i < 1000; i += 1) {
            await delay(10 * i)
            let found = false
            for (const promise of clock.sleepPromises) {
              if (promise.resolveTime + 1000 >= after60s) {
                found = true
              }
            }
            if (found) {
              break
            }
          }
          clock.step(10_000)
          await cancelFeedback(progressId)
          clock.step(60_000)
        })(),
      ])
    })

    it('can perform feedback run after a feedback cancellation', async () => {
      const clock = new ControllableClockInterface()
      vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

      const problemInput = generateProblemInput()

      const feedbackSpec = {
        problemId: problemInput.id,
        keys: ['overall'],
        prompt: {
          content: 'Provide feedback',
          replacementKeys: ['key'],
        },
        template: 'Feedback: {{overall}}',
      }

      problemInput.feedbackSpec = feedbackSpec

      let shouldSleep = true
      const mockLLMInterface = new DynamicLLMInterface(async (input) => {
        if (shouldSleep) {
          await clock.sleep(20_000) // Wait for 20 seconds
        }
        return input.input + '\n<overall>good</overall>'
      })

      vi.mocked(getLLMClient).mockResolvedValue(new LLMClient(mockLLMInterface))

      const { version: problemVersion } = await createOrUpdateProblem(problemInput, prisma)
      const testId = await createTest([problemVersion], prisma)
      const { progressId } = await loadProblemDynamicContent({ testId, n: 0 })
      await uploadSubmission({ testId, n: 0 }, 'Test submission')

      await Promise.all([
        // Here we push forward the test via await createAndWaitForFeedback
        (async () => {
          const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })
          expect(feedbackResult.type).toBe('cancelled')
        })(),
        // Here we control time
        (async () => {
          const now = clock.currentTime().getTime()
          const after60s = now + 60_000
          // This loop is extremely evil. We loop to wait for a task that needs to sleep 60 seconds.
          // This task must be the task that is waiting for LLM to resolve.
          // Once this task is registered, we are good to move forward time.
          for (let i = 0; i < 1000; i += 1) {
            await delay(10 * i)
            let found = false
            for (const promise of clock.sleepPromises) {
              if (promise.resolveTime + 1000 >= after60s) {
                found = true
              }
            }
            if (found) {
              break
            }
          }
          clock.step(10_000)
          await cancelFeedback(progressId)
          clock.step(10_000)
        })(),
      ])

      shouldSleep = false
      const feedbackResult = await createAndWaitForFeedback({ testId, n: 0 })
      expect(feedbackResult.type).toBe('complete')
      invariant(feedbackResult.type === 'complete')
      expect(feedbackResult.output).toBe('Feedback: good')
    })
  })
})
