import { describe, it, expect, vi, beforeAll, afterAll } from 'vitest'
import { PeaChatRequest, PrismaClient } from '@prisma/client'
import {
  isChatRequestComplete,
  createChat,
  loadChat,
  createChatRequest,
  loadChatRequestContent,
  getChatRequestStreamingValue,
  terminateChatRequest,
} from '../chat'
import {
  ChatInput,
  DynamicChatInterface,
  StreamChatClient,
  default as getStreamChatClient,
} from '../../deps.server/stream-chat-client'
import {
  ClockClient,
  ControllableClockInterface,
  default as getClockClient,
  SystemClockInterface,
} from '../../deps.server/clock-client'
import { delay } from '@common/utils'
import invariant from 'tiny-invariant'

vi.mock('../../deps.server/stream-chat-client', async () => {
  const actual = await vi.importActual('../../deps.server/stream-chat-client')
  return {
    ...actual,
    default: vi.fn(),
  }
})

vi.mock('../../deps.server/clock-client', async () => {
  const actual = await vi.importActual('../../deps.server/clock-client')
  return {
    ...actual,
    default: vi.fn(),
  }
})

class StreamChatChannel {
  private resolve?: (value: string | null) => void
  private reject?: (error: Error) => void

  post(value: string) {
    if (this.resolve) {
      this.resolve(value)
      this.resolve = undefined
      this.reject = undefined
    }
  }

  postError(error: Error) {
    if (this.reject) {
      this.reject(error)
      this.resolve = undefined
      this.reject = undefined
    }
  }

  close() {
    if (this.resolve) {
      this.resolve(null)
      this.resolve = undefined
      this.reject = undefined
    }
  }

  createStream(): AsyncIterableIterator<string> {
    const stream = async function* (self: StreamChatChannel) {
      while (true) {
        const result = await new Promise<string | null>((resolve, reject) => {
          self.resolve = resolve
          self.reject = reject
        })
        if (result === null) {
          break
        }
        yield result
      }
    }
    return stream(this)
  }
}

async function waitChatRequestToComplete(id: string, client: PrismaClient, now?: () => Date) {
  while (true) {
    const chatRequest = await client.peaChatRequest.findUniqueOrThrow({ where: { id } })
    if (isChatRequestComplete(chatRequest, now ? now() : new Date())) {
      return
    }
    await delay(10)
  }
}

describe('Chat Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  beforeEach(() => {
    vi.mocked(getStreamChatClient).mockReset()
    vi.mocked(getClockClient).mockResolvedValue(new ClockClient(new SystemClockInterface()))
    vi.useRealTimers()
  })

  it('should create an empty chat and retrieve it', async () => {
    const systemPrompt = 'Test system prompt'

    const createdChatId = await createChat(systemPrompt, prisma)
    const loadedChat = await loadChat(createdChatId, prisma)

    expect(loadedChat).toBeTruthy()
    expect(loadedChat?.id).toBe(createdChatId)
    expect(loadedChat?.chatRequests).toEqual([])
  })

  it('should correctly pass the system prompt and chat history', async () => {
    const systemPrompt = 'Test system prompt'
    const userMessage1 = 'Hello, AI!'
    const userMessage2 = 'Hello, AI again!'
    const userMessage3 = 'This message will error'
    const userMessage4 = 'Hello after error'

    const chatInterface = DynamicChatInterface.pure((input) => JSON.stringify(input))
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const createdChatId = await createChat(systemPrompt, prisma)

    // First request
    const chatRequestId1 = await createChatRequest(createdChatId, userMessage1, prisma)
    await waitChatRequestToComplete(chatRequestId1, prisma)

    const chatRequestResult1 = await loadChatRequestContent(chatRequestId1, prisma)
    expect(chatRequestResult1).toBeTruthy()
    expect(chatRequestResult1?.type).toEqual('complete')
    invariant(chatRequestResult1 && chatRequestResult1.type === 'complete')

    const chatInput1 = JSON.parse(chatRequestResult1.output) as ChatInput
    expect(chatInput1.messages).toHaveLength(2)
    expect(chatInput1.messages[0]).toEqual({ role: 'system', content: systemPrompt })
    expect(chatInput1.messages[1]).toEqual({ role: 'user', content: userMessage1 })

    // Second request
    const chatRequestId2 = await createChatRequest(createdChatId, userMessage2, prisma)
    await waitChatRequestToComplete(chatRequestId2, prisma)

    const chatRequestResult2 = await loadChatRequestContent(chatRequestId2, prisma)
    expect(chatRequestResult2).toBeTruthy()
    expect(chatRequestResult2?.type).toEqual('complete')
    invariant(chatRequestResult2 && chatRequestResult2.type === 'complete')

    const chatInput2 = JSON.parse(chatRequestResult2.output) as ChatInput
    expect(chatInput2.messages).toHaveLength(4)
    expect(chatInput2.messages[0]).toEqual({ role: 'system', content: systemPrompt })
    expect(chatInput2.messages[1]).toEqual({ role: 'user', content: userMessage1 })
    expect(chatInput2.messages[2]).toEqual({
      role: 'assistant',
      content: chatRequestResult1.output,
    })
    expect(chatInput2.messages[3]).toEqual({ role: 'user', content: userMessage2 })

    // Third request (with error)
    const errorChatInterface = new DynamicChatInterface(() => {
      const stream = async function* () {
        yield ''
        throw new Error('Simulated error')
      }
      return stream()
    })
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(errorChatInterface))

    const chatRequestId3 = await createChatRequest(createdChatId, userMessage3, prisma)
    await waitChatRequestToComplete(chatRequestId3, prisma)

    const chatRequestResult3 = await loadChatRequestContent(chatRequestId3, prisma)
    expect(chatRequestResult3).toBeTruthy()
    expect(chatRequestResult3?.type).toEqual('errored')

    // Reset mock to original chat interface
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    // Fourth request
    const chatRequestId4 = await createChatRequest(createdChatId, userMessage4, prisma)
    await waitChatRequestToComplete(chatRequestId4, prisma)

    const chatRequestResult4 = await loadChatRequestContent(chatRequestId4, prisma)
    expect(chatRequestResult4).toBeTruthy()
    expect(chatRequestResult4?.type).toEqual('complete')
    invariant(chatRequestResult4 && chatRequestResult4.type === 'complete')

    const chatInput4 = JSON.parse(chatRequestResult4.output) as ChatInput
    expect(chatInput4.messages).toHaveLength(6)
    expect(chatInput4.messages[0]).toEqual({ role: 'system', content: systemPrompt })
    expect(chatInput4.messages[1]).toEqual({ role: 'user', content: userMessage1 })
    expect(chatInput4.messages[2]).toEqual({
      role: 'assistant',
      content: chatRequestResult1.output,
    })
    expect(chatInput4.messages[3]).toEqual({ role: 'user', content: userMessage2 })
    expect(chatInput4.messages[4]).toEqual({
      role: 'assistant',
      content: chatRequestResult2.output,
    })
    expect(chatInput4.messages[5]).toEqual({ role: 'user', content: userMessage4 })
  })

  it('should correctly load a chat with multiple requests', async () => {
    let callCount = 0
    let externalResolve: () => void = () => {}
    const externalPromise = new Promise<void>((resolve) => {
      externalResolve = resolve
    })

    const chatInterface = new DynamicChatInterface(() => {
      callCount++
      const stream = async function* () {
        if (callCount === 1) {
          yield 'This is a constant response'
        } else if (callCount === 2) {
          throw new Error('Simulated error')
        } else {
          await externalPromise
          yield 'This is the response after waiting'
        }
      }
      return stream()
    })

    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const systemPrompt = 'You are a helpful assistant.'
    const createdChatId = await createChat(systemPrompt, prisma)

    // First request
    const chatRequestId1 = await createChatRequest(createdChatId, 'Hello', prisma)
    await waitChatRequestToComplete(chatRequestId1, prisma)

    // Second request (will error)
    const chatRequestId2 = await createChatRequest(createdChatId, 'This will error', prisma)
    await waitChatRequestToComplete(chatRequestId2, prisma)

    // Third request (will wait for external promise)
    const chatRequestId3 = await createChatRequest(createdChatId, 'This will wait', prisma)

    // Load the chat without waiting for the third request
    const loadedChat = await loadChat(createdChatId, prisma)

    expect(loadedChat).toBeDefined()
    expect(loadedChat?.id).toBe(createdChatId)
    expect(loadedChat?.chatRequests).toHaveLength(3)

    expect(loadedChat?.chatRequests[0].type).toBe('complete')
    expect(loadedChat?.chatRequests[1].type).toBe('errored')
    expect(loadedChat?.chatRequests[2].type).toBe('in-progress')

    // Resolve the external promise
    externalResolve()
    await waitChatRequestToComplete(chatRequestId3, prisma)

    // Load the chat again after all requests are complete
    const finalLoadedChat = await loadChat(createdChatId, prisma)
    expect(finalLoadedChat?.chatRequests[2].type).toBe('complete')
  })

  it('should throw an error when trying to create a concurrent chat request', async () => {
    let externalResolve: () => void = () => {}
    const externalPromise = new Promise<void>((resolve) => {
      externalResolve = resolve
    })

    const chatInterface = new DynamicChatInterface(async function* () {
      await externalPromise
      yield 'Response after external resolution'
    })

    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const systemPrompt = 'You are a helpful assistant'
    const createdChatId = await createChat(systemPrompt, prisma)

    // Start the first chat request
    const chatRequestId1 = await createChatRequest(createdChatId, 'First message', prisma)

    // Try to start a second chat request before the first one completes
    await expect(async () => {
      await createChatRequest(createdChatId, 'Second message', prisma)
    }).rejects.toThrow('Another chat request with id')

    // Resolve the external promise
    externalResolve()

    // Wait for the first request to complete
    await waitChatRequestToComplete(chatRequestId1, prisma)

    // Now we should be able to create a new chat request
    const chatRequestId3 = await createChatRequest(createdChatId, 'Third message', prisma)

    // Wait for the third request to complete
    await waitChatRequestToComplete(chatRequestId3, prisma)

    // Verify the chat state
    const loadedChat = await loadChat(createdChatId, prisma)
    expect(loadedChat?.chatRequests).toHaveLength(2)
    expect(loadedChat?.chatRequests[0].type).toBe('complete')
    expect(loadedChat?.chatRequests[1].type).toBe('complete')
  })

  it('should handle chat request creation based on time elapsed', async () => {
    const systemPrompt = 'You are a helpful assistant'

    let externalResolve: () => void = () => {}
    const externalPromise = new Promise<void>((resolve) => {
      externalResolve = resolve
    })

    const chatInterface1 = new DynamicChatInterface(async function* () {
      await externalPromise
      yield 'Response after external resolution'
    })
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface1))

    const createdChatId = await createChat(systemPrompt, prisma)

    const clock = new ControllableClockInterface()
    vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

    // Create the first chat request
    const chatRequestId1 = await createChatRequest(createdChatId, 'First message', prisma)

    // Advance time by 15 seconds
    clock.step(15 * 1000)

    // Try to create a second chat request, which should fail
    await expect(async () => {
      await createChatRequest(createdChatId, 'Second message', prisma)
    }).rejects.toThrow('Another chat request with id')

    const chatInterface2 = DynamicChatInterface.pure(() => 'Mocked response')
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface2))

    // Advance time by 120 seconds (2 minutes)
    clock.step(120 * 1000)

    // Now we should be able to create a new chat request
    const chatRequestId2 = await createChatRequest(createdChatId, 'Third message', prisma)

    externalResolve()

    // Wait for both requests to complete
    await waitChatRequestToComplete(chatRequestId1, prisma, () => clock.currentTime())
    await waitChatRequestToComplete(chatRequestId2, prisma, () => clock.currentTime())

    // Verify the chat state
    const loadedChat = await loadChat(createdChatId, prisma)
    expect(loadedChat?.chatRequests).toHaveLength(2)
    expect(loadedChat?.chatRequests[0].type).toBe('errored')
    expect(loadedChat?.chatRequests[1].type).toBe('complete')

    const firstRequestContent = await loadChatRequestContent(chatRequestId1, prisma)
    expect(firstRequestContent?.type).toBe('errored')
  })

  describe('isChatRequestComplete', () => {
    const now = new Date()
    const baseRequest: PeaChatRequest = {
      id: '1',
      chatId: '1',
      createdAt: now,
      updatedAt: now,
      input: '1',
      output: null,
      streamingId: null,
      upstreamRequestId: null,
      upstreamErrored: false,
      terminatedByUser: false,
    }

    it('should return true if outputId is present', () => {
      const request: PeaChatRequest = {
        ...baseRequest,
        output: '1',
      }
      expect(isChatRequestComplete(request, now)).toBe(true)
    })

    it('should return true if upstreamErrored is true', () => {
      const request: PeaChatRequest = {
        ...baseRequest,
        upstreamErrored: true,
      }
      expect(isChatRequestComplete(request, now)).toBe(true)
    })

    it('should return true if last update was more than 60 seconds ago', () => {
      const oldCreatedDate = new Date(Date.now() - 120 * 1000) // 120 seconds ago
      const oldUpdatedDate = new Date(Date.now() - 61 * 1000) // 61 seconds ago
      const recentUpdatedDate = new Date(Date.now() - 30 * 1000) // 30 seconds ago
      const now = new Date()

      // Case 1: Both createdAt and updatedAt are old
      const request1: PeaChatRequest = {
        ...baseRequest,
        createdAt: oldCreatedDate,
        updatedAt: oldUpdatedDate,
      }
      expect(isChatRequestComplete(request1, now)).toBe(true)

      // Case 2: createdAt is old, but updatedAt is recent
      const request2: PeaChatRequest = {
        ...baseRequest,
        createdAt: oldCreatedDate,
        updatedAt: recentUpdatedDate,
      }
      expect(isChatRequestComplete(request2, now)).toBe(false)

      // Case 3: Both createdAt and updatedAt are the same and old
      const request3: PeaChatRequest = {
        ...baseRequest,
        createdAt: oldUpdatedDate,
        updatedAt: oldUpdatedDate,
      }
      expect(isChatRequestComplete(request3, now)).toBe(true)
    })

    it('should return false for an ongoing request', () => {
      const request: PeaChatRequest = { ...baseRequest }
      expect(isChatRequestComplete(request, now)).toBe(false)
    })
  })

  it('should correctly handle streaming via broadcasting', async () => {
    const clock = new ControllableClockInterface()
    vi.mocked(getClockClient).mockResolvedValue(new ClockClient(clock))

    const systemPrompt = 'Test system prompt'
    const userMessage = 'Hello, AI!'
    const expectedResponse = 'Hello, human!'

    const chatChannel = new StreamChatChannel()
    const chatInterface = new DynamicChatInterface(() => chatChannel.createStream())
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const createdChatId = await createChat(systemPrompt, prisma)
    const chatRequestId = await createChatRequest(createdChatId, userMessage, prisma)

    // Initial state: streaming should be in progress
    let streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: '' })

    // Send partial response
    clock.step(200)
    chatChannel.post(expectedResponse.slice(0, 5))

    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: 'Hello' })

    // Send full response
    clock.step(200)
    // await vi.advanceTimersByTimeAsync(200)
    chatChannel.post(expectedResponse.slice(5))
    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: expectedResponse })

    // Close the channel
    chatChannel.close()

    // Use timers to wait for chat request, so we must use real timers here.
    await waitChatRequestToComplete(chatRequestId, prisma)

    // Final state: streaming should be complete
    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'complete', value: expectedResponse })

    // Verify that streamingId has been removed from the database
    const chatRequest = await prisma.peaChatRequest.findUnique({ where: { id: chatRequestId } })
    expect(chatRequest?.streamingId).toBeNull()
  })

  it('should correctly handle streaming error via broadcasting', async () => {
    vi.useFakeTimers()
    const systemPrompt = 'Test system prompt'
    const userMessage = 'Hello, AI!'
    const expectedPartialResponse = 'Hello,'
    const errorMessage = 'Simulated error during streaming'

    const chatChannel = new StreamChatChannel()
    const chatInterface = new DynamicChatInterface(() => chatChannel.createStream())
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const createdChatId = await createChat(systemPrompt, prisma)
    const chatRequestId = await createChatRequest(createdChatId, userMessage, prisma)

    // Initial state: streaming should be in progress
    let streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: '' })

    // Send partial response
    await vi.advanceTimersByTimeAsync(200)
    chatChannel.post(expectedPartialResponse)

    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: expectedPartialResponse })

    // Simulate an error
    await vi.advanceTimersByTimeAsync(200)
    chatChannel.postError(new Error(errorMessage))

    // Use timers to wait for chat request, so we must use real timers here.
    vi.useRealTimers()
    await waitChatRequestToComplete(chatRequestId, prisma)

    // Final state: streaming should be complete with an error
    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'complete', value: '' })

    // Verify that streamingId has been removed from the database and the request is marked as errored
    const chatRequest = await prisma.peaChatRequest.findUnique({ where: { id: chatRequestId } })
    expect(chatRequest?.streamingId).toBeNull()
    expect(chatRequest?.upstreamErrored).toBe(true)
    // Notice that the output is cleared when there is an upstream error.
    expect(chatRequest?.output).toBeNull()
  })

  it('should correctly handle termination of chat request', async () => {
    vi.useFakeTimers()
    const systemPrompt = 'Test system prompt'
    const userMessage = 'Hello, AI!'
    const partialResponse = 'Hello, I am'
    const remainingResponse = ' an AI assistant.'

    const chatChannel = new StreamChatChannel()
    const chatInterface = new DynamicChatInterface(() => chatChannel.createStream())
    vi.mocked(getStreamChatClient).mockResolvedValue(new StreamChatClient(chatInterface))

    const createdChatId = await createChat(systemPrompt, prisma)
    const chatRequestId = await createChatRequest(createdChatId, userMessage, prisma)

    // Initial state: streaming should be in progress
    let streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: '' })

    // Send partial response
    await vi.advanceTimersByTimeAsync(200)
    chatChannel.post(partialResponse)

    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'value', value: partialResponse })

    // Terminate the chat request
    await vi.advanceTimersByTimeAsync(200)
    const terminated = await terminateChatRequest(chatRequestId)
    expect(terminated).toBe(true)

    // Try to send the remaining response (this should not be saved)
    await vi.advanceTimersByTimeAsync(200)
    chatChannel.post(remainingResponse)
    chatChannel.close()

    // Use real timers to wait for chat request to complete
    vi.useRealTimers()
    await waitChatRequestToComplete(chatRequestId, prisma)

    // Final state: streaming should be complete
    streamingValue = await getChatRequestStreamingValue(chatRequestId)
    expect(streamingValue).toEqual({ type: 'complete', value: 'Hello, I am' })

    // Verify the final state of the chat request
    const chatRequest = await prisma.peaChatRequest.findUnique({ where: { id: chatRequestId } })
    expect(chatRequest?.streamingId).toBeNull()
    expect(chatRequest?.terminatedByUser).toBe(true)

    // Verify that only the partial response was saved
    const output = await loadChatRequestContent(chatRequestId, prisma)
    expect(output?.type).toBe('complete')
    invariant(output?.type === 'complete')
    expect(output?.output).toBe(partialResponse)
  })
})
