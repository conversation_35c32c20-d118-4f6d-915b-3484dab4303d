import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { ProblemInput } from '../../models/problem'
import { createOrUpdateProblem } from '../problem'
import { getAxiiaMetadata } from '../axiia-metadata'
import { getMaterialItem, getMaterialContent } from '../materials'
import { getSubmissionSpec } from '../submission-spec'
import { getFeedbackSpec } from '../feedback-spec'

function genId(): string {
  return crypto.randomUUID()
}

function generateBaseProblemInput(): ProblemInput {
  const problemId = genId()
  return {
    id: problemId,
    axiiaMetadata: {
      problemId,
      initialMessage: 'Initial message for the problem',
      prompt: {
        content: 'Prompt text for the problem',
        replacementKeys: ['key'],
      },
    },
    botPresetList: ['preset1', 'preset2'],
    materialList: [
      {
        problemId,
        name: 'material1',
        title: 'Material 1',
        content: 'Content for Material 1',
      },
      {
        problemId,
        name: 'material2',
        title: 'Material 2',
        content: 'Content for Material 2',
      },
    ],
    submissionSpec: {
      problemId,
      requiredPhrases: ['phrase1', 'phrase2'],
      maxLength: 1000,
      prefill: 'Prefill text for submission',
    },
  }
}

describe('Problem Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a problem and retrieve its components correctly', async () => {
    const problemInput = generateBaseProblemInput()
    const problemId = problemInput.id

    await prisma.$transaction(async (tx) => {
      // Part 1: Save the problem
      const { version: savedVersion, changed } = await createOrUpdateProblem(problemInput, tx)
      expect(changed).toBe(true)

      // Retrieve the problem row to get access to the versions
      const problemRow = await tx.peaProblem.findUnique({
        where: { id_version: { id: savedVersion.id, version: savedVersion.version } },
      })
      if (!problemRow) throw new Error('Problem row not found')

      // Part 2: Check Axiia Metadata
      const axiiaMetadata = await getAxiiaMetadata(
        { problemId, version: problemRow.vAxiiaMetdata },
        tx,
      )
      expect(axiiaMetadata).toEqual({
        problemId,
        version: problemRow.vAxiiaMetdata,
        initialMessage: 'Initial message for the problem',
        prompt: 'Prompt text for the problem',
        promptReplacementKey: 'key',
      })

      // Part 3: Check Materials
      for (const material of problemInput.materialList) {
        const materialItem = await getMaterialItem(
          { problemId: material.problemId, name: material.name, version: problemRow.vMaterialList },
          tx,
        )
        expect(materialItem).toEqual({
          problemId: material.problemId,
          name: material.name,
          version: problemRow.vMaterialList,
          title: material.title,
          copyable: false,
        })

        const materialContent = await getMaterialContent(
          { problemId: material.problemId, name: material.name, version: problemRow.vMaterialList },
          tx,
        )
        expect(materialContent).toBe(material.content)
      }

      // Part 4: Check Submission Spec
      const submissionSpec = await getSubmissionSpec(
        { problemId, version: problemRow.vSubmissionSpec },
        tx,
      )
      expect(submissionSpec).toEqual({
        problemId,
        version: problemRow.vSubmissionSpec,
        requiredPhrases: ['phrase1', 'phrase2'],
        maxLength: 1000,
        prefill: 'Prefill text for submission',
      })
    })
  })

  it('should save and retrieve a problem with feedback spec', async () => {
    const problemInput = generateBaseProblemInput()

    problemInput.feedbackSpec = {
      problemId: problemInput.id,
      keys: ['feedback1', 'feedback2'],
      prompt: {
        content: 'Feedback prompt text',
        replacementKeys: ['key'],
      },
      template: 'Feedback template content',
    }

    const { version } = await createOrUpdateProblem(problemInput, prisma)

    const problemRow = await prisma.peaProblem.findUnique({
      where: { id_version: { id: version.id, version: version.version } },
    })

    expect(problemRow).not.toBeNull()
    expect(problemRow?.vFeedbackSpec).not.toBeNull()
    expect(problemRow?.vFeedbackSpec).not.toBeUndefined()

    // Verify the feedback spec
    await prisma.$transaction(async (tx) => {
      const feedbackSpec = await getFeedbackSpec(
        { problemId: version.id, version: problemRow!.vFeedbackSpec! },
        tx,
      )
      expect(feedbackSpec).toEqual({
        problemId: version.id,
        version: problemRow!.vFeedbackSpec,
        keys: ['feedback1', 'feedback2'],
        template: 'Feedback template content',
        prompt: 'Feedback prompt text',
        promptReplacementKey: 'key',
      })
    })
  })

  describe('Skipping update scenarios', () => {
    it('should not update when no changes are made', async () => {
      const problemInput = generateBaseProblemInput()

      await createOrUpdateProblem(problemInput, prisma)
      const { changed } = await createOrUpdateProblem(problemInput, prisma)
      expect(changed).toBe(false)
    })

    it('should not update even with a feedbackSpec', async () => {
      const problemInput = generateBaseProblemInput()
      problemInput.feedbackSpec = {
        problemId: problemInput.id,
        keys: ['feedback1', 'feedback2'],
        prompt: {
          content: 'Feedback prompt text',
          replacementKeys: ['key'],
        },
        template: 'Feedback template content',
      }

      await createOrUpdateProblem(problemInput, prisma)
      const { changed } = await createOrUpdateProblem(problemInput, prisma)
      expect(changed).toBe(false)
    })
  })

  describe('Change detection scenarios', () => {
    it('should detect changes in axiiaMetadata', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        axiiaMetadata: {
          ...baseProblemInput.axiiaMetadata,
          initialMessage: 'Updated initial message',
        },
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).not.toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
    })

    it('should detect changes in botPresetList', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        botPresetList: ['preset1', 'preset2', 'preset3'],
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).not.toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
    })

    it('should detect changes in materialList', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        materialList: [
          ...baseProblemInput.materialList,
          {
            problemId: baseProblemInput.id,
            name: 'material3',
            title: 'Material 3',
            content: 'Content for Material 3',
          },
        ],
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).not.toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
    })

    it('should detect changes when materialList order is shuffled', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        materialList: [baseProblemInput.materialList[1], baseProblemInput.materialList[0]],
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).not.toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
    })

    it('should detect changes when material content is updated but order remains the same', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        materialList: [
          { ...baseProblemInput.materialList[0], content: 'Updated content for Material 1' },
          { ...baseProblemInput.materialList[1], content: 'Updated content for Material 2' },
        ],
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).not.toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
    })

    it('should detect changes in submissionSpec', async () => {
      const baseProblemInput = generateBaseProblemInput()

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        submissionSpec: {
          ...baseProblemInput.submissionSpec,
          maxLength: 1500,
          requiredPhrases: ['phrase1', 'phrase2', 'phrase3'],
        },
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).not.toBe(initialVersion.version)
    })

    it('should detect changes in feedbackSpec', async () => {
      const baseProblemInput = generateBaseProblemInput()
      baseProblemInput.feedbackSpec = {
        problemId: baseProblemInput.id,
        keys: ['feedback1', 'feedback2'],
        prompt: {
          content: 'Feedback prompt text',
          replacementKeys: ['key'],
        },
        template: 'Feedback template content',
      }

      const { version: initialVersion } = await createOrUpdateProblem(baseProblemInput, prisma)

      const updatedProblemInput = {
        ...baseProblemInput,
        feedbackSpec: {
          problemId: baseProblemInput.id,
          keys: ['newKey1', 'newKey2'],
          prompt: {
            content: 'Updated prompt text',
            replacementKeys: ['key'],
          },
          template: 'Updated template',
        },
      }

      const { version: updatedVersion, changed } = await createOrUpdateProblem(
        updatedProblemInput,
        prisma,
      )

      expect(changed).toBe(true)
      expect(updatedVersion.version).toBe(initialVersion.version + 1)

      const problemRow = await prisma.peaProblem.findUnique({
        where: { id_version: { id: updatedVersion.id, version: updatedVersion.version } },
      })

      expect(problemRow?.vAxiiaMetdata).toBe(initialVersion.version)
      expect(problemRow?.vBotPresetList).toBe(initialVersion.version)
      expect(problemRow?.vMaterialList).toBe(initialVersion.version)
      expect(problemRow?.vSubmissionSpec).toBe(initialVersion.version)
      expect(problemRow?.vFeedbackSpec).not.toBe(initialVersion.version)
    })
  })
})
