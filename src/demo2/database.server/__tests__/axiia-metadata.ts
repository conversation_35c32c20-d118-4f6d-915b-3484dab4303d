import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import {
  createOrUpdateAxiiaMetadata,
  getAxiiaMetadata,
  resolveLatestAxiiaMetadataVersion,
} from '../axiia-metadata'

function genId(): string {
  return crypto.randomUUID()
}

describe('Axiia Metadata Database Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('should save a metadata record and retrieve it', async () => {
    const problemId = genId()
    const initialMessage = 'Initial message content'
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateAxiiaMetadata(
        { problemId, initialMessage, prompt },
        tx,
      )

      expect(changed).toBe(true)

      const retrievedMetadata = await getAxiiaMetadata(savedVersion, tx)
      expect(retrievedMetadata).toEqual({
        problemId,
        version: savedVersion.version,
        initialMessage: 'Initial message content',
        prompt: 'Prompt text content',
        promptReplacementKey: 'key',
      })
    })
  })

  it('should update a metadata record when saving with the same problemId', async () => {
    const problemId = genId()
    const initialMessage = 'Initial message content'
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    const updatedMessage = 'Updated message content'
    const updatedPrompt = { content: 'Updated prompt text', replacementKeys: ['new-key'] }
    await prisma.$transaction(async (tx) => {
      const { version: initialVersion, changed: initialChanged } =
        await createOrUpdateAxiiaMetadata({ problemId, initialMessage, prompt }, tx)
      expect(initialChanged).toBe(true)

      const { version: updatedVersion, changed: updatedChanged } =
        await createOrUpdateAxiiaMetadata(
          { problemId, initialMessage: updatedMessage, prompt: updatedPrompt },
          tx,
        )
      expect(updatedChanged).toBe(true)

      expect(updatedVersion.version).toBeGreaterThan(initialVersion.version)
      const retrievedInitialMetadata = await getAxiiaMetadata(initialVersion, tx)
      expect(retrievedInitialMetadata).toEqual({
        problemId,
        version: initialVersion.version,
        initialMessage: 'Initial message content',
        prompt: 'Prompt text content',
        promptReplacementKey: 'key',
      })

      const retrievedUpdatedMetadata = await getAxiiaMetadata(updatedVersion, tx)
      expect(retrievedUpdatedMetadata).toEqual({
        problemId,
        version: updatedVersion.version,
        initialMessage: 'Updated message content',
        prompt: 'Updated prompt text',
        promptReplacementKey: 'new-key',
      })
    })
  })

  it('should resolve the latest version of a metadata record', async () => {
    const problemId = genId()
    const initialMessage = 'Initial message content'
    const prompt = { content: 'Prompt text content', replacementKeys: ['key'] }
    await prisma.$transaction(async (tx) => {
      const { version: savedVersion, changed } = await createOrUpdateAxiiaMetadata(
        { problemId, initialMessage, prompt },
        tx,
      )
      expect(changed).toBe(true)

      const latestVersion = await resolveLatestAxiiaMetadataVersion(problemId, tx)
      expect(latestVersion).toEqual({ problemId, version: savedVersion.version })
    })
  })

  it('should not create a new version when passing the same input', async () => {
    const problemId = genId()
    const initialMessage = 'Test message'
    const prompt = { content: 'Test prompt', replacementKeys: ['key1'] }
    const input = { problemId, initialMessage, prompt }

    await prisma.$transaction(async (tx) => {
      // Create initial version
      const { version: initialVersion, changed: initialChanged } =
        await createOrUpdateAxiiaMetadata(input, tx)

      expect(initialChanged).toBe(true)

      // Try to create/update with the same input
      const { version: updatedVersion, changed: updatedChanged } =
        await createOrUpdateAxiiaMetadata(input, tx)

      expect(updatedChanged).toBe(false)
      expect(updatedVersion).toEqual(initialVersion)
    })
  })
})
