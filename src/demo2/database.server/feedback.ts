import logger from '@common/deps.server/logger'
import getPrismaClient from '@deps/prisma-client'
import { PeaFeedback, PeaFeedbackRun, PeaFeedbackSpec, Prisma, PrismaClient } from '@prisma/client'
import invariant from 'tiny-invariant'
import get<PERSON><PERSON><PERSON>lient from '../deps.server/clock-client'
import getLLMClient, { LLMResult } from '../deps.server/llm-client'
import getTaskObservationClient from '../deps.server/task-observation-client'
import { FeedbackResult } from '../models/feedback'
import { renderNunjucksTemplate } from '../models/nunjucks'
import { getProblemProgressId } from './test'
import { TestProblemIndex } from '../components/common'

export async function loadOrCreateFeedback(
  progressId: string,
  client: PrismaClient,
): Promise<[PeaFeedback, PeaFeedbackSpec]> {
  const clockClient = await getClockClient()

  return await client.$transaction(
    async (tx) => {
      // Check if the progress is valid
      const progress = await tx.peaProblemProgress.findUniqueOrThrow({
        where: { id: progressId },
        include: {
          resolvedProblem: {
            include: {
              problem: {
                include: {
                  feedbackSpec: true,
                },
              },
            },
          },
          submissionSnapshots: {
            orderBy: { timestamp: 'desc' },
            take: 1,
            include: { feedback: true },
          },
        },
      })

      const { feedbackSpec } = progress.resolvedProblem.problem
      if (feedbackSpec === null) {
        throw new Error('The current problem does not support feedback.')
      }

      // Get the latest snapshot
      if (progress.submissionSnapshots.length === 0) {
        throw new Error('No submission snapshot found')
      }
      const latestSnapshot = progress.submissionSnapshots[0]

      // If the snapshot already has feedback, return it
      if (latestSnapshot.feedback) {
        return [latestSnapshot.feedback, feedbackSpec]
      }

      // Create new feedback
      const newFeedback = await tx.peaFeedback.create({
        data: {
          timestamp: clockClient.currentTime(),
          snapshotId: latestSnapshot.id,
        },
      })

      // Load all past snapshots with the same submission
      const pastSnapshots = await tx.peaSubmissionSnapshot.findMany({
        where: {
          progressId: progressId,
          textId: latestSnapshot.textId,
        },
        orderBy: { timestamp: 'desc' },
        take: 1,
        // We need to skip the latest snapshot.
        skip: 1,
        include: {
          feedback: {
            include: {
              feedbackMemberships: {
                include: {
                  run: true,
                },
              },
            },
          },
        },
      })

      // Find the latest snapshot with feedback
      if (pastSnapshots.length === 0) {
        return [newFeedback, feedbackSpec]
      }
      const latestSnapshotWithFeedback = pastSnapshots[0]

      if (latestSnapshotWithFeedback.feedback) {
        // Copy feedback runs from the latest snapshot with feedback
        const feedbackRunsToCreate = latestSnapshotWithFeedback.feedback.feedbackMemberships.map(
          (membership) => ({
            runId: membership.runId,
            feedbackId: newFeedback.id,
          }),
        )

        if (feedbackRunsToCreate.length > 0) {
          await tx.peaFeedbackRunMembership.createMany({
            data: feedbackRunsToCreate,
          })
        }
      }

      return [newFeedback, feedbackSpec]
    },
    { isolationLevel: 'Serializable' },
  )
}

export type FeedbackRunStatus = 'complete' | 'errored' | 'cancelled' | 'in-progress'

export function feedbackRunStatus(run: PeaFeedbackRun, now: Date): FeedbackRunStatus {
  if (run.upstreamErrored) {
    return 'errored'
  }
  if (run.renderedResult) {
    return 'complete'
  }
  if (run.output) {
    return 'errored'
  }
  if (run.terminatedByUser) {
    return 'cancelled'
  }
  if (now.getTime() - run.updatedAt.getTime() > 60 * 1000) {
    // No update for one minute. Consider it timed out.
    return 'errored'
  }
  return 'in-progress'
}

export async function createAndWaitForFeedback({
  testId,
  n,
}: TestProblemIndex): Promise<FeedbackResult> {
  const client = await getPrismaClient()
  const clockClient = await getClockClient()
  const requestedAt = clockClient.currentTime()

  const progressId = await getProblemProgressId(testId, n)
  const [{ id: feedbackId }, feedbackSpec] = await loadOrCreateFeedback(progressId, client)

  let lastInProgressId: string | null = null

  while (true) {
    // Load feedback at the moment, along with all feedback runs
    const feedback = await client.peaFeedback.findUniqueOrThrow({
      where: { id: feedbackId },
      include: {
        feedbackMemberships: {
          include: { run: true },
        },
        snapshot: {
          include: { progress: true },
        },
      },
    })

    const feedbackRuns = feedback.feedbackMemberships.map((membership) => membership.run)

    // Sort feedback runs from newest to oldest
    feedbackRuns.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

    if (feedbackRuns.length > 0) {
      const latestFeedbackRun = feedbackRuns[0]

      const now = clockClient.currentTime()
      const currentStatus = feedbackRunStatus(latestFeedbackRun, now)
      if (currentStatus === 'in-progress') {
        const taskObservationId = latestFeedbackRun.taskObservationId
        invariant(
          taskObservationId,
          'A feedback run in-progress should have an task observation id.',
        )
        invariant(
          lastInProgressId !== latestFeedbackRun.id,
          "A feedback run's task was marked complete but it is still in progress. This is an error.",
        )
        const client = await getTaskObservationClient()
        const now = clockClient.currentTime()
        const ttl = Math.max(1000, latestFeedbackRun.updatedAt.getTime() + 60_000 - now.getTime())
        await client.waitForTask(taskObservationId, ttl)
        lastInProgressId = taskObservationId
        continue
      }

      if (currentStatus === 'complete') {
        invariant(latestFeedbackRun.renderedResult !== null)
        return {
          type: 'complete',
          id: feedbackId,
          snapshotId: feedback.snapshotId,
          output: latestFeedbackRun.renderedResult,
        }
      }

      if (currentStatus === 'cancelled' && latestFeedbackRun.updatedAt >= requestedAt) {
        return {
          type: 'cancelled',
          id: feedbackId,
          snapshotId: feedback.snapshotId,
        }
      }

      // Count attempts so far (i.e., runs that are not cancelled)
      const attemptCount = feedbackRuns.filter(
        (run) => feedbackRunStatus(run, now) == 'errored',
      ).length

      // If attempt count is >= 3, return errored
      if (attemptCount >= 3) {
        return {
          type: 'errored',
          id: feedbackId,
          snapshotId: feedback.snapshotId,
        }
      }
    }

    await createFeedbackRun(feedback, feedbackSpec, client)
  }
}

export async function cancelFeedback(progressId: string): Promise<boolean> {
  const client = await getPrismaClient()
  const [feedback] = await loadOrCreateFeedback(progressId, client)
  const latestFeedbackRun = await client.peaFeedbackRun.findFirst({
    where: {
      feedbackMemberships: {
        some: {
          feedbackId: feedback.id,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (!latestFeedbackRun) {
    return false
  }

  const updated = await updateFeedbackRunIfNotComplete(latestFeedbackRun.id, async () => ({
    taskObservationId: null,
    terminatedByUser: true,
  }))

  return updated
}

export async function createFeedbackRun(
  feedback: PeaFeedback,
  feedbackSpec: PeaFeedbackSpec,
  client: PrismaClient,
) {
  try {
    await client.$transaction(
      async (tx) => {
        // Read the submission text and prompt associated with this feedback
        const submissionSnapshot = await tx.peaSubmissionSnapshot.findUniqueOrThrow({
          where: { id: feedback.snapshotId },
          include: { text: true },
        })

        const submissionText = submissionSnapshot.text.text

        // Perform string replacement to instantiate the prompt
        const instantiatedPrompt = renderNunjucksTemplate(feedbackSpec.prompt, {
          [feedbackSpec.promptReplacementKey]: submissionText,
        })

        // Send the instantiated prompt to LLM
        const llmClient = await getLLMClient()
        const taskObservationClient = await getTaskObservationClient()
        const taskObservationId = await taskObservationClient.startTask()

        const clockClient = await getClockClient()
        const now = clockClient.currentTime()

        // Create a new feedback run
        const feedbackRun = await tx.peaFeedbackRun.create({
          data: {
            problemId: feedbackSpec.problemId,
            problemVersion: feedbackSpec.version,
            createdAt: now,
            updatedAt: now,
            submissionTextId: submissionSnapshot.textId,
            taskObservationId,
            feedbackMemberships: {
              create: {
                feedbackId: feedback.id,
              },
            },
          },
        })

        waitFeedbackRunRequestToFinish(
          feedbackRun.id,
          feedbackSpec,
          async () => await llmClient.generateText({ input: instantiatedPrompt }),
          taskObservationId,
        )
      },
      {
        isolationLevel: 'Serializable',
      },
    )
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2034') {
      logger.info(
        'A conflict occurred when creating a feedback run. This is generally safe to ignore',
      )
      return
    }
    throw error
  }
}

/**
 * Extracts string values from the last occurrences of XML tags specified by keys in the given output string.
 *
 * @param output - The string containing XML tags to extract from.
 * @param keys - An array of keys representing the XML tags to extract.
 * @returns An object with status 'ok' and a dictionary of extracted values if successful,
 *          or status 'errored' with an array of missing keys if extraction fails.
 */
export function extractFields(
  output: string,
  keys: string[],
): { status: 'ok'; dict: Record<string, string> } | { status: 'errored'; missingKeys: string[] } {
  const result: Record<string, string> = {}
  const missingKeys: string[] = []

  for (const key of keys) {
    const regex = new RegExp(`<${key}>(.*?)</${key}>`, 'gs')
    const matches = [...output.matchAll(regex)]

    if (matches.length > 0) {
      result[key] = matches[matches.length - 1][1].trim()
    } else {
      missingKeys.push(key)
    }
  }

  if (missingKeys.length === 0) {
    return { status: 'ok', dict: result }
  } else {
    return { status: 'errored', missingKeys }
  }
}

async function waitFeedbackRunRequestToFinish(
  feedbackRunId: string,
  feedbackSpec: PeaFeedbackSpec,
  callLLMRequest: () => Promise<LLMResult>,
  taskObservationId: string,
) {
  const taskObservationClient = await getTaskObservationClient()

  try {
    const { requestId, output } = await callLLMRequest()

    await updateFeedbackRunIfNotComplete(feedbackRunId, async () => {
      const result = extractFields(output, feedbackSpec.keys)
      if (result.status === 'errored') {
        logger.error(
          `Feedback run ${feedbackRunId} misses the following key(s): ${result.missingKeys}.`,
        )
        return { taskObservationId: null, upstreamRequestId: requestId, output }
      }

      const template = feedbackSpec.template

      // Render the template with the variables from result.dict
      const renderedResult = await renderNunjucksTemplate(template, result.dict)

      return {
        taskObservationId: null,
        upstreamRequestId: requestId,
        output: output,
        renderedResult,
        extractedFields: JSON.stringify(result.dict),
      }
    })
  } catch (error) {
    logger.error(
      error,
      `An error happened when generating feedback. Feedback run id ${feedbackRunId}.`,
    )

    await updateFeedbackRunIfNotComplete(feedbackRunId, async () => ({
      taskObservationId: null,
      upstreamErrored: true,
    }))
  } finally {
    await taskObservationClient.completeTask(taskObservationId)
  }
}

async function updateFeedbackRunIfNotComplete(
  id: string,
  body: (tx: Prisma.TransactionClient, latest: PeaFeedbackRun) => Promise<Partial<PeaFeedbackRun>>,
): Promise<boolean> {
  const client = await getPrismaClient()
  const clockClient = await getClockClient()

  return await client.$transaction(
    async (tx) => {
      const row = await tx.peaFeedbackRun.findUniqueOrThrow({ where: { id } })
      if (feedbackRunStatus(row, clockClient.currentTime()) !== 'in-progress') {
        return false
      }
      const data = await body(tx, row)
      await tx.peaFeedbackRun.update({
        data: { ...data, updatedAt: clockClient.currentTime() },
        where: { id },
      })
      return true
    },
    {
      isolationLevel: Prisma.TransactionIsolationLevel.RepeatableRead,
    },
  )
}
