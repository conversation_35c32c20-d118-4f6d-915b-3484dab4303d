import { z } from 'zod'

export const submissionSpecInputSchema = z.object({
  problemId: z.string(),
  requiredPhrases: z.array(z.string()),
  maxLength: z.number().optional(),
  prefill: z.string(),
})

export type SubmissionSpecInput = z.infer<typeof submissionSpecInputSchema>

export interface SubmissionSpecVersion {
  problemId: string
  version: number
}

export type SubmissionSpec = SubmissionSpecVersion & {
  requiredPhrases: string[]
  maxLength: number | null
  prefill: string
}
