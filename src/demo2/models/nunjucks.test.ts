import { describe, expect, it } from 'vitest'
import { renderNunjucksTemplate } from './nunjucks'

describe('renderNunjucksTemplate', () => {
  it('should render a template with direct interpolation', () => {
    const template = 'Hello, {{name}}!'
    const extractedFields = { name: '<PERSON>' }

    const result = renderNunjucksTemplate(template, extractedFields)

    expect(result).toBe('Hello, John!')
  })

  it('should render a template with multiple interpolations', () => {
    const template = 'Hello, {{firstName}} {{lastName}}!'
    const extractedFields = { firstName: 'John', lastName: 'Doe' }

    const result = renderNunjucksTemplate(template, extractedFields)

    expect(result).toBe('Hello, John Doe!')
  })

  it('should handle if-else statements', () => {
    const template = '{% if score > 80 %}Great job!{% else %}Keep practicing!{% endif %}'

    const highScore = renderNunjucksTemplate(template, { score: 90 })
    const lowScore = renderNunjucksTemplate(template, { score: 70 })

    expect(highScore).toBe('Great job!')
    expect(lowScore).toBe('Keep practicing!')
  })

  it('should handle nested if-else statements', () => {
    const template = `
      {% if score > 90 %}
        Excellent!
      {% elif score > 80 %}
        Great job!
      {% else %}
        Keep practicing!
      {% endif %}
    `

    const excellentScore = renderNunjucksTemplate(template, { score: 95 })
    const greatScore = renderNunjucksTemplate(template, { score: 85 })
    const lowScore = renderNunjucksTemplate(template, { score: 75 })

    expect(excellentScore.trim()).toBe('Excellent!')
    expect(greatScore.trim()).toBe('Great job!')
    expect(lowScore.trim()).toBe('Keep practicing!')
  })

  it('should handle undefined variables', () => {
    const template = 'Hello, {{name}}!'
    const extractedFields = {}

    const result = renderNunjucksTemplate(template, extractedFields)

    expect(result).toBe('Hello, !')
  })
})
