export type DynamicChatRequest = {
  id: string
  input: string
  timestamp: number
} & (
  | {
      type: 'complete'
      output: string
    }
  | {
      type: 'in-progress'
      streamingId?: string
    }
  | {
      type: 'errored'
    }
)

export interface DynamicChat {
  id: string
  createdAt: number
  lastUpdatedAt: number
  requests: DynamicChatRequest[]
}

export type DynamicCustomChat = DynamicChat & {
  title: string
  sourcePresetId?: string
}

export type FeedbackDynamicContent = {
  submission: string
} & (
  | {
      errored: true
    }
  | { errored: false; feedback: string }
)

export type LabelPredPairTestResult = 'match' | 'mismatch' | 'no-value-extracted' | 'errored'

export interface LabelPredPairDynamicContent {
  name: string
  timestamp: number
  hidesInput: boolean
  result: LabelPredPairTestResult
  extractedOutput?: string
  output?: string
  textId: string
}

export interface LabelPredGroupListDynamicContent {
  // A nested dictionary, access by byGroup[groupName][pairName].
  byGroup: Record<string, Record<string, LabelPredPairDynamicContent>>
  // This field contains a map between textId and the resolved text.
  // Used to compare if the latest run is dirty.
  texts: Record<string, string>
}

export type ProblemSubmissions = {
  problemId: string
  submissions: {
    id: string
    timestamp: number
    content: string
  }[]
}

export interface ProblemDynamicContent {
  progressId: string
  axiiaChat: DynamicChat
  customChats: DynamicCustomChat[]

  feedback?: FeedbackDynamicContent
  labelPredGroupList?: LabelPredGroupListDynamicContent
  latestSubmission: string
  allProblemSubmissions: ProblemSubmissions[]
  allProblemFinalConfirm: ProblemFinalConfirmContent[]
}

export interface ProblemFinalConfirmContent {
  problemId: string
  progressId?: string
  hasSubmitted?: boolean
  feedback?: FeedbackDynamicContent
  labelPredGroupList?: LabelPredGroupListDynamicContent
}
