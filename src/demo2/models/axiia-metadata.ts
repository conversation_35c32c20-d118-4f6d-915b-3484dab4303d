import { z } from 'zod'
import { systemPromptInputSchema } from './system-prompt'

export const axiiaMetadataInputSchema = z.object({
  problemId: z.string(),
  initialMessage: z.string(),
  prompt: systemPromptInputSchema,
})

export type AxiiaMetadataInput = z.infer<typeof axiiaMetadataInputSchema>

export interface AxiiaMetadataVersion {
  problemId: string
  version: number
}

export type AxiiaMetadata = AxiiaMetadataVersion & {
  initialMessage: string
  prompt: string
  promptReplacementKey: string
}
