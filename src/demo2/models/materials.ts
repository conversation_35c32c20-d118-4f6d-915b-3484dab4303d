import { z } from 'zod'

export const materialInputSchema = z.object({
  problemId: z.string(),
  name: z.string(),
  title: z.string(),
  content: z.string(),
  copyable: z.boolean().optional(),
})

export type MaterialInput = z.infer<typeof materialInputSchema>

export interface MaterialVersion {
  problemId: string
  name: string
  version: number
}

export type MaterialItem = MaterialVersion & { title: string; copyable: boolean }
