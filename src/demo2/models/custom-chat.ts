import { z } from 'zod'

export const chatMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
})

export type ChatMessage = z.infer<typeof chatMessageSchema>

export const customChatInputSchema = z.object({
  title: z.string(),
  prompt: z.string(),
  initialMessage: z.array(chatMessageSchema),
})

export type CustomChatInput = z.infer<typeof customChatInputSchema>

export interface CustomChatVersion {
  problemId: string
  title: string
  version: number
}
