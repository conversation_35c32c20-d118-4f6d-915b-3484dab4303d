import { ResolvedBotPreset } from './bot-presets'
import { ProblemVersion } from './problem'

export interface MaterialStaticContent {
  name: string
  title: string
  copyable: boolean
  content: string
}

export interface SubmissionStaticContent {
  hasFeedback: boolean
  prefill: string
  requiredPhrases: string[]
}

export interface VisibleLabelPredPair {
  name: string
  input: string
  output: string
}

export interface InvisibleLabelPredPair {
  name: string
}

export type LabelPredGroupStaticContent = {
  name: string
  title: string
} & (
  | {
      hidesInput: false
      pairs: VisibleLabelPredPair[]
    }
  | {
      hidesInput: true
      pairs: InvisibleLabelPredPair[]
    }
)

export interface ProblemStaticContent {
  testId: string
  problemVersion: ProblemVersion

  axiiaInitialMessage: string
  botPresets: ResolvedBotPreset[]
  materials: MaterialStaticContent[]

  submission: SubmissionStaticContent
  labelPredGroups?: LabelPredGroupStaticContent[]
}

export type TestStaticContent = {
  allProblems: ProblemVersion[]
  isComplete: boolean
  firstProgressCreatedAt?: string
  problemSet?: {
    id: string
    expireMinutes?: number
  }
}
