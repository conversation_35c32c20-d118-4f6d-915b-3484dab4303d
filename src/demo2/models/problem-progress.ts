import { z } from 'zod'

export const customChatRequestSchema = z
  .object({
    title: z.string(),
    prompt: z.string().optional(),
    sourcePresetId: z.string().optional(),
  })
  .refine((data) => (data.prompt !== undefined) !== (data.sourcePresetId !== undefined), {
    message: "Exactly one of 'prompt' or 'sourcePresetId' must be provided",
    path: ['prompt', 'sourcePresetId'],
  })

export type CustomChatRequest = z.infer<typeof customChatRequestSchema>

export const submissionUploadRequestSchema = z.object({
  progressId: z.string(),
  content: z.string(),
})

export type SubmissionUploadRequest = z.infer<typeof submissionUploadRequestSchema>

export type SubmissionUploadResult =
  | {
      isValid: false
      missingPhrases?: string[]
      exceedingMaxLength?: boolean
      maxLength?: number
    }
  | {
      isValid: true
      changed: boolean
      id: string
      timestamp: number
      sha256: string
    }
