import { z } from 'zod'

export const LabelPredPairInputSchema = z.object({
  name: z.string(),
  input: z.string(),
  output: z.string(),
})

export const LabelPredGroupInputSchema = z.object({
  name: z.string(),
  title: z.string(),
  hidesInput: z.boolean(),
  pairs: z.array(LabelPredPairInputSchema),
})

export type LabelPredPairInput = z.infer<typeof LabelPredPairInputSchema>
export type LabelPredGroupInput = z.infer<typeof LabelPredGroupInputSchema>

export interface LabelPredPairVersion {
  id: string
  name: string
  version: number
}

export interface LabelPredGroupVersion {
  problemId: string
  name: string
  version: number
}
