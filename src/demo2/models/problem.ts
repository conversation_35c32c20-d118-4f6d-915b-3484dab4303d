import { z } from 'zod'
import { axiiaMetadataInputSchema } from './axiia-metadata'
import { materialInputSchema } from './materials'
import { submissionSpecInputSchema } from './submission-spec'
import { feedbackSpecInputSchema } from 'src/demo2/models/feedback-spec'
import { LabelPredGroupInputSchema } from './label-pred'
import { customChatInputSchema } from './custom-chat'

export const problemInputSchema = z.object({
  id: z.string(),
  axiiaMetadata: axiiaMetadataInputSchema,
  botPresetList: z.array(z.string()),
  materialList: z.array(materialInputSchema),
  submissionSpec: submissionSpecInputSchema,
  feedbackSpec: feedbackSpecInputSchema.optional(),
  labelPredGroupList: z.array(LabelPredGroupInputSchema).optional(),
  customChatList: z.array(customChatInputSchema).optional(),
})

export type ProblemInput = z.infer<typeof problemInputSchema>

export interface ProblemVersion {
  id: string
  version: number
}
