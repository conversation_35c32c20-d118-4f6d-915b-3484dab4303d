import { ResolvedBotPreset } from './bot-presets'
import { CustomChatItem } from './chat'
import { MaterialItem } from './materials'
import { ProblemVersion } from './problem'

export interface ProblemProgressIndex {
  testId: string
  problemVersion: ProblemVersion
  progressId: string
  botPresets: ResolvedBotPreset[]
  materials: MaterialItem[]
  axiiaChatId: string
  customChats: CustomChatItem[]
}

export type TestLoadResult =
  | {
      status: 'not-started'
    }
  | {
      status: 'done'
    }
  | {
      status: 'in-progress'
      index: ProblemProgressIndex
    }
