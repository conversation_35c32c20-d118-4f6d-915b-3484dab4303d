import { z } from 'zod'

export const botPresetInputSchema = z.object({
  id: z.string(),
  title: z.string(),
  prompt: z.string(),
})

export type BotPresetInput = z.infer<typeof botPresetInputSchema>

export interface BotPresetVersion {
  id: string
  version: number
}

export type BotPresetItem = BotPresetVersion & { title: string }

export interface ResolvedBotPreset {
  id: string
  version: number
  title: string
  prompt: string
}
