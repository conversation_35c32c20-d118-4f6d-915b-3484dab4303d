export interface CustomChatItem {
  id: string
  title: string
  createdAt: number
  lastUpdatedAt: number
  sourcePresetId?: string
}

export interface Chat {
  id: string
  createdAt: Date
  chatRequests: ChatRequest[]
}

export interface ChatRequest {
  id: string
  createdAt: Date
  updatedAt: Date
  type: 'complete' | 'errored' | 'in-progress'
}

export interface ChatRequestItem {
  id: string
  timestamp: number // for sorting
  status: 'complete' | 'errored' | 'in-progress'
}

export type ChatRequestContent =
  | {
      type: 'complete'
      input: string
      output: string
    }
  | {
      type: 'errored'
      input: string
    }
  | {
      type: 'in-progress'
      input: string
      streamingId?: string
    }

export type ChatStreamingMessage =
  | { type: 'complete'; value: string }
  | { type: 'error'; message: string }
  | { type: 'chunk'; patch: string }
