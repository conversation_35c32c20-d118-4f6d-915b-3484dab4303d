import { z } from 'zod'
import { systemPromptInputSchema } from './system-prompt'

export const feedbackSpecInputSchema = z.object({
  problemId: z.string(),
  keys: z.array(z.string()),
  prompt: systemPromptInputSchema,
  template: z.string(),
})

export type FeedbackSpecInput = z.infer<typeof feedbackSpecInputSchema>

export interface FeedbackSpecVersion {
  problemId: string
  version: number
}

export type FeedbackSpec = FeedbackSpecVersion & {
  keys: string[]
  prompt: string
  promptReplacementKey: string
  template: string
}
