import { <PERSON><PERSON><PERSON><PERSON> } from '@axiia-ui/components/scroll-area'
import { <PERSON>, useFetcher } from '@remix-run/react'
import clsx from 'clsx'
import { <PERSON><PERSON>, <PERSON>Che<PERSON> } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { SimpleTabItem } from '../models/tabs'
import { EditableButton } from './EditableBtn'
import { CreateBotDialog } from './bot-builder/CreateBot'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@axiia-ui/components/select'
import { useContext } from 'react'
import { ProblemContext } from './ProblemContext'
import { ProblemDynamicContent } from '../models/test-dynamic-content'
import { useAtom } from 'jotai'
import invariant from 'tiny-invariant'

interface SideBarProps {
  allTabs: SimpleTabItem[]
  activeTabs: string[]
  onTabChange: (id: string) => void
}

export function SideHeader() {
  return (
    <div className="flex h-14 items-center border-b px-4">
      <Link to="" className="flex items-center gap-2 font-semibold">
        <Bot className="h-6 w-6" />
        <span className="font-xl">Axiia</span>
      </Link>
    </div>
  )
}

interface ProbelmSelectorProps {
  dynamicContent: ProblemDynamicContent
}

export function ProblemSelector({ dynamicContent }: ProbelmSelectorProps) {
  const { problem, allProblems, solutionAtom } = useContext(ProblemContext)
  const [solution] = useAtom(solutionAtom)
  const { t } = useTranslation('pea')

  const fetcher = useFetcher()

  const handleProblemChange = (problemId: string) => {
    const index = allProblems.findIndex((x) => x.id === problemId)
    invariant(index !== -1)

    const form = new FormData()
    form.append('action', 'submit')
    form.append('submission', solution)
    form.append('newProblemIndex', String(index))
    fetcher.submit(form, {
      method: 'POST',
    })
  }

  return (
    <div className="font-medium">
      <SectionHeader title={t('SwitchTopic')}></SectionHeader>
      <div className="mt-3">
        <Select value={problem.id} onValueChange={handleProblemChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="choose problem" />
          </SelectTrigger>
          <SelectContent>
            {allProblems.map((p) => {
              const submissions = dynamicContent.allProblemSubmissions.find(
                (sub) => sub.problemId === p.id,
              )?.submissions
              const hasSubmitted = submissions && submissions?.length >= 2
              return (
                <SelectItem key={p.id} value={p.id}>
                  {p.id}
                  {hasSubmitted && <CircleCheck className="inline-block w-4 ml-2 text-primary" />}
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

function SectionHeader({ title, children }: { title: string; children?: React.ReactNode }) {
  return (
    <div className="flex flex-row items-center gap-2 text-lg">
      {title}
      {children}
    </div>
  )
}

function TabButton({
  tab,
  isActive,
  onClick,
}: {
  tab: SimpleTabItem
  isActive: boolean
  onClick: () => void
}) {
  return (
    <button
      className={clsx(
        'text-muted-foreground hover:text-primary flex items-center gap-3 pt-3 transition-all ',
        isActive && 'text-primary',
      )}
      onClick={onClick}
    >
      <span className="w-[12em] truncate text-start">{tab.name}</span>
    </button>
  )
}

interface TabSectionProps {
  title: string
  tabs: SimpleTabItem[]
  activeTabs: string[]
  onTabChange: (id: string) => void
  children?: React.ReactNode
}

function TabSection({ title, tabs, activeTabs, onTabChange, children }: TabSectionProps) {
  return (
    <div className="px-3 p-2">
      <SectionHeader title={title}>{children}</SectionHeader>
      {tabs.map((tab) =>
        tab.type === 'chat' ? (
          <EditableButton
            key={tab.id}
            info={tab}
            isSelected={activeTabs.includes(tab.id)}
            onSelect={() => onTabChange(tab.id)}
          ></EditableButton>
        ) : (
          <TabButton
            key={tab.id}
            tab={tab}
            isActive={activeTabs.includes(tab.id)}
            onClick={() => onTabChange(tab.id)}
          />
        ),
      )}
    </div>
  )
}

export const handle = {
  i18n: 'pea',
}

export function SideBar({ allTabs, onTabChange, activeTabs }: SideBarProps) {
  const chatTabs = allTabs.filter((tab) => tab.type === 'chat')
  const materialTabs = allTabs.filter((tab) => tab.type === 'material')
  const submissionTabs = allTabs.filter((tab) => tab.type === 'submission')
  const { t } = useTranslation('pea')

  return (
    <ScrollArea className="grid flex-1 items-start px-2 text-sm font-medium lg:px-4">
      <TabSection
        title={t('Chats')}
        tabs={chatTabs}
        activeTabs={activeTabs}
        onTabChange={onTabChange}
      >
        <CreateBotDialog />
      </TabSection>
      <TabSection
        title={t('Materials')}
        tabs={materialTabs}
        activeTabs={activeTabs}
        onTabChange={onTabChange}
      />
      <TabSection
        title={t('Solution')}
        tabs={submissionTabs}
        activeTabs={activeTabs}
        onTabChange={onTabChange}
      />
    </ScrollArea>
  )
}
