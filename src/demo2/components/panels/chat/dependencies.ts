import { ChatRequestItem } from '../../../models/chat'

export interface ChatPanelProps {
  chatId: string
  isAxiia?: boolean
}

export interface ChatRequestInputOutput {
  input: string
  output: string
}

export type FutureData<Data> =
  | {
      status: 'loading'
    }
  | {
      status: 'complete'
      data: Data
    }

export interface AsyncActionHandler<Input> {
  isPerforming: boolean
  perform: (input: Input) => void
}

export interface ChatPanelDeps {
  useAxiiaInitialMessage(): FutureData<string>
  useChatRequest(chatId: string, chatRequest: ChatRequestItem): FutureData<ChatRequestInputOutput>
  usePostRequest(chatId: string): AsyncActionHandler<string>
  useCancelRequest(): AsyncActionHandler<void>
}
