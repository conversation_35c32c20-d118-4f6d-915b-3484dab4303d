import React, { useEffect, useRef } from 'react'
import { Textarea } from '@axiia-ui/components/textarea'
import { Button } from '@axiia-ui/components/button'
import { Square, Send } from 'lucide-react'
import { useAtom } from 'jotai'
import { chatMessageState } from '@main/state/atoms'

interface ChatInputProps {
  chatId: string
  onSubmit: (input: string) => void
  isPerforming: boolean
  onCancel: () => void
  placeholder: string
}

export const ChatInput: React.FC<ChatInputProps> = ({
  chatId,
  onSubmit,
  isPerforming,
  onCancel,
  placeholder,
}) => {
  const [input, setInput] = useAtom(chatMessageState(chatId))
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 4 * 24)}px`
    }
  }, [input])

  const handleSubmit = () => {
    onSubmit(input)
    setInput('')
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.nativeEvent.isComposing || event.keyCode === 229) {
      return
    }

    if (event.key === 'Enter') {
      event.preventDefault()
      if (event.shiftKey) {
        setInput((prevInput) => prevInput + '\n')
      } else {
        handleSubmit()
      }
    }
  }

  return (
    <div className="relative">
      <Textarea
        ref={textareaRef}
        className="w-full no-scrollbar min-h-[48px] max-h-[96px] resize-none py-3 pr-12 shadow-sm"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        rows={1}
      />
      <ActionButton isPerforming={isPerforming} onCancel={onCancel} onSubmit={handleSubmit} />
    </div>
  )
}

const ActionButton: React.FC<{
  isPerforming: boolean
  onCancel: () => void
  onSubmit: () => void
}> = ({ isPerforming, onCancel, onSubmit }) => (
  <Button
    className="absolute right-2 bottom-2 h-8 w-8 rounded-full flex items-center justify-center"
    size="icon"
    onClick={isPerforming ? onCancel : onSubmit}
  >
    {isPerforming ? (
      <Square className="h-4 w-4 text-white" />
    ) : (
      <Send className="h-4 w-4 text-white" />
    )}
  </Button>
)
