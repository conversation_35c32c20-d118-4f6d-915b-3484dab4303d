import { useToast } from '@axiia-ui/components/use-toast'
import { useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useChat } from '../../common'
import { ChatInput } from './ChatInput'
import { AxiiaInitialMessage, ChatMessage } from './ChatMessage'
import { DynamicChat } from 'src/demo2/models/test-dynamic-content'
import { ProblemContext } from '../../ProblemContext'
import { ChatStreamingMessage } from 'src/demo2/models/chat'
import { useFetcher, useRevalidator } from '@remix-run/react'
import { Button } from '@axiia-ui/components/button'
import { RotateCcw } from 'lucide-react'

function useStreamingContent(chat: DynamicChat): string {
  const { testId } = useContext(ProblemContext)
  const [streamingContent, setStreamingContent] = useState('')

  const revalidator = useRevalidator()
  const revalidate = useRef<() => void>(() => {})
  revalidate.current = () => {
    revalidator.revalidate()
  }

  let streamingUrl = ''
  if (chat.requests.length > 0) {
    const lastRequest = chat.requests[chat.requests.length - 1]
    if (lastRequest.type === 'in-progress') {
      streamingUrl = `/demo2/tests/${testId}/chats/${chat.id}/streaming/${lastRequest.id}`
    }
  }

  useEffect(() => {
    setStreamingContent('')
    const eventSource = new EventSource(streamingUrl)

    eventSource.onmessage = (event) => {
      const data: ChatStreamingMessage = JSON.parse(event.data)

      switch (data.type) {
        case 'chunk':
          setStreamingContent((prevContent) => prevContent + data.patch)
          break
        case 'complete':
          eventSource.close()
          revalidate.current()
          break
        case 'error':
          eventSource.close()
          revalidate.current()
          break
      }
    }

    eventSource.onerror = () => {
      eventSource.close()
      revalidate.current()
    }

    return () => {
      eventSource.close()
    }
  }, [streamingUrl])

  return streamingContent
}

const ChatPanel = ({ chatId }: { chatId: string }) => {
  const chat = useChat(chatId)
  const streamingContent = useStreamingContent(chat)
  const fetcher = useFetcher()
  const { t } = useTranslation('pea')
  const $messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()
  const [pendingMessage, setPendingMessage] = useState<string | null>(null)

  const isProblemConfigChat = !chat.isAxiia && chat.sourcePresetId === 'problem-config'

  const handleSubmit = (input: string) => {
    const trimmedInput = input.trim()
    if (trimmedInput.length === 0) {
      toast({
        variant: 'destructive',
        description: t('Message cannot be empty'),
      })
      return
    }

    setPendingMessage(trimmedInput)
    const formData = new FormData()
    formData.append('action', 'create-chat-request')
    formData.append('chatId', chatId)
    formData.append('input', trimmedInput)
    fetcher.submit(formData, { method: 'POST' })
  }

  const resetChat = () => {
    const formData = new FormData()
    formData.append('action', 'reset-chat')
    formData.append('chatId', chatId)
    fetcher.submit(formData, { method: 'POST' })
  }

  const scrollToBottom = () => {
    $messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
    setPendingMessage(null)
  }, [chat.requests])

  return (
    <div className="flex flex-col h-full">
      {isProblemConfigChat && (
        <div className="flex justify-end items-center px-[2em] py-2 border-b">
          <Button variant="outline" size="sm" onClick={resetChat} title={t('Reset conversation')}>
            <RotateCcw className="h-4 w-4 mr-2" />
            {t('Reset')}
          </Button>
        </div>
      )}
      <main className="flex-1 overflow-y-auto p-[2em] space-y-2">
        {chat.isAxiia && <AxiiaInitialMessage content={chat.initialMessage} />}
        {chat.requests.map((request) => {
          const { id, input } = request
          if (request.type === 'complete') {
            return (
              <ChatMessage
                key={id}
                id={id}
                input={input}
                output={request.output}
                streaming={false}
              />
            )
          } else if (request.type === 'in-progress') {
            return (
              <ChatMessage
                key={id}
                id={id}
                input={input}
                output={streamingContent}
                streaming={true}
              />
            )
          }
        })}
        {pendingMessage && (
          <ChatMessage
            key="pending"
            id="pending"
            input={pendingMessage}
            output=""
            streaming={false}
          />
        )}
        <div ref={$messagesEndRef} />
      </main>
      <footer className="sticky bottom-0 p-[2em]">
        <ChatInput
          chatId={chatId}
          onSubmit={handleSubmit}
          isPerforming={fetcher.state === 'submitting'}
          onCancel={() => undefined}
          placeholder={t('Please enter your question, shift+enter to change line, enter to send')}
        />
      </footer>
    </div>
  )
}

export default ChatPanel
