import { MessageBubble } from '@main/components/demo/MessageBubble'

export function AxiiaInitialMessage({ content }: { content: string }) {
  return (
    <div className="space-y-4">
      <MessageBubble id={`axiia-initial`} isUser={false} content={content} isGenerating={false} />
    </div>
  )
}

interface ChatMessageProps {
  id: string
  input: string
  output: string
  streaming: boolean
}

export function ChatMessage({ id, input, output, streaming }: ChatMessageProps) {
  return (
    <div className="space-y-4">
      <MessageBubble id={`user-${id}`} isUser={true} content={input} isGenerating={false} />
      <MessageBubble id={`ai-${id}`} isUser={false} content={output} isGenerating={streaming} />
    </div>
  )
}
