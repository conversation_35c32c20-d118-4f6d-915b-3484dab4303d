import { ChatPanelDeps, ChatRequestInputOutput } from './dependencies'
import { ChatRequestItem } from '../../../models/chat'
import ChatPanel from './ChatPanel'
import { createContext } from 'react'

export const mockChatRequests: ChatRequestItem[] = [
  { id: '1', timestamp: 1625097600000, status: 'complete' },
  { id: '2', timestamp: 1625184000000, status: 'complete' },
  { id: '3', timestamp: 1625270400000, status: 'complete' },
  { id: '4', timestamp: 1625356800000, status: 'complete' },
  { id: '5', timestamp: 1625443200000, status: 'complete' },
  { id: '6', timestamp: 1625529600000, status: 'complete' },
  { id: '7', timestamp: 1625616000000, status: 'in-progress' },
]

const mockChatContents: Record<string, ChatRequestInputOutput> = {
  '1': { input: '你好', output: '你好！有什么我可以帮助你的吗？' },
  '2': {
    input: '今天天气如何？',
    output:
      '抱歉，作为AI助手，我无法实时获取天气信息。建议您查看当地天气预报或使用天气应用获取最新信息。',
  },
  '3': {
    input: '介绍一下人工智能',
    output: '人工智能是计算机科学的一个分支，致力于创造能够模仿人类智能的',
  },
  '4': {
    input: '介绍一下人工智能',
    output: '人工智能是计算机科学的一个分支，致力于创造能够模仿人类智能的',
  },
  '5': {
    input: '介绍一下人工智能',
    output: '人工智能是计算机科学的一个分支，致力于创造能够模仿人类智能的',
  },
  '6': {
    input: '介绍一下人工智能',
    output: '人工智能是计算机科学的一个分支，致力于创造能够模仿人类智能的',
  },
  '7': {
    input: '介绍一下人工智能',
    output: '人工智能是计算机科学的一个分支，致力于创造能够模仿人类智能的',
  },
}

const mockChatPanelDeps: ChatPanelDeps = {
  useAxiiaInitialMessage: () => ({ status: 'loading' }),
  // useChatRequests: () => ({ status: 'complete', data: mockChatRequests }),
  useChatRequest: (_, request) => ({ status: 'complete', data: mockChatContents[request.id] }),
  usePostRequest: () => ({
    isPerforming: false,
    perform: (input: string) => {
      console.log('发送新请求:', input)
    },
  }),
  useCancelRequest: () => ({
    isPerforming: false,
    perform: () => {
      console.log('取消请求')
    },
  }),
}

const ChatPanelDepsContext = createContext<ChatPanelDeps>(mockChatPanelDeps)

const ChatPanelDemo: React.FC = () => {
  return (
    <ChatPanelDepsContext.Provider value={mockChatPanelDeps}>
      <ChatPanel chatId="mock-chat-id" />
    </ChatPanelDepsContext.Provider>
  )
}

export default ChatPanelDemo
