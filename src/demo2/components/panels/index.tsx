import { TabItem } from '../model'
import ChatPanel from './chat/ChatPanel'
import MaterialPanel from './MaterialPanel'
import SubmissionPanel from './submission/SubmissionPanel'
import Tasks from './task/Task'
export { default as MaterialPanel } from './MaterialPanel'

export function PanelContent({ tab }: { tab: TabItem }) {
  switch (tab.kind) {
    case 'axiia-chat':
    case 'custom-chat':
      return <ChatPanel chatId={tab.id} />
    case 'label-pred':
      return <Tasks group={tab} />
    case 'material':
      return <MaterialPanel material={tab} />
    case 'submission':
      return <SubmissionPanel />
    default:
      return <p>Hello</p>
  }
}
