import { ScrollArea } from '@radix-ui/react-scroll-area'
import { StyledMarkdown } from '@main/components/demo/StyledMarkdown'
import { MaterialStaticContent } from 'src/demo2/models/test-static-content'
import { CopyButton } from '@main/components/demo/Toolbar'

export default function MaterialPanel({ material }: { material: MaterialStaticContent }) {
  return (
    <ScrollArea className="overflow-auto relative h-full p-4 ">
      {material.copyable && (
        <div className="absolute top-4 right-4">
          <CopyButton text={material.content} className="w-6 h-6" />
        </div>
      )}
      <StyledMarkdown content={material.content} />
    </ScrollArea>
  )
}
