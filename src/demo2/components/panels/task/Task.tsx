import { Button } from '@axiia-ui/components/button'
import { Popover, PopoverContent, PopoverTrigger } from '@axiia-ui/components/popover'
import { StyledMarkdown } from '@main/components/demo/StyledMarkdown'
import { Info, Loader2, Play, TriangleAlert } from 'lucide-react'
import { LabelPredGroupStaticContent } from 'src/demo2/models/test-static-content'
import { useProblemDynamicContent, useProblemStaticContent } from '../../common'
import { useFetcher } from '@remix-run/react'
import {
  LabelPredGroupListDynamicContent,
  LabelPredPairDynamicContent,
  LabelPredPairTestResult,
} from 'src/demo2/models/test-dynamic-content'
import { useAtom } from 'jotai'
import { useContext } from 'react'
import { ProblemContext } from '../../ProblemContext'
import { useToast } from '@axiia-ui/components/use-toast'
import { useTranslation } from 'react-i18next'

interface SingleTaskResolvedStatus {
  name: string
  groupName: string
  input: string
  output: string

  actualOutput?: string
  extractedOutput?: string
  result?: LabelPredPairTestResult
  isDirty?: boolean
}

interface OverallResolvedStatus {
  hidesInput: boolean
  successfulRunCount: number
  correctCount: number
  groupName: string
  pairNames: string[]
}

interface ResolvedStatus {
  tasks?: SingleTaskResolvedStatus[]
  overall?: OverallResolvedStatus
}

function resolve(
  groupName: string,
  submission: string,
  staticContent: LabelPredGroupStaticContent,
  dynamicContent: LabelPredGroupListDynamicContent | undefined,
): ResolvedStatus {
  const status: ResolvedStatus = {}

  function getLabelPredPairDynamicContent(name: string): LabelPredPairDynamicContent | undefined {
    if (
      dynamicContent &&
      groupName in dynamicContent.byGroup &&
      name in dynamicContent.byGroup[groupName]
    ) {
      return dynamicContent.byGroup[groupName][name]
    }
  }

  if (!staticContent.hidesInput) {
    const tasks: SingleTaskResolvedStatus[] = []
    status.tasks = tasks

    for (const pair of staticContent.pairs) {
      const task: SingleTaskResolvedStatus = {
        name: pair.name,
        groupName,
        input: pair.input,
        output: pair.output,
      }
      tasks.push(task)

      const info = getLabelPredPairDynamicContent(pair.name)

      if (!info) {
        continue
      }
      task.actualOutput = info.output
      task.extractedOutput = info.extractedOutput
      task.result = info.result
      task.isDirty = true
      if (dynamicContent && info.textId in dynamicContent.texts) {
        task.isDirty = submission !== dynamicContent.texts[info.textId]
      }
    }
  }

  if (staticContent.hidesInput || staticContent.pairs.length > 1) {
    let successfulRunCount = 0
    let correctCount = 0
    const pairNames: string[] = []

    for (const pair of staticContent.pairs) {
      pairNames.push(pair.name)
      const info = getLabelPredPairDynamicContent(pair.name)
      if (!info) {
        continue
      }
      let isDirty = true
      if (dynamicContent && info.textId in dynamicContent.texts) {
        isDirty = submission !== dynamicContent.texts[info.textId]
      }
      if (isDirty || !info.result || info.result === 'errored') {
        continue
      }

      successfulRunCount += 1
      if (info.result === 'match') {
        correctCount += 1
      }
    }

    status.overall = {
      hidesInput: staticContent.hidesInput,
      successfulRunCount,
      correctCount,
      groupName,
      pairNames,
    }
  }

  return status
}

interface TasksProps {
  group: LabelPredGroupStaticContent
}

export default function Tasks({ group }: TasksProps) {
  const { labelPredGroupList } = useProblemDynamicContent()
  const { solutionAtom } = useContext(ProblemContext)
  const [solution] = useAtom(solutionAtom)

  const status = resolve(group.name, solution, group, labelPredGroupList)

  return (
    <div className="flex flex-1 flex-col overflow-y-auto p-6 pb-0 h-full">
      {status.tasks && status.tasks.map((task) => <SingleTask key={task.name} {...task} />)}
      {status.overall && (
        <div className="flex w-full sticky justify-end bottom-0 right-0 p-4 bg-white/80 rounded-md border border-slate-200">
          <Overall {...status.overall} />
        </div>
      )}
    </div>
  )
}

function ActualOutputLabel(props: SingleTaskResolvedStatus) {
  if (!props.result) {
    return <p className="font-medium break-words text-wrap text-gray-200">N/A</p>
  }
  switch (props.result) {
    case 'errored':
      return (
        <p className="font-medium break-words text-wrap text-red-600">运行时出现错误，请重试</p>
      )
    case 'no-value-extracted':
      return (
        <p className="font-medium break-words text-wrap text-red-600">
          输出格式错误，请点击右侧信息按钮查看实际输出
        </p>
      )
    case 'mismatch':
      return (
        <p className="font-medium break-words text-wrap text-red-600">{props.extractedOutput}</p>
      )
    case 'match':
      return (
        <p className="font-medium break-words text-wrap text-green-600">{props.extractedOutput}</p>
      )
  }
}

function SingleTask(props: SingleTaskResolvedStatus) {
  const { submission, problemVersion } = useProblemStaticContent()
  const { progressId } = useProblemDynamicContent()
  const fetcher = useFetcher()
  const { name, groupName, input, output } = props
  const { solutionAtom } = useContext(ProblemContext)
  const [solution] = useAtom(solutionAtom)
  const isSubmitting = fetcher.state === 'submitting'
  const { toast } = useToast()
  const { t } = useTranslation('pea')

  const isBriefingDebug = problemVersion.id.startsWith('000611')

  function handleClick() {
    if (
      !solution.trim() ||
      !submission.requiredPhrases.every((phrase) => solution.includes(phrase))
    ) {
      toast({
        variant: 'destructive',
        description: `Please ensure your solution includes the following: ${submission.requiredPhrases.join(', ')}`,
      })
      return
    }

    const form = new FormData()
    form.append('action', 'run-label-pred-task')
    form.append('progressId', progressId)
    form.append('submission', solution)
    form.append('groupName', groupName)
    form.append('pairNames', name)

    fetcher.submit(form, {
      method: 'POST',
    })
  }

  return (
    <div className="mb-4 rounded-md bg-white p-6 border border-slate-200">
      <header className="flex justify-between mb-2">
        <h3 className="font-medium leading-10">{t('Task')}</h3>
        <Button variant="ghost" size="icon" onClick={handleClick} disabled={isSubmitting}>
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </Button>
      </header>
      <div>
        <div className="border border-gray-300 w-full p-4 rounded-md bg-gray-50">
          <StyledMarkdown content={input} />
        </div>
        {isBriefingDebug ? (
          <div className="flex gap-4 my-4">
            <div className="flex-1">
              <p className="text-sm text-slate-500 leading-8">{t('Output')}</p>
              <StyledMarkdown content={props.actualOutput || 'N/A'} />
            </div>
          </div>
        ) : (
          <div className="flex gap-4 my-4">
            <div className="flex-1">
              <p className="text-sm text-slate-500 leading-8">{t('Expect')}</p>
              <p className="font-medium break-all text-wrap">{output}</p>
            </div>
            <div className="flex-1">
              <p className="text-sm text-slate-500 leading-8">{t('Actual')}</p>
              <ActualOutputLabel {...props} />
            </div>
            <div className="w-10">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="w-5 h-5" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit max-h-80 overflow-y-auto">
                  <div className="max-w-lg text-wrap text-sm">
                    <StyledMarkdown content={props.actualOutput || 'N/A'} />
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        )}
        {props.isDirty && (
          <p className="text-sm text-slate-500">
            <TriangleAlert className="w-4 h-4 inline-block" /> Your prompt has changed since the
            last test. You should rerun the test to verify your changes.
          </p>
        )}
      </div>
    </div>
  )
}

function Overall(props: OverallResolvedStatus) {
  const { submission, problemVersion } = useProblemStaticContent()
  const { progressId } = useProblemDynamicContent()
  const fetcher = useFetcher()
  const { hidesInput, groupName, pairNames } = props
  const { solutionAtom } = useContext(ProblemContext)
  const [solution] = useAtom(solutionAtom)
  const isSubmitting = fetcher.state === 'submitting'
  const { toast } = useToast()
  const { t } = useTranslation('pea')

  const isBriefingDebug = problemVersion.id.startsWith('000611')

  function handleClick() {
    if (
      !solution.trim() ||
      !submission.requiredPhrases.every((phrase) => solution.includes(phrase))
    ) {
      toast({
        variant: 'destructive',
        description: `Please ensure your solution includes the following: ${submission.requiredPhrases.join(', ')}`,
      })
      return
    }
    const form = new FormData()
    form.append('action', 'run-label-pred-task')
    form.append('progressId', progressId)
    form.append('submission', solution)
    form.append('groupName', groupName)
    form.append('pairNames', pairNames.join(','))

    fetcher.submit(form, {
      method: 'POST',
    })
  }

  const text =
    props.successfulRunCount > 0
      ? t('testResultsSummary', {
          testedCount: props.successfulRunCount,
          correctCount: props.correctCount,
          percentage: Math.round((props.correctCount / props.successfulRunCount) * 100),
        })
      : t('noTestsRun')

  return (
    <div className="w-fit flex flex-col justify-end items-end">
      {hidesInput && (
        <p className="text-slate-800 text-sm">
          {t('hiddenDataDescription', { count: pairNames.length })}
        </p>
      )}
      {isBriefingDebug ? <></> : <p className="text-slate-800 text-sm">{text}</p>}
      <Button className="w-fit my-2" onClick={handleClick} disabled={isSubmitting}>
        {isSubmitting ? (
          <Loader2 className="h-4 w-4 animate-spin text-white" />
        ) : (
          <Play className="w-4 h-4 mr-2" />
        )}
        {t('Run All Task')}
      </Button>
    </div>
  )
}
