import {
  <PERSON>alog,
  <PERSON><PERSON>Close,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@axiia-ui/components/dialog'
import { Form, useNavigation } from '@remix-run/react'
import { useTranslation } from 'react-i18next'
import { useProblemDynamicContent, useProblemStaticContent } from '../../common'
import { StyledMarkdown } from '@main/components/demo/StyledMarkdown'
import { Loader2 } from 'lucide-react'
import { Button } from '@axiia-ui/components/button'
import { useAtom } from 'jotai'
import { useContext } from 'react'
import { ProblemContext } from '../../ProblemContext'
import { Textarea } from '@axiia-ui/components/textarea'

export const handle = {
  i18n: 'pea',
}

const SubmissionPanel = () => {
  const { progressId } = useProblemDynamicContent()
  const { solutionAtom } = useContext(ProblemContext)
  const [solution, setSolution] = useAtom(solutionAtom)

  const navigation = useNavigation()
  const isIdle = navigation.state === 'idle'
  const { t } = useTranslation('pea')

  return (
    <div className="flex h-full w-full flex-col gap-[1em] p-[2em]">
      <Textarea
        value={solution}
        placeholder={t('Enter your analysis here...')}
        className="min-h-0 w-full flex-1 resize-none p-4"
        onChange={(e) => setSolution(e.target.value)}
      />
      <footer className="flex justify-between gap-[1em]">
        <Form method="POST">
          <input name="progressId" value={progressId} type="hidden" />
          <input name="submission" value={solution} type="hidden" />
          <FeedbackButton solution={solution} isDisabled={!solution || !isIdle} />
        </Form>
      </footer>
    </div>
  )
}

function FeedbackButton({ solution, isDisabled }: { solution: string; isDisabled: boolean }) {
  const { t } = useTranslation('pea')
  const { submission } = useProblemStaticContent()
  const { feedback } = useProblemDynamicContent()

  if (!submission.hasFeedback) {
    return null
  }

  let hasFeedback = !!feedback
  if (feedback && feedback.submission !== solution) {
    hasFeedback = false
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div>
          <input type="hidden" name="requestFeedback" value="true" />
          <Button disabled={isDisabled} type="submit" name="action" value="submit">
            {t('Evaluation')}
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{hasFeedback ? t('Evaluation Result') : t('Evaluating...')}</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        {hasFeedback && feedback ? (
          feedback.errored ? (
            <p>评估似乎出现了一些问题，您可以修改提交内容后重试或继续提交</p>
          ) : (
            <StyledMarkdown content={feedback.feedback}></StyledMarkdown>
          )
        ) : (
          <Loader2 className="text-primary animate-spin" />
        )}

        <DialogFooter className="sm:justify-end">
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              {t('Close')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default SubmissionPanel
