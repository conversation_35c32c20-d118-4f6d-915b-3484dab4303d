import { useToast } from '@axiia-ui/components/use-toast'
import { SimpleTabItem } from '../models/tabs'
import { useFetcher } from '@remix-run/react'
import clsx from 'clsx'
import { Edit } from 'lucide-react'
import { useRef, useState } from 'react'
import invariant from 'tiny-invariant'

interface EditableBtnProps {
  info: SimpleTabItem
  isSelected: boolean
  onSelect: () => void
}

export const EditableButton = (props: EditableBtnProps) => {
  const { info, isSelected, onSelect } = props
  const fetcher = useFetcher<Record<'ok', boolean>>()
  const [isEditing, setIsEditing] = useState(false)
  const [text, setText] = useState(info.name)
  const { toast } = useToast()

  const inputRef = useRef<HTMLInputElement>(null)

  const handleButtonClick = (e: React.MouseEvent<SVGSVGElement>) => {
    e.stopPropagation()
    setIsEditing(true)
  }

  const handleKeyDown: React.KeyboardEventHandler<HTMLInputElement> = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      inputRef.current?.blur()
    }
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const newTopic = e.target.value.trim()
    if (newTopic.length < 1 || newTopic.length > 50) {
      toast({
        variant: 'destructive',
        description: 'Topic should be between 1 and 50 characters',
      })
      return
    }
    if (newTopic !== info.name) {
      e.stopPropagation()
      setText(newTopic)
      editTopic({ topic: newTopic, id: info.id })
    }
    setIsEditing(false)
  }

  function editTopic(editInfo: { topic: string; id: string }) {
    const form = new FormData()
    form.set('action', 'rename-bot')
    form.set('topic', editInfo.topic)
    invariant(editInfo.id.startsWith('chat/'))
    form.set('id', editInfo.id.slice(5))
    fetcher.submit(form, { method: 'POST' })
  }

  return (
    <button
      onClick={onSelect}
      className={clsx(
        isSelected && 'text-primary',
        'text-muted-foreground hover:text-primary flex w-full items-center gap-3 pt-3 transition-all',
      )}
    >
      {isEditing ? (
        <input
          ref={inputRef}
          className="focus:outline-primary rounded-md border border-gray-300 px-1"
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
        />
      ) : (
        <div className="group flex items-center justify-between gap-2">
          <p className="w-[10em] truncate text-start">{text}</p>
          <Edit
            className={clsx(
              'invisible h-4 w-4',
              info.name === 'Axiia' || info.isProblemConfigChat
                ? 'invisible'
                : 'group-hover:visible',
            )}
            onClick={handleButtonClick}
          />
        </div>
      )}
    </button>
  )
}
