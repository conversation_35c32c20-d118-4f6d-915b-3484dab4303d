import { Button } from '@axiia-ui/components/button'
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@axiia-ui/components/dialog'
import { useProblemDynamicContent, useProblemStaticContent } from './common'
import { Card, CardContent, CardHeader, CardTitle } from '@axiia-ui/components/card'
import { cn } from '@axiia-ui/lib/utils'
import {
  LabelPredPairDynamicContent,
  ProblemFinalConfirmContent,
} from '../models/test-dynamic-content'
import { Form } from '@remix-run/react'
import { useAtom } from 'jotai'
import { useContext } from 'react'
import { ProblemContext } from './ProblemContext'
import { useTranslation } from 'react-i18next'

export function FinalConfirmWrapper() {
  const { progressId } = useProblemDynamicContent()
  const { solutionAtom } = useContext(ProblemContext)
  const [solution] = useAtom(solutionAtom)
  return (
    <Form method="post">
      <input type="hidden" name="submission" value={solution} />
      <input type="hidden" name="progressId" value={progressId} />
      <FinalConfirm />
    </Form>
  )
}

export function FinalConfirm() {
  const { t } = useTranslation('pea')
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="flex justify-center mt-2">
          <Button type="submit" name="action" value="submit" className="w-full">
            {t('Submit')}
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className={'lg:max-w-screen-lg overflow-y-scroll max-h-screen'}>
        <DialogHeader>
          <DialogTitle>{t('Answer Review')}</DialogTitle>
          <DialogDescription>
            {t('Please check your answers and confirm before submitting')}
          </DialogDescription>
        </DialogHeader>
        <FinalConfirmContent />
      </DialogContent>
    </Dialog>
  )
}

function handleLabelPredGroupList(
  labelPredGroupList: Record<string, Record<string, LabelPredPairDynamicContent>>,
) {
  const counts = Object.values(labelPredGroupList).reduce(
    (acc, group) => {
      Object.values(group).forEach((pair) => {
        if (pair.result && pair.result !== 'errored') {
          acc.testedCount++
          if (pair.result === 'match') {
            acc.correctCount++
          }
        }
      })
      return acc
    },
    { testedCount: 0, correctCount: 0 },
  )

  const { testedCount: successfulRunCount, correctCount } = counts
  const percentage = successfulRunCount ? Math.round((correctCount / successfulRunCount) * 100) : 0

  return { ...counts, percentage }
}

export function FinalConfirmContent() {
  const { allProblemFinalConfirm } = useProblemDynamicContent()
  const { t } = useTranslation('pea')
  const disableToSubmit = allProblemFinalConfirm.some((problem) => !problem.hasSubmitted)
  return (
    <>
      <div className="h-full overflow-y-auto">
        <div className="flex gap-4 w-full mb-4">
          <FinalConfirmCard allProblemFinalConfirm={allProblemFinalConfirm} />
        </div>
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="secondary">
            {t('Close')}
          </Button>
        </DialogClose>
        <Form method="post">
          <Button type="submit" name="action" value="final-submit" disabled={disableToSubmit}>
            {t('Confirm')}
          </Button>
        </Form>
      </DialogFooter>
    </>
  )
}

export function FinalConfirmCard({
  allProblemFinalConfirm,
}: {
  allProblemFinalConfirm: ProblemFinalConfirmContent[]
}) {
  const { t } = useTranslation('pea')
  const { problemVersion } = useProblemStaticContent()
  const isBriefingDebug = problemVersion.id.startsWith('000611')

  const getCardText = (problem: ProblemFinalConfirmContent) => {
    const { hasSubmitted, feedback, labelPredGroupList } = problem

    if (!hasSubmitted) return t('This question has not been answered')
    if (!feedback && !labelPredGroupList) return t('This question has not been evaluated')

    if (feedback) {
      return feedback.errored ? t('Evaluation error, please try again') : feedback.feedback
    }

    if (isBriefingDebug) {
      return '已作答'
    }

    if (labelPredGroupList) {
      const { testedCount, correctCount, percentage } = handleLabelPredGroupList(
        labelPredGroupList.byGroup,
      )
      return t('testResultsSummary', { testedCount, correctCount, percentage })
    }
  }

  const getCardClassName = (problem: ProblemFinalConfirmContent) => {
    const { hasSubmitted, feedback, labelPredGroupList } = problem
    const hasEvaluation = feedback || labelPredGroupList
    const passEvaluation =
      hasEvaluation && (!feedback?.errored || labelPredGroupList?.byGroup?.length)

    return cn(
      'w-full min-w-60 border-2 min-h-60',
      hasSubmitted
        ? passEvaluation
          ? ''
          : 'border-yellow-500 bg-yellow-100'
        : 'border-red-500 bg-red-100',
    )
  }

  return allProblemFinalConfirm.map((problem) => (
    <Card key={problem.problemId} className={getCardClassName(problem)}>
      <CardHeader>
        <CardTitle>{problem.problemId}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full whitespace-pre-wrap">{getCardText(problem)}</div>
      </CardContent>
    </Card>
  ))
}
