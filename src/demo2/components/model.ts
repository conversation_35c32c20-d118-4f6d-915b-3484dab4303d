import { immerable } from 'immer'
import { CustomChatItem } from '../models/chat'
import invariant from 'tiny-invariant'
import {
  LabelPredGroupStaticContent,
  MaterialStaticContent,
  ProblemStaticContent,
} from '../models/test-static-content'
import { ProblemDynamicContent } from '../models/test-dynamic-content'

export type TabItem =
  | ({
      kind: 'material'
    } & MaterialStaticContent)
  | ({
      kind: 'label-pred'
    } & LabelPredGroupStaticContent)
  | ({
      kind: 'custom-chat'
    } & CustomChatItem)
  | {
      kind: 'axiia-chat'
      id: string
    }
  | {
      kind: 'scratchpad'
    }
  | {
      kind: 'submission'
    }

export function titleForTabItem(tabItem: TabItem): string {
  switch (tabItem.kind) {
    case 'material':
      return tabItem.title
    case 'label-pred':
      return tabItem.title
    case 'custom-chat':
      return tabItem.title
    case 'axiia-chat':
      return 'Axiia'
    case 'scratchpad':
      return 'Scratchpad'
    case 'submission':
      return 'Solution'
  }
}

export function idForTabItem(tabItem: TabItem): string {
  switch (tabItem.kind) {
    case 'material':
      return `material/${tabItem.name}`
    case 'label-pred':
      return `label-pred/${tabItem.name}`
    case 'custom-chat':
      return `chat/${tabItem.id}`
    case 'axiia-chat':
      return 'axiia'
    case 'scratchpad':
      return 'scratchpad'
    case 'submission':
      return 'submission'
  }
}

export class MainPanelState {
  [immerable] = true

  staticContent: ProblemStaticContent

  availableTabs: TabItem[]
  leftInFocus: boolean
  leftActiveTabId: string
  rightActiveTabId: string
  leftTabs: string[]
  rightTabs: string[]

  constructor(staticContent: ProblemStaticContent, dynamicContent: ProblemDynamicContent) {
    this.staticContent = staticContent
    this.availableTabs = MainPanelState.availableTabs(staticContent, dynamicContent)
    this.leftInFocus = true
    this.leftActiveTabId = idForTabItem({ kind: 'axiia-chat', id: dynamicContent.progressId })
    this.rightActiveTabId = idForTabItem({ kind: 'submission' })
    this.leftTabs = [this.leftActiveTabId]
    this.rightTabs = [this.rightActiveTabId]
  }

  get canCloseLeftTabs(): boolean {
    return this.leftTabs.length > 0
  }

  get canCloseRightTabs(): boolean {
    return this.rightTabs.length > 0
  }

  get canSendLeftActiveTabToRight(): boolean {
    return this.leftTabs.length > 0
  }

  get canSendRightActiveTabToLeft(): boolean {
    return this.rightTabs.length > 0
  }

  tabPosition(tabId: string): { left: boolean; index: number } {
    let index = this.leftTabs.findIndex((x) => x === tabId)
    if (index !== -1) {
      return { left: true, index }
    }
    index = this.rightTabs.findIndex((x) => x === tabId)
    invariant(index !== -1)
    return { left: false, index }
  }

  closeTab(tabId: string) {
    const { left, index } = this.tabPosition(tabId)
    if (left) {
      invariant(this.canCloseLeftTabs)
      this.leftTabs.splice(index, 1)
      this.leftActiveTabId = this.leftTabs[Math.min(index, this.leftTabs.length - 1)]
    } else {
      invariant(this.canCloseRightTabs)
      this.rightTabs.splice(index, 1)
      this.rightActiveTabId = this.rightTabs[Math.min(index, this.rightTabs.length - 1)]
    }
  }

  sendActiveTabToOtherSide(left: boolean) {
    const tabId = left ? this.leftActiveTabId : this.rightActiveTabId
    const { index } = this.tabPosition(tabId)
    if (left) {
      invariant(this.canSendLeftActiveTabToRight)
      this.rightTabs.push(tabId)

      this.leftTabs.splice(index, 1)
      this.leftActiveTabId = this.leftTabs[Math.min(index, this.leftTabs.length - 1)]
      this.rightActiveTabId = tabId

      this.leftInFocus = false
    } else {
      invariant(this.canSendRightActiveTabToLeft)
      this.leftTabs.push(tabId)

      this.rightTabs.splice(index, 1)
      this.rightActiveTabId = this.rightTabs[Math.min(index, this.rightTabs.length - 1)]
      this.leftActiveTabId = tabId

      this.leftInFocus = true
    }
  }

  openTabFromSidebar(tabId: string) {
    invariant(this.availableTabs.find((x) => idForTabItem(x) === tabId))

    const updateLeftInFocusIfNeeded = (newValue: boolean) => {
      if (this.leftInFocus !== newValue) {
        this.leftInFocus = newValue
      }
    }

    if (this.leftActiveTabId === tabId) {
      updateLeftInFocusIfNeeded(true)
      return
    }
    if (this.rightActiveTabId === tabId) {
      updateLeftInFocusIfNeeded(false)
      return
    }

    if (this.leftTabs.find((x) => x == tabId)) {
      this.leftActiveTabId = tabId
      updateLeftInFocusIfNeeded(true)
      return
    }

    if (this.rightTabs.find((x) => x == tabId)) {
      this.rightActiveTabId = tabId
      updateLeftInFocusIfNeeded(false)
      return
    }

    if (this.leftInFocus) {
      this.leftTabs.push(tabId)
      this.leftActiveTabId = tabId
    } else {
      this.rightTabs.push(tabId)
      this.rightActiveTabId = tabId
    }
  }

  merge(dynamicContent: ProblemDynamicContent) {
    const oldTabs = this.availableTabs
    const newTabs = MainPanelState.availableTabs(this.staticContent, dynamicContent)
    this.availableTabs = newTabs

    const newTabIds: string[] = []

    for (const newTab of newTabs) {
      const id = idForTabItem(newTab)
      if (!oldTabs.find((x) => idForTabItem(x) === id)) {
        newTabIds.push(id)
      }
    }

    if (newTabIds.length === 1) {
      const newTabId = newTabIds[0]
      if (this.leftInFocus) {
        this.leftTabs.push(newTabId)
        this.leftActiveTabId = newTabId
      } else {
        this.rightTabs.push(newTabId)
        this.rightActiveTabId = newTabId
      }
    }
  }

  getTabItem(tabId: string): TabItem {
    const tab = this.availableTabs.find((x) => idForTabItem(x) === tabId)
    invariant(tab)
    return tab
  }

  static availableTabs(
    staticContent: ProblemStaticContent,
    dynamicContent: ProblemDynamicContent,
  ): TabItem[] {
    const tabs: TabItem[] = []
    tabs.push({
      kind: 'axiia-chat',
      id: dynamicContent.axiiaChat.id,
    })

    for (const chat of dynamicContent.customChats) {
      tabs.push({
        kind: 'custom-chat',
        ...chat,
      })
    }

    for (const material of staticContent.materials) {
      tabs.push({
        kind: 'material',
        ...material,
      })
    }

    for (const group of staticContent.labelPredGroups || []) {
      tabs.push({
        kind: 'label-pred',
        ...group,
      })
    }

    tabs.push({
      kind: 'submission',
    })

    return tabs
  }
}
