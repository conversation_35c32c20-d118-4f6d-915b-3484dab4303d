import { GetStartedButton } from '@main/components/demo/Buttons'
import { useNavigate, useParams } from '@remix-run/react'
import { useTranslation } from 'react-i18next'

export default function DemoStart() {
  const { t } = useTranslation('pea')
  const navigate = useNavigate()
  const { id } = useParams()

  function handleClick() {
    navigate(`/demo2/tests/${id}/problems/0`)
  }

  return (
    <div className="flex items-center justify-center h-screen">
      <div>
        <GetStartedButton title={t('Get Started')} isLoading={false} onClick={handleClick} />
      </div>
    </div>
  )
}
