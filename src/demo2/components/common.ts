import { useRouteLoaderData } from '@remix-run/react'
import { ProblemStaticContent } from '../models/test-static-content'
import invariant from 'tiny-invariant'
import {
  DynamicChat,
  DynamicCustomChat,
  ProblemDynamicContent,
} from '../models/test-dynamic-content'

export function useProblemStaticContent(): ProblemStaticContent {
  return useRouteLoaderData<ProblemStaticContent>('routes/demo2.tests.$id.problems.$n')!
}

export function useProblemDynamicContent(): ProblemDynamicContent {
  return useRouteLoaderData<ProblemDynamicContent>('routes/demo2.tests.$id.problems.$n._index')!
}

type UseChatResult =
  | ({ isAxiia: true; initialMessage: string } & DynamicChat)
  | ({ isAxiia: false } & DynamicCustomChat)

export function useChat(chatId: string): UseChatResult {
  const result = useProblemDynamicContent()
  const { axiiaInitialMessage } = useProblemStaticContent()
  if (result.axiiaChat.id === chatId) {
    return {
      isAxiia: true,
      initialMessage: axiiaInitialMessage,
      ...result.axiiaChat,
    }
  }
  const chat = result.customChats.find((x) => x.id === chatId)
  invariant(chat)
  return {
    isAxiia: false,
    ...chat,
  }
}

export interface TestProblemIndex {
  testId: string
  n: number
}

export function getTestProblemIndex(params: Record<string, string | undefined>): TestProblemIndex {
  const testId = params.id
  invariant(testId)
  const n = parseInt(params.n || '')
  invariant(!isNaN(n) && n >= 0)
  return { testId, n }
}
