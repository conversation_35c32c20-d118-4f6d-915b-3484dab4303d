import { createContext } from 'react'
import { ProblemVersion } from '../models/problem'
import { atom, PrimitiveAtom } from 'jotai'

interface Problem {
  testId: string
  problem: ProblemVersion
  solutionAtom: PrimitiveAtom<string>
  allProblems: ProblemVersion[]
}

const dummyAtom: PrimitiveAtom<string> = atom('')

export const ProblemContext = createContext<Problem>({
  testId: 'example-test-id',
  problem: {
    id: 'example-problem-id',
    version: 0,
  },
  solutionAtom: dummyAtom,
  allProblems: [
    {
      id: 'example-problem-id',
      version: 0,
    },
  ],
})
