import { Input } from '@axiia-ui/components/input'
import { Textarea } from '@axiia-ui/components/textarea'
import { useTranslation } from 'react-i18next'

interface InstructionInputProps {
  instruction: string
  title: string
  setInstruction: (newValue: string) => void
  setTitle: (newValue: string) => void
}

export default function InstructionInput({
  instruction,
  setInstruction,
  title,
  setTitle,
}: InstructionInputProps) {
  const { t } = useTranslation('pea')
  return (
    <>
      <h2 className="mt-0 text-5xl font-medium">{t('Or enter your own instructions')}</h2>
      <Input value={title} onChange={(e) => setTitle(e.target.value)} placeholder="Bot Name" />
      <Textarea
        name="instruction"
        placeholder={t('You are a helpful assistant…')}
        className="h-[300px] resize-none"
        value={instruction}
        onChange={(e) => setInstruction(e.target.value)}
      ></Textarea>
    </>
  )
}
