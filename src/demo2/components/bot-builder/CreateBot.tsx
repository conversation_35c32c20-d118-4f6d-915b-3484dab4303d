import { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@axiia-ui/components/dialog'
import { Plus } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { PresetSelector } from './PresetSelector'
import InstructionInput from './InstructionInput'
import { Button } from '@axiia-ui/components/button'
import { useProblemDynamicContent, useProblemStaticContent } from '../common'
import { useFetcher } from '@remix-run/react'

export type CreateBotSpec =
  | {
      type: 'preset'
      id: string
    }
  | {
      type: 'custom'
      instruction: string
    }

export const handle = {
  i18n: 'pea',
}

export function CreateBotDialog() {
  const { t } = useTranslation('pea')
  const [open, setOpen] = useState(false)
  const presets = useProblemStaticContent().botPresets
  const [selectedPreset, setSelectedPreset] = useState<string | undefined>(presets[0].id)
  const [instruction, setInstruction] = useState<string>(presets[0].prompt)
  const [title, setTitle] = useState<string>(presets[0].title)

  function handleInstructionChange(newValue: string) {
    setSelectedPreset(undefined)
    setInstruction(newValue)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Plus className="ml-1 inline-block h-6 w-6 cursor-pointer align-bottom" />
      </DialogTrigger>
      <DialogContent className="min-w-[800px]">
        <DialogHeader>
          <DialogTitle>{t('Create Bot')}</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-[2em] overflow-auto h-[60vh] p-2">
          <PresetSelector
            presets={presets}
            selectedPreset={selectedPreset}
            onSelected={(id) => {
              const preset = presets.find((preset) => preset.id === id)
              setSelectedPreset(id)
              setTitle(preset?.title ?? '')
              setInstruction(preset?.prompt ?? '')
            }}
          />
          <InstructionInput
            instruction={instruction}
            setInstruction={handleInstructionChange}
            title={title}
            setTitle={setTitle}
          />
        </div>
        <DialogFooter>
          <CreateBotForm
            prompt={instruction}
            sourcePresetId={selectedPreset}
            title={title}
            setOpen={setOpen}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface CreateBotFormProps {
  prompt?: string
  sourcePresetId?: string
  title: string
  setOpen: (open: boolean) => void
}

const CreateBotForm = ({ prompt, sourcePresetId, title, setOpen }: CreateBotFormProps) => {
  const { progressId } = useProblemDynamicContent()

  const fetcher = useFetcher()
  const { t } = useTranslation('pea')

  function onSubmit() {
    const formData = new FormData()
    formData.set('action', 'create-bot')
    formData.set('title', title)
    formData.set('progressId', progressId)
    if (sourcePresetId) {
      formData.set('sourcePresetId', sourcePresetId)
    } else {
      formData.set('prompt', prompt ?? '')
    }
    fetcher.submit(formData, { method: 'POST' })
  }

  useEffect(() => {
    if (fetcher.state === 'loading') {
      setOpen(false)
    }
  }, [setOpen, fetcher.state])

  return (
    <Button disabled={fetcher.state !== 'idle'} onClick={onSubmit}>
      {t('Create')}
    </Button>
  )
}
