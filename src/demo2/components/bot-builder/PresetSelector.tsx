import { ReactNode } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { ResolvedBotPreset } from 'src/demo2/models/bot-presets'

interface PresetSelectorProps {
  presets: ResolvedBotPreset[]
  selectedPreset?: string
  onSelected: (preset: string) => void
}

export const PresetSelector = (props: PresetSelectorProps) => {
  const { presets, selectedPreset, onSelected } = props
  const { t } = useTranslation('pea')

  const preset = ({ id, title }: ResolvedBotPreset) => {
    return (
      <button
        key={id}
        onClick={() => {
          onSelected(id)
        }}
        className={clsx(selectedPreset === id ? 'text-primary' : '')}
      >
        {t(`${title}`)}
      </button>
    )
  }

  const presetWithCommas = presets
    .map(preset)
    .reduce((prev: ReactNode[], curr: ReactNode, index: number) => {
      return index === 0 ? [curr] : [...prev, ', ', curr]
    }, [])

  return (
    <div>
      <h2 className="mb-6 text-5xl font-medium">{t('Select a preset')}</h2>
      <p className="text-4xl">{presetWithCommas}</p>
    </div>
  )
}
