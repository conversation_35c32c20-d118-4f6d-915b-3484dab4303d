/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useEffect, useState } from 'react'
import { ProblemSelector, SideBar, SideHeader } from './SideBar'
import PanelHeader from '@main/components/demo/PanelHeader'
import { PanelContent } from './panels'
import clsx from 'clsx'
import { Draft, produce } from 'immer'
import { idForTabItem, MainPanelState, TabItem, titleForTabItem } from './model'
import { SimpleTabItem } from '../models/tabs'
import { ProblemStaticContent } from '../models/test-static-content'
import { ProblemDynamicContent } from '../models/test-dynamic-content'
import { FinalConfirmWrapper } from './FinalConfirm'

function tabItemToSimpleTabItem(tab: TabItem): SimpleTabItem {
  const id = idForTabItem(tab)
  const name = titleForTabItem(tab)
  let type: 'chat' | 'material' | 'scratchpad' | 'submission'
  switch (tab.kind) {
    case 'custom-chat':
    case 'axiia-chat':
      type = 'chat'
      break
    case 'material':
    case 'label-pred':
      type = 'material'
      break
    case 'scratchpad':
      type = 'scratchpad'
      break
    case 'submission':
      type = 'submission'
      break
  }

  const result: SimpleTabItem = { id, name, type }
  if (tab.kind === 'custom-chat' && tab.sourcePresetId === 'problem-config') {
    result.isProblemConfigChat = true
  }

  return result
}

type UpdateMainPanelState = (body: (draft: Draft<MainPanelState>) => void) => void

interface MainPanelProps {
  staticContent: ProblemStaticContent
  dynamicContent: ProblemDynamicContent
}

export default function MainPanel({ staticContent, dynamicContent }: MainPanelProps) {
  const [state, setState] = useState(() => new MainPanelState(staticContent, dynamicContent))

  function update(body: (draft: Draft<MainPanelState>) => void) {
    setState((prev) => produce(prev, body))
  }

  useEffect(() => {
    setState((prev) => {
      return produce(prev, (draft) => {
        draft.merge(dynamicContent)
      })
    })
  }, [setState, dynamicContent])

  return (
    <div className="grid h-screen w-screen grid-cols-[220px_1fr]">
      <div className="bg-muted/40 flex h-full max-h-screen flex-col gap-2 border-r">
        <SideHeader />
        <SideBarWrapper state={state} update={update} />
        <div className="m-5">
          <ProblemSelector dynamicContent={dynamicContent} />
          <FinalConfirmWrapper />
        </div>
      </div>
      <main className="flex flex-row overflow-hidden">
        <RenderPanel state={state} update={update} left={true} />
        <RenderPanel state={state} update={update} left={false} />
      </main>
    </div>
  )
}

function SideBarWrapper({
  state,
  update,
}: {
  state: MainPanelState
  update: UpdateMainPanelState
}) {
  const handleSideBarTabChange = (tabId: string) => {
    update((draft) => {
      draft.openTabFromSidebar(tabId)
    })
  }

  return (
    <SideBar
      allTabs={state.availableTabs.map(tabItemToSimpleTabItem)}
      activeTabs={[state.leftInFocus ? state.leftActiveTabId : state.rightActiveTabId]}
      onTabChange={handleSideBarTabChange}
    />
  )
}

function RenderPanel({
  state,
  update,
  left,
}: {
  state: MainPanelState
  update: UpdateMainPanelState
  left: boolean
}) {
  const activeTabId = left ? state.leftActiveTabId : state.rightActiveTabId
  const tabs = (left ? state.leftTabs : state.rightTabs).map((x) =>
    tabItemToSimpleTabItem(state.getTabItem(x)),
  )

  function setActivePanel() {
    update((draft) => {
      if (draft.leftInFocus === left) {
        return
      }
      draft.leftInFocus = left
    })
  }

  function sendTabToOtherSide() {
    const enabled = left ? state.canSendLeftActiveTabToRight : state.canSendRightActiveTabToLeft
    if (!enabled) {
      return
    }
    update((draft) => {
      draft.sendActiveTabToOtherSide(left)
    })
  }

  function handleTabChange(id: string) {
    update((draft) => {
      if (left) {
        if (draft.leftActiveTabId !== id) {
          draft.leftActiveTabId = id
        }
      } else {
        if (draft.rightActiveTabId !== id) {
          draft.rightActiveTabId = id
        }
      }
    })
  }

  function handleTabClose(id: string) {
    update((draft) => {
      draft.closeTab(id)
    })
  }

  return (
    <div
      className={clsx(
        'flex flex-1 flex-col overflow-hidden',
        state.leftInFocus === left ? 'bg-muted/60' : '',
      )}
      onClick={() => setActivePanel()}
    >
      <PanelHeader
        curTabID={activeTabId}
        tabs={tabs}
        isLeft={left}
        onSwitch={sendTabToOtherSide}
        onTabChange={handleTabChange}
        onRemove={handleTabClose}
      />
      <div className="relative overflow-hidden flex-1 w-full">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`absolute top-0 h-full w-full ${
              activeTabId === tab.id ? 'visible' : 'invisible'
            }`}
          >
            <PanelContent tab={state.getTabItem(tab.id)} />
          </div>
        ))}
      </div>
    </div>
  )
}
