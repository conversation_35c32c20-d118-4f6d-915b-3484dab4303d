import { useFetcher } from '@remix-run/react'
import { useState, useEffect } from 'react'

export default function CountdownTimer({
  testId,
  startTime,
  expireMinutes,
}: {
  testId: string
  startTime: string | undefined
  expireMinutes: number
}) {
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const fetcher = useFetcher()

  useEffect(() => {
    if (!startTime) return

    const startTimeDate = new Date(startTime)
    const expirationTime = new Date(startTimeDate.getTime() + expireMinutes * 60 * 1000)

    const calculateTimeLeft = () => {
      const now = new Date()
      const difference = expirationTime.getTime() - now.getTime()

      if (difference <= 0) {
        return 0
      }

      return Math.floor(difference / 1000)
    }

    setTimeLeft(calculateTimeLeft())

    const timer = setInterval(() => {
      const remaining = calculateTimeLeft()
      setTimeLeft(remaining)

      if (remaining <= 0) {
        clearInterval(timer)
        fetcher.submit(
          {
            action: 'final-submit',
          },
          {
            method: 'POST',
          },
        )
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [startTime, expireMinutes, testId, fetcher])

  if (!timeLeft || !startTime) return null

  const hours = Math.floor(timeLeft / 3600)
  const minutes = Math.floor((timeLeft % 3600) / 60)
  const seconds = timeLeft % 60

  return (
    <div className="absolute top-4 right-4">
      <div className="text-sm text-gray-500">
        {hours.toString().padStart(2, '0')}:{minutes.toString().padStart(2, '0')}:
        {seconds.toString().padStart(2, '0')}
      </div>
    </div>
  )
}
