export async function decryptAES(concated: <PERSON>int8A<PERSON>y, key: <PERSON><PERSON><PERSON><PERSON><PERSON>): Promise<Uint8Array> {
  const iv = concated.slice(0, 12)
  const encrypted = concated.slice(12)
  const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, key, encrypted)
  return new Uint8Array(decrypted)
}

export async function encryptAES(plain: Uint8Array, key: Crypto<PERSON><PERSON>): Promise<Uint8Array> {
  const iv = crypto.getRandomValues(new Uint8Array(12))
  const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, key, plain)
  const concated = new Uint8Array(12 + encrypted.byteLength)
  concated.set(iv)
  concated.set(new Uint8Array(encrypted), 12)
  return concated
}
