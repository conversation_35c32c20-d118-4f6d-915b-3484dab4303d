import { encryptAES, decryptAES } from './aes'

let key: Crypto<PERSON><PERSON>

beforeAll(async () => {
  key = await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, [
    'encrypt',
    'decrypt',
  ])
})

describe('AES Encryption/Decryption', () => {
  it('should encrypt and decrypt data correctly', async () => {
    const plainText = new TextEncoder().encode('This is a test message')

    // Encrypt the plaintext
    const encrypted = await encryptAES(plainText, key)
    expect(encrypted).toBeInstanceOf(Uint8Array)
    expect(encrypted.length).toBeGreaterThan(12) // iv + encrypted data

    // Decrypt the ciphertext
    const decrypted = await decryptAES(encrypted, key)
    expect(decrypted).toBeInstanceOf(Uint8Array)

    // Verify that the decrypted text matches the original plaintext
    const decryptedText = new TextDecoder().decode(decrypted)
    expect(decryptedText).toBe('This is a test message')
  })

  it('should throw an error with incorrect key', async () => {
    const plainText = new TextEncoder().encode('This is another test message')
    const encrypted = await encryptAES(plainText, key)

    // Generate a different key
    const wrongKey = await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, [
      'encrypt',
      'decrypt',
    ])

    // Attempt to decrypt with the wrong key and expect an error
    await expect(decryptAES(encrypted, wrongKey)).rejects.toThrow()
  })

  it('should throw an error with tampered data', async () => {
    const plainText = new TextEncoder().encode('This is yet another test message')
    const encrypted = await encryptAES(plainText, key)

    // Tamper with the encrypted data
    encrypted[encrypted.length - 1] ^= 1 // Flip the last bit

    // Attempt to decrypt tampered data and expect an error
    await expect(decryptAES(encrypted, key)).rejects.toThrow()
  })

  it('should produce different ciphertexts for the same plaintext on consecutive encryptions', async () => {
    const plainText = new TextEncoder().encode('Consistent message')

    // Encrypt the plaintext twice
    const encrypted1 = await encryptAES(plainText, key)
    const encrypted2 = await encryptAES(plainText, key)

    // Ensure the encrypted results are different
    expect(encrypted1).not.toEqual(encrypted2)
  })
})
