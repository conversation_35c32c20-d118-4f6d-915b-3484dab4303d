import { signHMAC, verifyHMAC } from './hmac'

let key: Crypt<PERSON><PERSON><PERSON>

beforeAll(async () => {
  key = await crypto.subtle.generateKey(
    {
      name: 'HM<PERSON>',
      hash: { name: 'SHA-256' },
      length: 256,
    },
    true,
    ['sign', 'verify'],
  )
})

describe('HMAC Signing/Verification', () => {
  it('should sign and verify a message correctly', async () => {
    const message = new TextEncoder().encode('This is a test message')

    // Sign the message
    const signature = await signHMAC(message, key)
    expect(signature).toBeInstanceOf(Uint8Array)

    // Verify the signature
    const isValid = await verifyHMAC(signature, message, key)
    expect(isValid).toBe(true)
  })

  it('should fail verification with a different message', async () => {
    const message = new TextEncoder().encode('This is a test message')
    const differentMessage = new TextEncoder().encode('This is a different message')

    // Sign the original message
    const signature = await sign<PERSON><PERSON><PERSON>(message, key)

    // Verify the signature with a different message
    const isValid = await verifyHMAC(signature, differentMessage, key)
    expect(isValid).toBe(false)
  })

  it('should fail verification with a different key', async () => {
    const message = new TextEncoder().encode('This is a test message')

    // Sign the message with the original key
    const signature = await signHMAC(message, key)

    // Generate a different key
    const differentKey = await crypto.subtle.generateKey(
      {
        name: 'HMAC',
        hash: { name: 'SHA-256' },
        length: 256,
      },
      true,
      ['sign', 'verify'],
    )

    // Verify the signature with the different key
    const isValid = await verifyHMAC(signature, message, differentKey)
    expect(isValid).toBe(false)
  })
})
