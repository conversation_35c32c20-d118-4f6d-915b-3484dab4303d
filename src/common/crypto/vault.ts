import invariant from 'tiny-invariant'
import { decryptAES, encryptAES } from './aes'
import { signHMAC, verifyHMAC } from './hmac'

export interface VersionedSecretID {
  name: string
  iat: number
}

export interface SecretFamily {
  name: string
  rotationPeriod: number
  type: 'symmetric' | 'hmac'
  parent?: SymmetricSecretFamily
}

export type SymmetricSecretFamily = SecretFamily & { type: 'symmetric' }
export type HMACSecretFamily = SecretFamily & { type: 'hmac' }

export interface RootSecretClient {
  encryptRoot(plaintext: Uint8Array): Promise<Uint8Array>
  decryptRoot(ciphertext: Uint8Array): Promise<Uint8Array>
}

export interface SecretStorageClient {
  fetch(id: VersionedSecretID): Promise<Uint8Array | null>
  insert(id: VersionedSecretID, secret: Uint8Array): Promise<void>
  isInsertionConflictError(error: unknown): boolean
}

export class Vault {
  readonly rootSecretClient: RootSecretClient
  readonly secretStorageClient: SecretStorageClient
  #caches: Map<string, Map<number, CryptoKey>>

  constructor(rootSecretClient: RootSecretClient, secretStorageClient: SecretStorageClient) {
    this.rootSecretClient = rootSecretClient
    this.secretStorageClient = secretStorageClient
    this.#caches = new Map()
  }

  async encrypt(
    plaintext: Uint8Array,
    family: SymmetricSecretFamily,
    date: Date,
  ): Promise<Uint8Array> {
    const key = await this.fetchOrCreateDate(family, date)
    const ciphertext = await encryptAES(plaintext, key)
    return ciphertext
  }

  async decrypt(
    ciphertext: Uint8Array,
    family: SymmetricSecretFamily,
    date: Date,
  ): Promise<Uint8Array> {
    const key = await this.fetchOrCreateDate(family, date)
    const plaintext = await decryptAES(ciphertext, key)
    return plaintext
  }

  async sign(message: Uint8Array, family: HMACSecretFamily, date: Date): Promise<Uint8Array> {
    const key = await this.fetchOrCreateDate(family, date)
    const signature = await signHMAC(message, key)
    return signature
  }

  async verify(
    signature: Uint8Array,
    message: Uint8Array,
    family: HMACSecretFamily,
    date: Date,
  ): Promise<boolean> {
    const key = await this.fetchOrCreateDate(family, date)
    const isValid = await verifyHMAC(signature, message, key)
    return isValid
  }

  private importKey(type: 'symmetric' | 'hmac', rawKey: Uint8Array): Promise<CryptoKey> {
    if (type === 'symmetric') {
      return crypto.subtle.importKey('raw', rawKey, 'AES-GCM', false, ['encrypt', 'decrypt'])
    } else {
      return crypto.subtle.importKey(
        'raw',
        rawKey,
        {
          name: 'HMAC',
          hash: 'SHA-256',
        },
        false,
        ['sign', 'verify'],
      )
    }
  }

  private async generateKey(type: 'symmetric' | 'hmac'): Promise<[CryptoKey, Uint8Array]> {
    let extractable: CryptoKey
    if (type === 'symmetric') {
      extractable = await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, [
        'encrypt',
        'decrypt',
      ])
    } else {
      extractable = await crypto.subtle.generateKey(
        {
          name: 'HMAC',
          hash: { name: 'SHA-256' },
          length: 256,
        },
        true,
        ['sign', 'verify'],
      )
    }

    const rawBuffer = await crypto.subtle.exportKey('raw', extractable)
    const rawKey = new Uint8Array(rawBuffer)
    const key = await this.importKey(type, rawKey)
    return [key, rawKey]
  }

  private async fetchKey(family: SecretFamily, iat: number): Promise<CryptoKey | undefined> {
    const encryptedKey = await this.secretStorageClient.fetch({ name: family.name, iat })
    if (!encryptedKey) {
      return
    }

    let rawKey: Uint8Array
    if (family.parent) {
      const r = family.parent.rotationPeriod
      const parentKey = await this.fetchOrCreate(family.parent, Math.floor(iat / r) * r)
      rawKey = await decryptAES(encryptedKey, parentKey)
    } else {
      rawKey = await this.rootSecretClient.decryptRoot(encryptedKey)
    }

    const key = await this.importKey(family.type, rawKey)
    crypto.getRandomValues(rawKey)
    return key
  }

  private async createKey(family: SecretFamily, iat: number): Promise<CryptoKey> {
    const [key, rawKey] = await this.generateKey(family.type)
    let encryptedKey: Uint8Array
    if (family.parent) {
      const r = family.parent.rotationPeriod
      const parentKey = await this.fetchOrCreate(family.parent, Math.floor(iat / r) * r)
      encryptedKey = await encryptAES(rawKey, parentKey)
    } else {
      encryptedKey = await this.rootSecretClient.encryptRoot(rawKey)
    }

    crypto.getRandomValues(rawKey)
    await this.secretStorageClient.insert({ name: family.name, iat }, encryptedKey)
    return key
  }

  private async fetchOrCreate(family: SecretFamily, iat: number): Promise<CryptoKey> {
    let cachedFamily = this.#caches.get(family.name)
    if (!cachedFamily) {
      cachedFamily = new Map()
      this.#caches.set(family.name, cachedFamily)
    }

    let hasInsertionConflict = false
    while (true) {
      const cachedKey = cachedFamily.get(iat)
      if (cachedKey) {
        return cachedKey
      }

      const existingKey = await this.fetchKey(family, iat)
      if (existingKey) {
        cachedFamily.set(iat, existingKey)
        return existingKey
      }

      invariant(!hasInsertionConflict)

      try {
        const key = await this.createKey(family, iat)
        cachedFamily.set(iat, key)
        return key
      } catch (error) {
        if (this.secretStorageClient.isInsertionConflictError(error)) {
          hasInsertionConflict = true
          continue
        }
        throw error
      }
    }
  }

  private fetchOrCreateDate(family: SecretFamily, date: Date): Promise<CryptoKey> {
    const secondsSince1970 = date.getTime() / 1000
    const iat = Math.floor(secondsSince1970 / family.rotationPeriod) * family.rotationPeriod
    return this.fetchOrCreate(family, iat)
  }
}
