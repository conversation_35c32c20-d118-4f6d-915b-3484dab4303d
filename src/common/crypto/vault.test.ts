import {
  Vault,
  RootSecretClient,
  SecretStorageClient,
  SymmetricSecretFamily,
  HMACSecretFamily,
  VersionedSecretID,
} from './vault'
import { encryptAES, decryptAES } from './aes'

class MockRootSecretClient implements RootSecretClient {
  private key: Crypto<PERSON>ey

  constructor(key: <PERSON><PERSON><PERSON><PERSON><PERSON>) {
    this.key = key
  }

  async encryptRoot(plaintext: Uint8Array): Promise<Uint8Array> {
    return encryptAES(plaintext, this.key)
  }

  async decryptRoot(ciphertext: Uint8Array): Promise<Uint8Array> {
    return decryptAES(ciphertext, this.key)
  }
}

let rootSecretClient: MockRootSecretClient
beforeAll(async () => {
  const key = await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, [
    'encrypt',
    'decrypt',
  ])

  rootSecretClient = new MockRootSecretClient(key)
})

class MockSecretStorageClient implements SecretStorageClient {
  private storage: Map<string, Map<number, Uint8Array>>

  constructor() {
    this.storage = new Map()
  }

  async fetch(id: VersionedSecretID): Promise<Uint8Array | null> {
    const familyMap = this.storage.get(id.name)
    if (familyMap) {
      return familyMap.get(id.iat) || null
    }
    return null
  }

  async insert(id: VersionedSecretID, secret: Uint8Array): Promise<void> {
    let familyMap = this.storage.get(id.name)
    if (!familyMap) {
      familyMap = new Map()
      this.storage.set(id.name, familyMap)
    }
    if (familyMap.get(id.iat)) {
      throw new Error(`Insertion conflict for ${id.name}:${id.iat}`)
    }
    familyMap.set(id.iat, secret)
  }

  isInsertionConflictError(error: unknown): boolean {
    return error instanceof Error && error.message.startsWith('Insertion conflict for ')
  }
}

describe('Vault', () => {
  const WEEK_IN_SECONDS = 3600 * 24 * 7
  const family: SymmetricSecretFamily = {
    name: 'aes',
    rotationPeriod: WEEK_IN_SECONDS,
    type: 'symmetric',
  }
  const hmacFamily: HMACSecretFamily = {
    name: 'hmac',
    rotationPeriod: WEEK_IN_SECONDS,
    type: 'hmac',
    parent: family,
  }
  const textEncoder = new TextEncoder()

  describe('basic encrypt and sign', async () => {
    let vault: Vault

    beforeEach(() => {
      const secretStorageClient = new MockSecretStorageClient()
      vault = new Vault(rootSecretClient, secretStorageClient)
    })

    const date = new Date()

    it('should encrypt data using symmetric key', async () => {
      const plaintext = textEncoder.encode('test plaintext')
      const ciphertext = await vault.encrypt(plaintext, family, date)
      expect(ciphertext).not.toEqual(plaintext)
    })

    it('should decrypt data using symmetric key', async () => {
      const plaintext = textEncoder.encode('test plaintext')
      const ciphertext = await vault.encrypt(plaintext, family, date)
      const decrypted = await vault.decrypt(ciphertext, family, date)
      expect(new TextDecoder().decode(decrypted)).toEqual('test plaintext')
    })

    it('should sign data using HMAC', async () => {
      const message = textEncoder.encode('test message')
      const signature = await vault.sign(message, hmacFamily, date)
      expect(signature).not.toEqual(message)
    })

    it('should verify HMAC signature', async () => {
      const message = textEncoder.encode('test message')
      const signature = await vault.sign(message, hmacFamily, date)
      const isValid = await vault.verify(signature, message, hmacFamily, date)
      expect(isValid).toBe(true)
    })
  })

  describe('key rotation', async () => {
    let vault: Vault

    const initialDate = new Date('2024-01-01')
    const rotatedDate = new Date(initialDate.getTime() + WEEK_IN_SECONDS * 1000 + 1)

    beforeEach(() => {
      const secretStorageClient = new MockSecretStorageClient()
      vault = new Vault(rootSecretClient, secretStorageClient)
    })

    it('should encrypt and decrypt data using symmetric key with key rotation', async () => {
      const plaintext = textEncoder.encode('test plaintext')

      const initialCiphertext = await vault.encrypt(plaintext, family, initialDate)
      const initialDecrypted = await vault.decrypt(initialCiphertext, family, initialDate)
      expect(new TextDecoder().decode(initialDecrypted)).toEqual('test plaintext')

      const rotatedCiphertext = await vault.encrypt(plaintext, family, rotatedDate)
      const rotatedDecrypted = await vault.decrypt(rotatedCiphertext, family, rotatedDate)
      expect(new TextDecoder().decode(rotatedDecrypted)).toEqual('test plaintext')

      // Ensure that decrypting rotated ciphertext with the initial date fails
      let decryptionFailed = false
      try {
        await vault.decrypt(rotatedCiphertext, family, initialDate)
      } catch {
        decryptionFailed = true
      }
      expect(decryptionFailed).toBe(true)
    })

    it('should sign and verify data using HMAC with nested parent and key rotation', async () => {
      const message = textEncoder.encode('test message')

      const initialSignature = await vault.sign(message, hmacFamily, initialDate)
      const initialIsValid = await vault.verify(initialSignature, message, hmacFamily, initialDate)
      expect(initialIsValid).toBe(true)

      const rotatedSignature = await vault.sign(message, hmacFamily, rotatedDate)
      const rotatedIsValid = await vault.verify(rotatedSignature, message, hmacFamily, rotatedDate)
      expect(rotatedIsValid).toBe(true)

      // Ensure that verifying rotated signature with the initial date fails
      const initialSignatureInvalid = await vault.verify(
        rotatedSignature,
        message,
        hmacFamily,
        initialDate,
      )
      expect(initialSignatureInvalid).toBe(false)
    })
  })

  it('should handle concurrent key generation', async () => {
    const secretStorageClient = new MockSecretStorageClient()
    const vault1 = new Vault(rootSecretClient, secretStorageClient)

    const date = new Date()
    const plaintext = textEncoder.encode('test plaintext')

    const ciphertext = await vault1.encrypt(plaintext, family, date)

    class MissOnceStorageClient implements SecretStorageClient {
      base: SecretStorageClient
      missedOnce: Record<string, boolean>
      encounteredInsertionConflict: boolean

      constructor(base: SecretStorageClient) {
        this.base = base
        this.missedOnce = {}
        this.encounteredInsertionConflict = false
      }

      async fetch(id: VersionedSecretID): Promise<Uint8Array | null> {
        const key = `${id.name}:${id.iat}`
        if (key in this.missedOnce) {
          return this.base.fetch(id)
        }
        this.missedOnce[key] = true
        return null
      }

      insert(id: VersionedSecretID, secret: Uint8Array): Promise<void> {
        return this.base.insert(id, secret)
      }

      isInsertionConflictError(error: unknown): boolean {
        const result = this.base.isInsertionConflictError(error)
        this.encounteredInsertionConflict = this.encounteredInsertionConflict || result
        return result
      }
    }

    const missOnceStorageClient = new MissOnceStorageClient(secretStorageClient)
    const vault2 = new Vault(rootSecretClient, missOnceStorageClient)
    const decrypted = await vault2.decrypt(ciphertext, family, date)

    expect(decrypted).toEqual(plaintext)
    expect(missOnceStorageClient.encounteredInsertionConflict).toBe(true)
  })

  it('should cache access', async () => {
    const secretStorageClient = new MockSecretStorageClient()
    const vault1 = new Vault(rootSecretClient, secretStorageClient)

    const date = new Date()
    const plaintext = textEncoder.encode('test plaintext')
    await vault1.encrypt(plaintext, family, date)

    let counter = 0
    const vault2 = new Vault(
      {
        encryptRoot: async (plaintext) => {
          return rootSecretClient.encryptRoot(plaintext)
        },
        decryptRoot: async (ciphertext) => {
          counter += 1
          return rootSecretClient.decryptRoot(ciphertext)
        },
      },
      secretStorageClient,
    )

    for (let i = 0; i < 5; i += 1) {
      await vault2.encrypt(plaintext, family, date)
    }

    expect(counter).toEqual(1)
  })
})
