export async function signHMAC(message: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON><PERSON><PERSON>): Promise<Uint8Array> {
  const signature = await crypto.subtle.sign({ name: 'HM<PERSON>' }, key, message)
  return new Uint8Array(signature)
}

export async function verifyHMAC(
  signature: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  message: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  key: <PERSON><PERSON><PERSON><PERSON><PERSON>,
): Promise<boolean> {
  const isValid = await crypto.subtle.verify({ name: 'HM<PERSON>' }, key, signature, message)
  return isValid
}
