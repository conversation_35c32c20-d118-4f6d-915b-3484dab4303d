# Sample Data Bootstrap and Test Creation

This guide provides instructions on bootstrapping the development database with sample data and creating tests using YAML files.

## Prerequisites

Before you begin, ensure you have the following tools installed:

- `yq`: YAML processor
- `jq`: JSON processor
- `curl`: Command-line tool for making HTTP requests

### Installing Prerequisites

We recommend using Homebrew to install `yq` and `jq` on macOS or Linux:

```bash
brew install yq jq
```

For other operating systems or alternative installation methods, please refer to the official documentation for each tool.

## Bootstrapping Dev Database with Data

Follow these steps to populate the development database with sample data.

### Sync Problems to Database

Run the following command to sync problems to the database:

```bash
curl -X POST http://localhost:5189/demo2/sync \
     -H "Content-Type: application/json" \
     -d "$(yq -o json full.yml)" | jq
```

Expected output:

```json
{
  "bots": [
    {
      "id": "test-bot",
      "version": 0,
      "changed": false
    },
    {
      "id": "coder",
      "version": 0,
      "changed": false
    }
  ],
  "problems": [
    {
      "id": "minimum-data-analysis",
      "version": 2,
      "changed": true
    }
  ]
}
```

### Create a Test

To create a test, use the following command:

```bash
curl -X POST http://localhost:5189/demo2/tests \
     -H "Content-Type: application/json" \
     -d "$(yq -o json test.yml)" | jq
```

Expected output:

```json
{
  "testId": "fdb034f5-4803-4c7a-9aa5-88a2b47c71d4",
  "url": "http://localhost:5188/demo2/tests/fdb034f5-4803-4c7a-9aa5-88a2b47c71d4"
}
```

## Troubleshooting

If you encounter any issues:

1. Ensure all prerequisites are correctly installed.
2. Check that the development server is running on the specified port.
3. Verify the contents of your YAML files (`full.yml` and `test.yml`).

For further assistance, please contact the development team.