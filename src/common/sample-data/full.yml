problems:
  - id: minimum-data-analysis
    axiiaMetadata:
      problemId: minimum-data-analysis
      initialMessage: |
        Welcome to the prompt engineering assessment test.

        You will have fourty minutes to complete this test. Hurry!
      prompt:
        text: |
          You need to answer questions for a prompt engineering assessment test.

          The following initial message is provided to the student:

          {{problem_description}}
        replacementKeys: ['problem_description']
    botPresetList:
      - test-bot
      - coder
    materialList:
      - name: material-a
        problemId: minimum-data-analysis
        title: Material A
        content: |
          # Material A

          Hello, world!

          - A
          - B
          - C
      - name: material-b
        problemId: minimum-data-analysis
        title: Material B
        content: |
          # Material B

          Hello, world!

          1. A
          2. B
          3. C
    submissionSpec:
      problemId: minimum-data-analysis
      requiredPhrases: []
      prefill: |
        This is a sample submission. You probably want to delete everything here.
    feedbackSpec:
      problemId: minimum-data-analysis
      keys:
        - overall
      prompt:
        text: |
          Judge the following submission as an answer for a prompt engineering assessment test.

          <submission>
          {{submission}}
          </submission>

          You should output in XML, either 
          
          <overall>good</overall>

          or 

          <overall>bad</overall>
        replacementKeys: ['submission']
      template: |
        The result given by ChatGPT is {{overall}}.
  - id: simple-prompt
    axiiaMetadata:
      problemId: simple-prompt
      initialMessage: |
        Welcome to the prompt engineering assessment test.

        You need to write a prompt to solve a label prediction task.
      prompt:
        text: |
          You need to answer questions for a prompt engineering assessment test.

          The following initial message is provided to the student:

          {{problem_description}}
        replacementKeys: ['problem_description']
    botPresetList:
      - test-bot
      - coder
    materialList: []
    submissionSpec:
      problemId: simple-prompt
      requiredPhrases: ['{{input}}']
      prefill: |
        Generate the answer to the following task.

        {{input}}

        You can write down your reasoning first. At the end of your output, give the answer wrapped in an XML tag, like

        <result>A</result>
    labelPredGroupList:
      - name: sample-task
        title: Sample
        hidesInput: false
        pairs:
          - name: p0
            input: |
              What's capital of China?

              A. Beijing
              B. Shanghai
              C. New York
            output: A
      - name: public-questions
        title: Public Questions
        hidesInput: false
        pairs:
          - name: p1
            input: |
              Which planet is known as the Red Planet?

              A. Venus
              B. Mars
              C. Jupiter
            output: B
          - name: p2
            input: |
              Who painted the Mona Lisa?

              A. Vincent van Gogh
              B. Pablo Picasso
              C. Leonardo da Vinci
            output: C
          - name: p3
            input: |
              What is the largest ocean on Earth?

              A. Atlantic Ocean
              B. Indian Ocean
              C. Pacific Ocean
            output: C
      - name: private-questions
        title: Private Questions
        hidesInput: true
        pairs:
          - name: p4
            input: |
              Which country is home to the kangaroo?

              A. New Zealand
              B. South Africa
              C. Australia
            output: C
          - name: p5
            input: |
              What is the chemical symbol for gold?

              A. Au
              B. Ag
              C. Fe
            output: A
          - name: p6
            input: |
              Who wrote the play "Romeo and Juliet"?

              A. Charles Dickens
              B. William Shakespeare
              C. Jane Austen
            output: B
          - name: p7
            input: |
              What is the largest planet in our solar system?

              A. Earth
              B. Mars
              C. Jupiter
            output: C


bots:
  - id: test-bot
    title: Test Bot
    prompt: Be a helpful AI assistant.
  - id: coder
    title: Coder
    prompt: You are an experienced coder.
