import equal from 'fast-deep-equal'

export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export function assertWithHttpError(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  condition: any,
  errorCode: number,
  errorMessage: string | undefined = undefined,
): asserts condition {
  if (!condition) {
    throw new Response(null, { status: errorCode, statusText: errorMessage })
  }
}

export function deepEqual<T>(a: T, b: T): boolean {
  return equal(a, b)
}

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface AnyObj<T = any> {
  [key: string]: T
}
