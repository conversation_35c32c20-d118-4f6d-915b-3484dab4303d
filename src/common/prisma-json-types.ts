/* eslint-disable @typescript-eslint/no-namespace */
declare global {
  namespace PrismaJson {
    interface PeajResolvedBotPreset {
      id: string
      version: number
    }

    type PeajResolvedBotPresetList = PeajResolvedBotPreset[]

    interface PeajTestProblem {
      problem_id: string
      problem_version: number
      progress_id: string | null
      resolved_problem_id: string
    }
  }
}

export {}
