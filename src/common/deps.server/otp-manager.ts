import dependencyManager from './manager'
import getPrismaClient from './prisma-client'

export interface OTPFamily {
  name: string
  duration: number
  length: number
  charset: 'digits' | 'alphanum'
}

export interface OTPManager {
  generate(subject: string): Promise<[string, string]>
  verify(otpID: string, otpCode: string): Promise<string | null>
}

function generateOTPID(): string {
  return crypto.randomUUID()
}

function generateOTPCode(family: OTPFamily): string {
  let charset = '0123456789'
  if (family.charset === 'alphanum') {
    charset += 'abcdefghijklmnopqrstuvwxyz'
  }
  const max = Math.floor(256 / charset.length) * charset.length

  const buffer = new Uint8Array(32)
  let i = buffer.length

  function generate(): number {
    if (i >= buffer.length) {
      crypto.getRandomValues(buffer)
      i = 0
    }
    const value = buffer[i]
    i += 1
    return value
  }

  let code = ''
  for (let i = 0; i < family.length; i += 1) {
    while (true) {
      const number = generate()
      if (number >= max) {
        continue
      }
      code += charset[number % charset.length]
      break
    }
  }

  return code
}

class OTPMasterManager {
  async generate(family: OTPFamily, subject: string): Promise<[string, string]> {
    const client = await getPrismaClient()

    const iat = new Date()
    const exp = new Date(iat.getTime() + family.duration * 1000)
    const otpID = generateOTPID()
    const otpCode = generateOTPCode(family)
    const attemptLeft = 5
    const data = { iat, exp, otpID, otpCode, attemptLeft }

    const row = await client.oTP.upsert({
      where: {
        name_subject: { name: family.name, subject },
      },
      create: {
        name: family.name,
        subject,
        ...data,
      },
      update: data,
    })
    return [row.otpID, row.otpCode]
  }

  async verify(family: OTPFamily, otpID: string, otpCode: string): Promise<string | null> {
    const client = await getPrismaClient()
    const now = new Date()

    const subject: string | null = await client.$transaction(
      async (tx) => {
        const row = await tx.oTP.findFirst({
          where: {
            otpID,
          },
        })
        if (!row) {
          return null
        }
        if (row.name !== family.name) {
          return null
        }
        if (now <= row.iat || now >= row.exp || row.attemptLeft <= 0) {
          return null
        }

        const isValid = row.otpCode === otpCode
        const attemptLeft = isValid ? 0 : row.attemptLeft - 1

        await tx.oTP.update({
          where: {
            name_subject: { name: family.name, subject: row.subject },
          },
          data: {
            attemptLeft,
          },
        })

        return isValid ? row.subject : null
      },
      {
        isolationLevel: 'Serializable',
      },
    )

    return subject
  }

  bind(family: OTPFamily): OTPManager {
    return {
      generate: (subject) => this.generate(family, subject),
      verify: (otpID, otpCode) => this.verify(family, otpID, otpCode),
    }
  }
}

dependencyManager.registerIfNeeded(OTPMasterManager, async () => new OTPMasterManager())

export default async function getOTPManager(family: OTPFamily): Promise<OTPManager> {
  const master = await dependencyManager.get(OTPMasterManager)
  return master.bind(family)
}
