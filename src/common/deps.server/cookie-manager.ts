import { <PERSON><PERSON>, <PERSON>ieOptions, createCookie } from '@remix-run/node'
import { HMACSecretFamily, SymmetricSecretFamily } from '@common/crypto/vault'
import dependencyManager from './manager'
import getVault from './vault'

const dataKeyFamily: SymmetricSecretFamily = {
  name: `${process.env.STACK}.data-key`,
  // Every three months
  rotationPeriod: 3600 * 24 * 30 * 3,
  type: 'symmetric',
}

const cookieSecretFamily: HMACSecretFamily = {
  name: `${process.env.STACK}.cookie-secret`,
  // Every two weeks
  rotationPeriod: 3600 * 24 * 15,
  type: 'hmac',
  parent: dataKeyFamily,
}

function iatForCookieSecret(date: Date) {
  const secondsSince1970 = date.getTime() / 1000
  const rotationPeriod = cookieSecretFamily.rotationPeriod
  return Math.floor(secondsSince1970 / rotationPeriod) * rotationPeriod
}

export class CookieManager {
  cookies: Map<string, [number, Cookie]>

  constructor() {
    this.cookies = new Map()
  }

  async createCookie(name: string, options: Partial<CookieOptions> = {}): Promise<Cookie> {
    const currentIat = iatForCookieSecret(new Date())

    const existingPair = this.cookies.get(name)
    if (existingPair) {
      const [iat, cookie] = existingPair
      if (iat === currentIat) {
        return cookie
      }
    }

    const vault = await getVault()
    const encoder = new TextEncoder()
    const secrets: string[] = []

    for (let i = 0; i < 4; i += 1) {
      const iat = currentIat - i * cookieSecretFamily.rotationPeriod
      const message = encoder.encode(name)
      const derived = await vault.sign(message, cookieSecretFamily, new Date(iat * 1000))
      secrets.push(Buffer.from(derived).toString('base64'))
    }

    const mergedOptions: CookieOptions = {
      path: '/',
      sameSite: 'lax',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      secrets,
    }
    Object.assign(mergedOptions, options)

    const cookie = createCookie(name, mergedOptions)
    this.cookies.set(name, [currentIat, cookie])
    return cookie
  }
}

dependencyManager.registerIfNeeded(CookieManager, async () => new CookieManager())

export default async function getCookieManager(): Promise<CookieManager> {
  return await dependencyManager.get(CookieManager)
}
