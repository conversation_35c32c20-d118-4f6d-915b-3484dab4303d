import invariant from 'tiny-invariant'
import {
  RootSecretClient,
  SecretStorageClient,
  Vault,
  VersionedSecretID,
} from '@common/crypto/vault'
import dependencyManager from './manager'
import { DecryptCommand, EncryptCommand, KMSClient } from '@aws-sdk/client-kms'
import logger from './logger'
import getPrismaClient from './prisma-client'
import { Prisma } from '@prisma/client'

class KMSRootSecretClient implements RootSecretClient {
  rootKeyID: string

  constructor() {
    const keyID = process.env['ROOT_KEY_ID']
    invariant(keyID, 'Root key ARN is not provided!')
    this.rootKeyID = keyID
  }

  async encryptRoot(plaintext: Uint8Array): Promise<Uint8Array> {
    const client = new KMSClient()
    const command = new EncryptCommand({
      KeyId: this.rootKeyID,
      Plaintext: plaintext,
    })
    try {
      const data = await client.send(command)
      invariant(data.CiphertextBlob, 'Ciphertext is not available.')
      return data.CiphertextBlob
    } catch (error) {
      logger.error(error, 'An error occurred when encrypting using the root key.')
      throw error
    }
  }

  async decryptRoot(ciphertext: Uint8Array): Promise<Uint8Array> {
    const client = new KMSClient()
    const command = new DecryptCommand({
      KeyId: this.rootKeyID,
      CiphertextBlob: ciphertext,
    })
    try {
      const data = await client.send(command)
      invariant(data.Plaintext, 'Plaintext is not available.')
      return data.Plaintext
    } catch (error) {
      logger.error(error, 'An error occurred when decrypting using the root key.')
      throw error
    }
  }
}

class PostgresSecretStorageClient implements SecretStorageClient {
  async fetch(id: VersionedSecretID): Promise<Uint8Array | null> {
    const client = await getPrismaClient()
    const row = await client.secret.findFirst({
      where: {
        name: id.name,
        iat: id.iat,
      },
    })
    if (!row) {
      return null
    }
    return row.payload
  }

  async insert(id: VersionedSecretID, secret: Uint8Array): Promise<void> {
    const client = await getPrismaClient()
    await client.secret.create({
      data: {
        ...id,
        payload: Buffer.from(secret),
      },
    })
  }

  isInsertionConflictError(error: unknown): boolean {
    if (!(error instanceof Prisma.PrismaClientKnownRequestError)) {
      return false
    }
    // Unique constraint violation.
    return error.code === 'P23505'
  }
}

dependencyManager.registerIfNeeded(Vault, async () => {
  const rootSecretClient = new KMSRootSecretClient()
  const secretStorageClinet = new PostgresSecretStorageClient()
  return new Vault(rootSecretClient, secretStorageClinet)
})

export default async function getVault(): Promise<Vault> {
  return await dependencyManager.get(Vault)
}
