import logger from './logger'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Constructor<T> = new (...args: any[]) => T
type DependencyConstructor<T> = () => Promise<T>

interface Dependency<T> {
  constructor: DependencyConstructor<T>
  constructorName: string
  ttl: number
  instance: T | null
  expiration: number
  pendingPromise: Promise<T> | null
}

export class DependencyManager {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private dependencies: Map<Constructor<any>, Dependency<any>> = new Map()

  register<T>(
    key: Constructor<T>,
    constructor: DependencyConstructor<T>,
    ttl: number = 0,
    constructorName: string | undefined = undefined,
  ) {
    this.dependencies.set(key, {
      constructor,
      constructorName: constructorName || key.name,
      ttl,
      instance: null,
      expiration: 0,
      pendingPromise: null,
    })
  }

  registerIfNeeded<T>(
    key: Constructor<T>,
    constructor: DependencyConstructor<T>,
    ttl: number = 0,
    constructorName: string | undefined = undefined,
  ) {
    if (this.dependencies.has(key)) {
      return
    }
    this.register(key, constructor, ttl, constructorName)
  }

  async get<T>(key: Constructor<T>): Promise<T> {
    const dep = this.dependencies.get(key) as Dependency<T>
    const name = dep.constructorName

    if (!dep) {
      throw new Error(`Dependency "${name}" is not registered.`)
    }

    const currentTime = Date.now()

    if (dep.instance && currentTime <= dep.expiration) {
      logger.debug(
        `Re-use ${name} since current time is ${currentTime} and dep is set to expire at ${dep.expiration}.`,
      )
      return dep.instance
    }

    try {
      if (dep.pendingPromise) {
        const instance = await dep.pendingPromise
        return instance
      }

      dep.instance = null
      dep.pendingPromise = dep.constructor()
      const instance = await dep.pendingPromise

      dep.instance = instance
      let ttl = dep.ttl
      if (ttl === 0) {
        ttl = 3600 * 24 * 365
      }
      dep.expiration = Date.now() + ttl * 1000
      dep.pendingPromise = null

      logger.info(`Dependency ${name} has been constructed. It should expire in ${dep.expiration}.`)

      return instance
    } catch (error) {
      dep.instance = null
      dep.pendingPromise = null
      this.dependencies.set(key, dep)

      logger.error(error, `An error occurred when constructing dependency ${name}`)
      throw error
    }
  }
}

declare global {
  // eslint-disable-next-line
  var _dependencyManager: DependencyManager | undefined
}

const dependencyManager = global._dependencyManager || new DependencyManager()

export default dependencyManager
