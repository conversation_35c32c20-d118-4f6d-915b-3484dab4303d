import { PrismaClient } from '@prisma/client'
import { PrismaPg } from '@prisma/adapter-pg'
import invariant from 'tiny-invariant'
import dependencyManager from './manager'

function createAdapter(): PrismaPg {
  const url = process.env.DATABASE_URL
  invariant(url, 'DATABASE_URL is required!')
  return new PrismaPg({ connectionString: url })
}

dependencyManager.registerIfNeeded(
  PrismaClient,
  async () => {
    const adapter = createAdapter()
    return new PrismaClient({ adapter })
  },
  0,
  'PrismaClient',
)

export default async function getPrismaClient(): Promise<PrismaClient> {
  return await dependencyManager.get(PrismaClient)
}
