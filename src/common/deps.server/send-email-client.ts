import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager'
import { Transporter, createTransport } from 'nodemailer'
import dependencyManager from './manager'
import logger from './logger'

export class SendEmailClient {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async sendEmail(from: string, to: string, subject: string, html: string): Promise<void> {
    throw new Error('Unimplemented')
  }
}

class CLISendEmailClient extends SendEmailClient {
  async sendEmail(from: string, to: string, subject: string, html: string): Promise<void> {
    logger.info(`Send email from ${from} to ${to}.\nSubject: ${subject}\n${html}`)
  }
}

class SMTPSendEmailClient extends SendEmailClient {
  private transporter: Transporter

  constructor(host: string, port: number, secure: boolean, user: string, pass: string) {
    super()

    this.transporter = createTransport({
      host,
      port,
      secure,
      auth: { user, pass },
    })
  }

  async sendEmail(from: string, to: string, subject: string, html: string) {
    try {
      await this.transporter.sendMail({ from, to, subject, html })
    } catch (error) {
      logger.error(error, `An error occurred when sending an email from ${from} to ${to}.`)
    }
  }
}

async function loadSTMPCredentialsFromAWS(): Promise<Record<string, unknown>> {
  const arn = process.env.EMAIL_SMTP_ARN
  if (!arn) {
    throw new Error('Email SMTP ARN is not provided.')
  }

  const client = new SecretsManagerClient()
  const command = new GetSecretValueCommand({ SecretId: arn })
  const response = await client.send(command)

  if (!response.SecretString) {
    throw new Error('Failed to fetch secrets about SMTP credentials as string.')
  }
  const credentials = JSON.parse(response.SecretString)
  if (typeof credentials != 'object') {
    throw new Error('Invalid SMTP credentials.')
  }
  return credentials as Record<string, unknown>
}

async function createSMTPSendEmailClient(): Promise<SMTPSendEmailClient> {
  const credentials = await loadSTMPCredentialsFromAWS()

  const getString = (key: string) => {
    const value = credentials[key]
    if (typeof value != 'string') {
      throw new Error(`Invalid key ${key} on SMTP credentials!`)
    }
    return value
  }

  const getNumber = (key: string) => {
    const value = credentials[key]
    if (typeof value != 'number') {
      throw new Error(`Invalid key ${key} on SMTP credentials!`)
    }
    return value
  }

  const host = getString('smtp_host')
  const user = getString('smtp_user')
  const pass = getString('smtp_pass')
  const port = getNumber('smtp_port')
  const protocol = getString('smtp_protocol')

  const client = new SMTPSendEmailClient(host, port, protocol === 'SSL', user, pass)
  return client
}

dependencyManager.registerIfNeeded(
  SendEmailClient,
  async () => {
    if (process.env.EMAIL_CLI_MOCK === '1') {
      return new CLISendEmailClient()
    }
    return await createSMTPSendEmailClient()
  },
  3600,
)

export default async function getSendEmailClient(): Promise<SendEmailClient> {
  return await dependencyManager.get(SendEmailClient)
}
