import { Fetcher<PERSON>ithCom<PERSON>, useFetcher } from '@remix-run/react'
import { createContext, ReactNode, useContext, useEffect, useMemo, useRef, useState } from 'react'
import invariant from 'tiny-invariant'

interface FullStackState {
  url: string
  loaderData: unknown | undefined
  actionData: unknown | undefined
  state: 'idle' | 'submitting' | 'loading'
  submitRef: React.RefObject<FetcherWithComponents<unknown>>
}

const FullStackContext = createContext<FullStackState | null>(null)

function useFullStackState(): FullStackState {
  const state = useContext(FullStackContext)
  invariant(state !== null, 'useComponentLoaderData must be used within a FullStackComponent')
  return state
}

export function useComponentLoaderData<T>(): T {
  const { loaderData } = useFullStackState()
  invariant(
    loaderData !== undefined,
    'useComponentLoaderData must be used in a loaded FullStackComponent',
  )
  return loaderData as T
}

export function useComponentActionData<T>(): T | undefined {
  const { actionData } = useFullStackState()
  return actionData as T | undefined
}

export function useComponentSubmit(): (data: FormData) => void {
  const { url, submitRef } = useFullStackState()
  invariant(submitRef.current)
  const submit = submitRef.current
  return (data) => {
    submit.submit(data, {
      action: url,
      method: 'POST',
    })
  }
}

export function useComponentNavigationState(): 'idle' | 'submitting' | 'loading' {
  const { state } = useFullStackState()
  return state
}

interface FullStackComponentProps {
  url: string
  children: ReactNode
  fallback?: ReactNode
}

const KeyedFullStackComponent = ({ url, children, fallback }: FullStackComponentProps) => {
  const loader = useFetcher()
  const [loaderData, setLoaderData] = useState<unknown | undefined>(undefined)
  const submit = useFetcher()
  const submitRef = useRef<FetcherWithComponents<unknown>>(submit)
  submitRef.current = submit

  useEffect(() => {
    loader.load(url)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url])

  useEffect(() => {
    setLoaderData((prev: unknown) => {
      if (loader.data === undefined) {
        return prev
      }
      return loader.data
    })
  }, [loader.data, setLoaderData])

  let state: 'idle' | 'submitting' | 'loading'
  if (submit.state === 'submitting') {
    state = 'submitting'
  } else if (loader.state === 'loading') {
    state = 'loading'
  } else {
    state = 'idle'
  }

  const contextValue: FullStackState = useMemo(
    () => ({
      url,
      loaderData,
      actionData: submit.data,
      state,
      submitRef,
    }),
    [url, loaderData, submit.data, state, submitRef],
  )

  if (loaderData === undefined) {
    return fallback
  }

  return <FullStackContext.Provider value={contextValue}>{children}</FullStackContext.Provider>
}

const FullStackComponent = ({ url, children, fallback }: FullStackComponentProps) => {
  return (
    <KeyedFullStackComponent key={url} url={url} fallback={fallback}>
      {children}
    </KeyedFullStackComponent>
  )
}

export default FullStackComponent
