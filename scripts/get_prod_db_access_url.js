import process from 'process'
import { promises as fs } from 'fs'
import 'dotenv/config'
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager'

function getUpdatedEnvVars(envVars, databaseURL) {
  const pattern = /^DATABASE_URL=.*$/m
  const newline = `DATABASE_URL=${databaseURL}`

  if (pattern.test(envVars)) {
    return envVars.replace(pattern, newline)
  } else if (envVars.endsWith('\n')) {
    return envVars + databaseURL + '\n'
  } else {
    return envVars + '\n' + databaseURL + '\n'
  }
}

async function main() {
  const arn = process.env.PROD_DB_ADMIN_ARN
  const client = new SecretsManagerClient()
  const command = new GetSecretValueCommand({ SecretId: arn })
  const response = await client.send(command)
  const credentials = JSON.parse(response.SecretString)

  const databaseURL = credentials['database_url']
  const envVars = await fs.readFile('.env', 'utf-8')

  await fs.writeFile('.env', getUpdatedEnvVars(envVars, databaseURL), 'utf-8')
}

main()
