name: Deploy to Production

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  build-and-deploy:
    runs-on: ARM64
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-west-2
          role-to-assume: arn:aws:iam::767398125647:role/GithubActions_Axiia_Main

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        env:
          REPO_URL: 767398125647.dkr.ecr.us-west-2.amazonaws.com/axiia-app
        with:
          context: .
          push: true
          tags: 767398125647.dkr.ecr.us-west-2.amazonaws.com/axiia-app:${{ github.sha }}
          provenance: false

      - name: Deploy
        run: |
          # Replace IMAGE_TAG placeholder with commit SHA in task definition
          sed 's/{{IMAGE_TAG}}/${{ github.sha }}/g' .aws/task-definition.json > task-definition-updated.json
          
          # Register the new task definition
          aws ecs register-task-definition --cli-input-json file://task-definition-updated.json
          
          # Update the service with the new task definition
          aws ecs update-service --cluster axiia-v6-cluster --service axiia-app --task-definition axiia-app
