generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters", "multiSchema", "relationJoins"]
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public", "peassess"]
}

model PerspectiveUser {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  createdAt DateTime @default(now()) @map("created_at")

  name String @default("")

  @@map("perspective_users")
  @@schema("public")
}

model LeetcodeProblem {
  id            Int    @id @default(autoincrement())
  problemTitle  String @map("problem_title")
  problemDetail String @map("problem_detail")

  samples           LeetcodeSample[]
  sampleVersions    LeetcodeSampleVersion[]
  LeetcodeInterview LeetcodeInterview[]

  @@map("v1_leetcode_problems")
  @@schema("public")
}

enum Tag {
  train
  test
  evaluation
  
  @@schema("public")
}

model LeetcodeSample {
  id        Int  @id @default(autoincrement())
  problemID Int  @map("problem_id")
  tag       Tag
  deletedAt Int?

  problem  LeetcodeProblem         @relation(fields: [problemID], references: [id])
  versions LeetcodeSampleVersion[]

  @@map("v1_leetcode_samples")
  @@schema("public")
}

model LeetcodeSampleVersion {
  problemID Int    @map("problem_id")
  sampleID  Int    @map("sample_id")
  version   Int
  input     String
  output    String

  problem LeetcodeProblem @relation(fields: [problemID], references: [id])
  sample  LeetcodeSample  @relation(fields: [sampleID], references: [id])

  @@unique([problemID, version])
  @@map("v1_leetcode_sample_versions")
  @@schema("public")
}

model LeetcodeInterview {
  id             Int    @id @default(autoincrement())
  problemID      Int    @map("problem_id")
  version        Int
  randomID       String @unique @map("random_id")
  recipientName  String @map("recipient_name")
  recipientEmail String @map("recipient_email")

  problem     LeetcodeProblem      @relation(fields: [problemID], references: [id])
  submissions LeetcodeSubmission[]

  @@map("v1_leetcode_interviews")
  @@schema("public")
}

model LeetcodeSubmission {
  id                   Int    @id @default(autoincrement())
  interviewID          Int    @map("interview_id")
  prompt               String
  targetSampleVersions Int[]  @map("target_sample_versions")

  interview     LeetcodeInterview                @relation(fields: [interviewID], references: [id])
  sampleResults LeetcodeSubmissionSampleResult[]

  @@map("v1_leetcode_submissions")
  @@schema("public")
}

model LeetcodeSubmissionSampleResult {
  submissionID  Int     @map("submission_id")
  sampleVersion Int     @map("sample_version")
  sampleTag     Tag     @map("sample_tag")
  input         String
  output        String
  isCorrect     Boolean @map("is_correct")

  submission LeetcodeSubmission @relation(fields: [submissionID], references: [id])

  @@unique([submissionID, sampleVersion])
  @@map("v1_leetcode_interview_submission_sample_results")
  @@schema("public")
}

model D1InterviewInvitation {
  id             String   @id @default(uuid())
  recipientName  String   @map("recipient_name")
  recipientEmail String   @map("recipient_email")
  createdAt      DateTime @default(now()) @map("created_at")

  interview D1Interview?
  @@schema("public")
}

enum D1Stage {
  intro
  p1
  p2
  p3
  p4
  p5
  done

  @@schema("public")
}

model D1Interview {
  id           String   @id @default(uuid())
  invitationID String   @unique @map("invitation_id")
  stage        D1Stage
  createdAt    DateTime @default(now()) @map("created_at")

  chats               D1Chat[]
  p2SampleSubmissions D1P2SampleSubmission[]
  p5SampleSubmissions D1P5SampleSubmission[]
  midtermEvaluations  D1MidtermEvaluation[]

  invitation  D1InterviewInvitation @relation(fields: [invitationID], references: [id])
  submissions D1Submission[]

  @@map("d1_interviews")
  @@schema("public")
}

model D1Chat {
  id          String   @id @default(uuid())
  isAxiiaChat Boolean  @default(false) @map("is_axiia_chat")
  axiiaStage  D1Stage  @map("axiia_stage")
  topic       String
  instruction String
  createdAt   DateTime @default(now()) @map("created_at")
  interviewID String   @map("interview_id")

  userMessages      D1UserMessage[]
  assistantMessages D1AssistantMessage[]
  interview         D1Interview          @relation(fields: [interviewID], references: [id])

  @@map("d1_chats")
  @@schema("public")
}

model D1UserMessage {
  id        String   @id @default(uuid())
  chatID    String   @map("chat_id")
  createdAt DateTime @default(now()) @map("created_at")
  content   String

  chat D1Chat @relation(fields: [chatID], references: [id])

  @@map("d1_user_messages")
  @@schema("public")
}

model D1AssistantMessage {
  id                String    @id @default(uuid())
  chatID            String    @map("chat_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  lastModifiedAt    DateTime? @default(now()) @map("last_modified_at")
  usedSubstitutions String[]  @map("used_substs")
  content           String

  chat D1Chat @relation(fields: [chatID], references: [id])

  @@map("d1_assistant_messages")
  @@schema("public")
}

model D1P2SampleSubmission {
  id          String   @id @default(uuid())
  interviewID String   @map("interview_id")
  sampleID    String   @map("sample_id")
  createdAt   DateTime @default(now()) @map("created_at")
  prompt      String
  llmOutput   String   @map("llm_output")
  isCorrect   Boolean  @map("is_correct")

  interview D1Interview @relation(fields: [interviewID], references: [id])

  @@map("d1p2_sample_submissions")
  @@schema("public")
}

model D1P5SampleSubmission {
  id          String   @id @default(uuid())
  interviewID String   @map("interview_id")
  sampleID    String   @map("sample_id")
  createdAt   DateTime @default(now()) @map("created_at")
  prompt      String
  llmOutput   String   @map("llm_output")
  isCorrect   Boolean  @map("is_correct")

  interview D1Interview @relation(fields: [interviewID], references: [id])

  @@map("d1p5_sample_submissions")
  @@schema("public")
}

model D1P2TestSubmission {
  id          String                     @id @default(uuid())
  interviewID String                     @map("interview_id")
  createdAt   DateTime                   @default(now()) @map("created_at")
  prompt      String
  isComplete  Boolean                    @map("is_complete")
  samples     D1P2TestSubmissionSample[]

  @@map("d1p2_test_submissions")
  @@schema("public")
}

model D1P2TestSubmissionSample {
  submissionID String
  sampleID     String             @map("sample_id")
  llmOutput    String             @map("llm_output")
  isCorrect    Boolean            @map("is_correct")
  submission   D1P2TestSubmission @relation(fields: [submissionID], references: [id])

  @@unique([submissionID, sampleID])
  @@map("d1p2_test_submission_samples")
  @@schema("public")
}

model D1MidtermEvaluation {
  id          String            @id @default(uuid())
  stage       D1Stage
  level       D1EvaluationLevel
  feedback    String
  createdAt   DateTime          @default(now()) @map("created_at")
  interviewID String            @map("interview_id")

  interview D1Interview @relation(fields: [interviewID], references: [id])

  @@map("d1_midterm_evaluations")
  @@schema("public")
}

enum D1EvaluationLevel {
  low
  fair
  high

  @@schema("public")
}

enum D1Problem {
  p1
  p2
  p3
  p4
  p5

  @@schema("public")
}

model D1Submission {
  interviewID String    @map("interview_id")
  problem     D1Problem
  submission  String

  interview D1Interview @relation(fields: [interviewID], references: [id])

  @@unique([interviewID, problem])
  @@map("d1_submissions")
  @@schema("public")
}

model Secret {
  name    String
  iat     Int
  payload Bytes

  @@id([name, iat])
  @@map("secrets")
  @@schema("public")
}

model OTP {
  name        String
  subject     String
  otpID       String   @map("otp_id")
  otpCode     String   @map("otp_code")
  iat         DateTime
  exp         DateTime
  attemptLeft Int      @map("attempt_left")

  @@id([name, subject])
  @@unique([otpID])
  @@map("otps")
  @@schema("public")
}

model Employee {
  handle       String @id
  nickname     String
  privateEmail String @map("private_email")

  @@map("employees")
  @@schema("public")
}

model D3Chat {
  id          String   @id @default(uuid())
  topic       String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  botID       String   @map("bot_id")
  userID      String?  @map("user_id")

  user        AxiiaUser?             @relation(fields: [userID], references: [id])
  bot         D3QuestionBot?         @relation(fields: [botID], references: [id])
  botMessages D3QuestionBotMessage[]
  messages    D3Message[]

  @@map("d3_chats")
  @@schema("public")
}

model D3Message {
  id        String   @id @default(uuid())
  chatID    String   @map("chat_id")
  createdAt DateTime @default(now()) @map("created_at")
  content   String
  isUser    Boolean  @map("is_user")

  chat        D3Chat                 @relation(fields: [chatID], references: [id])
  botMessages D3QuestionBotMessage[]

  @@map("d3_messages")
  @@schema("public")
}

model D3QuestionBot {
  id           String   @id @default(uuid())
  name         String
  createdAt    DateTime @default(now()) @map("created_at")
  systemPrompt String   @map("system_prompt")
  chats        D3Chat[]

  @@map("d3_question_bots")
  @@schema("public")
}

model D3QuestionBotMessage {
  id              String   @id @default(uuid())
  chatID          String   @map("chat_id")
  sourceMessageID String   @map("source_message_id")
  createdAt       DateTime @default(now()) @map("created_at")
  content         String

  sourceMessage D3Message @relation(fields: [sourceMessageID], references: [id])
  chat          D3Chat    @relation(fields: [chatID], references: [id])

  @@map("d3_question_bot_messages")
  @@schema("public")
}

model AxiiaUser {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String
  createdAt DateTime @default(now()) @map("created_at")
  d3Chats   D3Chat[]

  @@map("axiia_users")
  @@schema("public")
}
