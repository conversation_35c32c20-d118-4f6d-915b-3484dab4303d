// Problem

model PeaProblem {
  id      String
  version Int

  vAxiiaMetdata   Int @map("v_axiia_metadata")
  vBotPresetList  Int @map("v_bot_preset_list")
  vMaterialList   Int @map("v_material_list")
  vSubmissionSpec Int @map("v_submission_spec")

  axiiaMetadata  PeaAxiiaMetadata        @relation(fields: [id, vAxiiaMetdata], references: [problemId, version])
  botPresetList  PeaProblemBotPresetList @relation(fields: [id, vBotPresetList], references: [problemId, version])
  materialList   PeaProblemMaterialList  @relation(fields: [id, vMaterialList], references: [problemId, version])
  submissionSpec PeaSubmissionSpec       @relation(fields: [id, vSubmissionSpec], references: [problemId, version])

  vFeedbackSpec Int?             @map("v_feedback_spec")
  feedbackSpec  PeaFeedbackSpec? @relation(fields: [id, vFeedbackSpec], references: [problemId, version])

  vLabelPredictionGroupList Int?                         @map("v_label_prediction_group_list")
  labelPredictionGroupList  PeaLabelPredictionGroupList? @relation(fields: [id, vLabelPredictionGroupList], references: [problemId, version])

  vCustomChatList Int?               @map("v_custom_chat_list")
  customChatList  PeaCustomChatList? @relation(fields: [id, vCustomChatList], references: [problemId, version])

  resolvedProblems PeaResolvedProblem[]
  feedbackRuns     PeaFeedbackRun[]

  @@id([id, version])
  @@map("problems")
  @@schema("peassess")
}

model PeaAxiiaMetadata {
  problemId String @map("problem_id")
  version   Int

  initialMessage       String @map("initial_message")
  prompt               String
  promptReplacementKey String @map("prompt_replacement_key")

  problems PeaProblem[]

  @@id([problemId, version])
  @@map("axiia_metadata")
  @@schema("peassess")
}

model PeaFeedbackSpec {
  problemId String @map("problem_id")
  version   Int

  keys String[]

  prompt               String
  promptReplacementKey String @map("prompt_replacement_key")
  template             String

  PeaProblem PeaProblem[]

  @@id([problemId, version])
  @@map("feedback_spec")
  @@schema("peassess")
}

model PeaBotPreset {
  id      String
  version Int
  title   String
  prompt  String

  @@id([id, version])
  @@map("bot_presets")
  @@schema("peassess")
}

model PeaProblemBotPresetList {
  problemId String   @map("problem_id")
  version   Int
  presets   String[]

  problems PeaProblem[]

  @@id([problemId, version])
  @@map("bot_preset_lists")
  @@schema("peassess")
}

model PeaMaterial {
  problemId String @map("problem_id")
  name      String
  version   Int
  title     String

  content  String
  copyable Boolean @default(false)

  memberships PeaMaterialMembership[]

  @@id([problemId, name, version])
  @@map("materials")
  @@schema("peassess")
}

model PeaMaterialMembership {
  id Int @id @default(autoincrement())

  materialName    String      @map("material_name")
  materialVersion Int         @map("material_version")
  material        PeaMaterial @relation(fields: [problemId, materialName, materialVersion], references: [problemId, name, version])

  problemId   String                 @map("problem_id")
  listVersion Int                    @map("list_version")
  list        PeaProblemMaterialList @relation(fields: [problemId, listVersion], references: [problemId, version])

  order Int

  @@unique([problemId, listVersion, materialName])
  @@unique([problemId, listVersion, order])
  @@map("bot_material_memberships")
  @@schema("peassess")
}

model PeaProblemMaterialList {
  problemId String @map("problem_id")
  version   Int

  materials PeaMaterialMembership[]
  problems  PeaProblem[]

  @@id([problemId, version])
  @@map("material_lists")
  @@schema("peassess")
}

model PeaSubmissionSpec {
  problemId String @map("problem_id")
  version   Int

  requiredPhrases String[] @map("required_phrases")
  maxLength       Int?     @map("max_length")

  prefill  String
  problems PeaProblem[]

  @@id([problemId, version])
  @@map("submission_spec")
  @@schema("peassess")
}

// Dataset

model PeaLabelPredictionPair {
  id        String @id @default(uuid())
  problemId String @map("problem_id")
  groupName String @map("group_name")
  name      String
  version   Int

  input  String
  output String

  memberships PeaLabelPredictionPairMembership[]
  tests       PeaLabelPredictionPairTest[]

  @@unique([problemId, groupName, name, version])
  @@map("label_prediction_pairs")
  @@schema("peassess")
}

model PeaLabelPredictionPairMembership {
  id Int @id @default(autoincrement())

  problemId    String @map("problem_id")
  groupName    String @map("group_name")
  groupVersion Int    @map("group_version")

  pairName    String @map("pair_name")
  pairVersion Int    @map("pair_version")

  order Int

  pair  PeaLabelPredictionPair  @relation(fields: [problemId, groupName, pairName, pairVersion], references: [problemId, groupName, name, version])
  group PeaLabelPredictionGroup @relation(fields: [problemId, groupName, groupVersion], references: [problemId, name, version])

  @@unique([problemId, groupName, groupVersion, pairName], map: "label_prediction_unique_pair_among_group")
  @@unique([problemId, groupName, groupVersion, order], map: "label_prediction_unique_order_among_group")
  @@map("label_prediction_pair_memberships")
  @@schema("peassess")
}

model PeaLabelPredictionGroup {
  problemId String @map("problem_id")
  name      String
  version   Int

  title      String
  hidesInput Boolean @default(false) @map("hides_input")

  pairs       PeaLabelPredictionPairMembership[]
  memberships PeaLabelPredictionGroupMembership[]

  @@id([problemId, name, version])
  @@map("label_prediction_groups")
  @@schema("peassess")
}

model PeaLabelPredictionGroupMembership {
  id Int @id @default(autoincrement())

  groupName    String                  @map("group_name")
  groupVersion Int                     @map("group_version")
  group        PeaLabelPredictionGroup @relation(fields: [problemId, groupName, groupVersion], references: [problemId, name, version])

  problemId   String                      @map("problem_id")
  listVersion Int                         @map("list_version")
  list        PeaLabelPredictionGroupList @relation(fields: [problemId, listVersion], references: [problemId, version])

  order Int

  @@unique([problemId, listVersion, groupName], map: "label_prediction_unique_group_among_group_list")
  @@unique([problemId, listVersion, order], map: "label_prediction_unique_order_among_group_list")
  @@map("label_prediction_group_memberships")
  @@schema("peassess")
}

model PeaLabelPredictionGroupList {
  problemId String @map("problem_id")
  version   Int

  groups   PeaLabelPredictionGroupMembership[]
  problems PeaProblem[]

  @@id([problemId, version])
  @@map("label_prediction_group_lists")
  @@schema("peassess")
}

// Test

model PeaTest {
  id         String  @id @default(uuid())
  isComplete Boolean @default(false) @map("is_complete")

  /// [PeajTestProblem]
  problems Json[]

  problemSetId String? @map("problem_set_id")
  problemSet   PeaProblemSet? @relation(fields: [problemSetId], references: [id])

  progresses PeaProblemProgress[]

  @@map("tests")
  @@schema("peassess")
}

model PeaProblemSet {
  id String @id @default(uuid())

  /// [PeajTestProblem]
  problems Json[]
  
  expireMinutes Int? @map("expire_minutes")

  tests PeaTest[]

  @@map("problem_sets")
  @@schema("peassess")
}

model PeaResolvedProblem {
  id             String @id @default(uuid())
  problemId      String @map("problem_id")
  problemVersion Int    @map("problem_version")

  problem PeaProblem @relation(fields: [problemId, problemVersion], references: [id, version])

  /// [PeajResolvedBotPresetList]
  resolvedPresets Json @map("resolved_presets")

  progresses PeaProblemProgress[]

  @@map("resolved_problems")
  @@schema("peassess")
}

model PeaProblemProgress {
  id String @id @default(uuid())

  testId String  @map("test_id")
  test   PeaTest @relation(fields: [testId], references: [id])

  resolvedProblemId String             @map("resolved_problem_id")
  resolvedProblem   PeaResolvedProblem @relation(fields: [resolvedProblemId], references: [id])

  createdAt                        DateTime  @map("created_at")
  completedAt                      DateTime? @map("completed_at")
  labelPredictionBatchTestUnlocked Boolean?  @map("label_prediction_batch_test_unlocked")

  axiiaChatId String              @unique @map("axiia_chat_id")
  axiiaChat   PeaProblemAxiiaChat @relation(fields: [axiiaChatId], references: [id])

  submissionSnapshots      PeaSubmissionSnapshot[]
  submissionTexts          PeaSubmissionTexts[]
  customChats              PeaProblemCustomChat[]
  labelPredictionPairTests PeaLabelPredictionPairTest[]

  @@unique([testId, resolvedProblemId])
  @@map("problem_progress")
  @@schema("peassess")
}

model PeaSubmissionSnapshot {
  id        String   @id @default(uuid())
  timestamp DateTime
  isValid   Boolean  @map("is_valid")

  progressId String             @map("progress_id")
  progress   PeaProblemProgress @relation(fields: [progressId], references: [id])

  textId String             @map("text_id")
  text   PeaSubmissionTexts @relation(fields: [textId], references: [id])

  feedback                 PeaFeedback?
  labelPredictionPairTests PeaLabelPredictionPairTest[]

  @@map("submission_snapshots")
  @@schema("peassess")
}

model PeaSubmissionTexts {
  id     String @id @default(uuid())
  sha256 Bytes
  text   String

  progressId String             @map("progress_id")
  progress   PeaProblemProgress @relation(fields: [progressId], references: [id])

  snapshots    PeaSubmissionSnapshot[]
  feedbackRuns PeaFeedbackRun[]

  @@unique([progressId, sha256])
  @@map("submission_texts")
  @@schema("peassess")
}

model PeaFeedback {
  id        String   @id @default(uuid())
  timestamp DateTime

  snapshotId String                @unique @map("snapshot_id")
  snapshot   PeaSubmissionSnapshot @relation(fields: [snapshotId], references: [id])

  feedbackMemberships PeaFeedbackRunMembership[]

  @@map("feedbacks")
  @@schema("peassess")
}

model PeaFeedbackRunMembership {
  id Int @id @default(autoincrement())

  runId String         @map("run_id")
  run   PeaFeedbackRun @relation(fields: [runId], references: [id])

  feedbackId String      @map("feedback_id")
  feedback   PeaFeedback @relation(fields: [feedbackId], references: [id])

  @@index([feedbackId])
  @@map("feedback_run_memberships")
  @@schema("peassess")
}

model PeaFeedbackRun {
  id String @id @default(uuid())

  createdAt DateTime @map("created_at")
  updatedAt DateTime @map("updated_at")

  problemId      String     @map("problem_id")
  problemVersion Int        @map("problem_version")
  problem        PeaProblem @relation(fields: [problemId, problemVersion], references: [id, version])

  submissionTextId String             @map("submission_id")
  submissionText   PeaSubmissionTexts @relation(fields: [submissionTextId], references: [id])

  output String?

  taskObservationId String? @map("task_observation_id")
  upstreamRequestId String? @map("upstream_request_id")
  upstreamErrored   Boolean @default(false) @map("upstream_errored")
  terminatedByUser  Boolean @default(false) @map("terminated_by_user")

  extractedFields String? @map("extracted_fields")
  renderedResult  String? @map("rendered_result")

  feedbackMemberships PeaFeedbackRunMembership[]

  @@map("feedback_runs")
  @@schema("peassess")
}

model PeaChat {
  id           String   @id @default(uuid())
  createdAt    DateTime @map("created_at")
  systemPrompt String   @map("system_prompt")

  chatRequests PeaChatRequest[]

  problemAxiiaChat  PeaProblemAxiiaChat?
  problemCustomChat PeaProblemCustomChat?

  @@map("chats")
  @@schema("peassess")
}

model PeaChatRequest {
  id     String  @id @default(uuid())
  chatId String  @map("chat_id")
  chat   PeaChat @relation(fields: [chatId], references: [id])

  createdAt DateTime @map("created_at")
  updatedAt DateTime @map("updated_at")

  input  String
  output String?

  streamingId       String? @map("streaming_id")
  upstreamRequestId String? @map("upstream_request_id")
  upstreamErrored   Boolean @default(false) @map("upstream_errored")
  terminatedByUser  Boolean @default(false) @map("terminated_by_user")

  @@map("chat_requests")
  @@schema("peassess")
}

model PeaProblemAxiiaChat {
  id   String  @id
  chat PeaChat @relation(fields: [id], references: [id])

  progress PeaProblemProgress?

  @@map("problem_axiia_chats")
  @@schema("peassess")
}

model PeaProblemCustomChat {
  id   String  @id
  chat PeaChat @relation(fields: [id], references: [id])

  progressId String             @map("progress_id")
  progress   PeaProblemProgress @relation(fields: [progressId], references: [id])

  title          String
  sourcePresetId String? @map("source_preset_id")

  @@map("problem_custom_chats")
  @@schema("peassess")
}

model PeaLabelPredictionPairTest {
  id        String   @id @default(uuid())
  timestamp DateTime @default(now())

  progressId String             @map("progress_id")
  progress   PeaProblemProgress @relation(fields: [progressId], references: [id])

  snapshotId String                @map("snapshot_id")
  snapshot   PeaSubmissionSnapshot @relation(fields: [snapshotId], references: [id])

  pairId String                 @map("pair_id")
  pair   PeaLabelPredictionPair @relation(fields: [pairId], references: [id])

  upstreamRequestId String? @map("upstream_request_id")

  output          String?
  extractedOutput String?  @map("extracted_output")
  isCorrect       Boolean? @map("is_correct")

  // Cannot be unique since the run could fail.
  @@index([snapshotId, pairId])
  @@index([progressId, pairId])
  @@map("label_prediction_pair_test")
  @@schema("peassess")
}

model PeaCustomChat {
  problemId String @map("problem_id")
  title     String
  version   Int

  systemPrompt   String  @map("system_prompt")
  initialMessage String[] @map("initial_message")

  memberships PeaCustomChatMembership[]

  @@id([problemId, title, version])
  @@map("custom_chats")
  @@schema("peassess")
}

model PeaCustomChatMembership {
  id Int @id @default(autoincrement())

  chatTitle    String        @map("chat_title")
  chatVersion  Int           @map("chat_version")
  chat         PeaCustomChat @relation(fields: [problemId, chatTitle, chatVersion], references: [problemId, title, version])

  problemId   String            @map("problem_id")
  listVersion Int               @map("list_version")
  list        PeaCustomChatList @relation(fields: [problemId, listVersion], references: [problemId, version])

  order Int

  @@unique([problemId, listVersion, chatTitle], map: "custom_chat_unique_chat_among_list")
  @@unique([problemId, listVersion, order], map: "custom_chat_unique_order_among_list")
  @@map("custom_chat_memberships")
  @@schema("peassess")
}

model PeaCustomChatList {
  problemId String @map("problem_id")
  version   Int

  chats    PeaCustomChatMembership[]
  problems PeaProblem[]

  @@id([problemId, version])
  @@map("custom_chat_lists")
  @@schema("peassess")
}
