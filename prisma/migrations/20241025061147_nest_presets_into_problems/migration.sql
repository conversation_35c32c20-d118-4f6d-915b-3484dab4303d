/*
  Warnings:

  - You are about to drop the `resolved_bot_presets` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `resolved_presets` to the `resolved_problems` table without a default value. This is not possible if the table is not empty.

*/

ALTER TABLE peassess.resolved_problems ADD COLUMN resolved_presets JSONB;

-- Perform the migration to nest the presets
WITH preset_groups AS (
  SELECT 
    test_id,
    problem_id,
    jsonb_agg(
      jsonb_build_object(
        'id', preset_id,
        'version', preset_version
      )
      ORDER BY "order"
    ) as presets_array
  FROM peassess.resolved_bot_presets
  GROUP BY test_id, problem_id
)
UPDATE peassess.resolved_problems
SET resolved_presets = COALESCE(preset_groups.presets_array, '[]'::jsonb)
FROM preset_groups
WHERE resolved_problems.test_id = preset_groups.test_id 
AND resolved_problems.problem_id = preset_groups.problem_id;

-- DropForeign<PERSON>ey
ALTER TABLE "peassess"."resolved_bot_presets" DROP CONSTRAINT "resolved_bot_presets_preset_id_preset_version_fkey";

-- DropForeignKey
ALTER TABLE "peassess"."resolved_bot_presets" DROP CONSTRAINT "resolved_bot_presets_test_id_problem_id_fkey";

ALTER TABLE "peassess"."resolved_problems" ALTER COLUMN "resolved_presets" SET NOT NULL;

-- DropTable
DROP TABLE "peassess"."resolved_bot_presets";
