/*
  Warnings:

  - You are about to drop the column `resolved_problems` on the `tests` table. All the data in the column will be lost.

*/

-- First add the column
ALTER TABLE peassess.tests 
ADD COLUMN problems JSONB[];

-- Then populate it
WITH problem_data AS (
  -- Get all the relationships between tests, resolved problems, and progress
  SELECT 
    t.id AS test_id,
    rp.problem_id,
    rp.problem_version,
    rp.id AS resolved_problem_id,
    pp.id AS progress_id
  FROM peassess.tests t
  CROSS JOIN UNNEST(t.resolved_problems) AS resolved_problem_id
  LEFT JOIN peassess.resolved_problems rp ON rp.id = resolved_problem_id
  LEFT JOIN peassess.problem_progress pp ON pp.test_id = t.id AND pp.resolved_problem_id = rp.id
)
UPDATE peassess.tests t
SET problems = (
  SELECT 
    array_agg(
      jsonb_build_object(
        'problem_id', pd.problem_id,
        'problem_version', pd.problem_version,
        'resolved_problem_id', pd.resolved_problem_id,
        'progress_id', pd.progress_id
      )
    )
  FROM problem_data pd
  WHERE pd.test_id = t.id
  GROUP BY pd.test_id
);

-- Add NOT NULL constraint after populating
ALTER TABLE peassess.tests 
ALTER COLUMN problems SET NOT NULL;

ALTER TABLE "peassess"."tests" DROP COLUMN "resolved_problems";
