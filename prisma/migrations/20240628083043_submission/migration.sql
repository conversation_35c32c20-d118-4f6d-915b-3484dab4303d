-- CreateTable
CREATE TABLE "v1_leetcode_interviews" (
    "id" SERIAL NOT NULL,
    "problem_id" INTEGER NOT NULL,
    "version" INTEGER NOT NULL,
    "random_id" TEXT NOT NULL,
    "recipient_name" TEXT NOT NULL,
    "recipient_email" TEXT NOT NULL,

    CONSTRAINT "v1_leetcode_interviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "v1_leetcode_submissions" (
    "id" SERIAL NOT NULL,
    "interview_id" INTEGER NOT NULL,
    "prompt" TEXT NOT NULL,
    "target_sample_versions" INTEGER[],

    CONSTRAINT "v1_leetcode_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "v1_leetcode_interview_submission_sample_results" (
    "submission_id" INTEGER NOT NULL,
    "sample_version" INTEGER NOT NULL,
    "sample_tag" "Tag" NOT NULL,
    "output" TEXT NOT NULL,
    "is_correct" BOOLEAN NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "v1_leetcode_interview_submission_sample_results_submission__key" ON "v1_leetcode_interview_submission_sample_results"("submission_id", "sample_version");

-- AddForeignKey
ALTER TABLE "v1_leetcode_interviews" ADD CONSTRAINT "v1_leetcode_interviews_problem_id_fkey" FOREIGN KEY ("problem_id") REFERENCES "v1_leetcode_problems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "v1_leetcode_submissions" ADD CONSTRAINT "v1_leetcode_submissions_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "v1_leetcode_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "v1_leetcode_interview_submission_sample_results" ADD CONSTRAINT "v1_leetcode_interview_submission_sample_results_submission_fkey" FOREIGN KEY ("submission_id") REFERENCES "v1_leetcode_submissions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
