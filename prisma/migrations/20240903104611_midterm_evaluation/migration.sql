-- CreateEnum
CREATE TYPE "D1EvaluationLevel" AS ENUM ('low', 'fair', 'high');

-- CreateTable
CREATE TABLE "d1_midterm_evaluations" (
    "id" TEXT NOT NULL,
    "stage" "D1Stage" NOT NULL,
    "level" "D1EvaluationLevel" NOT NULL,
    "feedback" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "interview_id" TEXT NOT NULL,

    CONSTRAINT "d1_midterm_evaluations_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "d1_midterm_evaluations" ADD CONSTRAINT "d1_midterm_evaluations_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "d1_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
