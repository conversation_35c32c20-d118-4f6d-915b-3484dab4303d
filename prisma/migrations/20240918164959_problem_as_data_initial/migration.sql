-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "peassess";

-- CreateTable
CREATE TABLE "peassess"."problems" (
    "id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "v_axiia_metadata" INTEGER NOT NULL,
    "v_bot_preset_list" INTEGER NOT NULL,
    "v_material_list" INTEGER NOT NULL,
    "v_submission_spec" INTEGER NOT NULL,
    "v_feedback_spec" INTEGER,
    "v_label_prediction_group_list" INTEGER,

    CONSTRAINT "problems_pkey" PRIMARY KEY ("id","version")
);

-- CreateTable
CREATE TABLE "peassess"."axiia_metadata" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "initial_message" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "prompt_replacement_key" TEXT NOT NULL,

    CONSTRAINT "axiia_metadata_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."feedback_spec" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "keys" TEXT[],
    "prompt" TEXT NOT NULL,
    "prompt_replacement_key" TEXT NOT NULL,
    "template" TEXT NOT NULL,

    CONSTRAINT "feedback_spec_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."bot_presets" (
    "id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,

    CONSTRAINT "bot_presets_pkey" PRIMARY KEY ("id","version")
);

-- CreateTable
CREATE TABLE "peassess"."bot_preset_lists" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "presets" TEXT[],

    CONSTRAINT "bot_preset_lists_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."materials" (
    "problem_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,

    CONSTRAINT "materials_pkey" PRIMARY KEY ("problem_id","name","version")
);

-- CreateTable
CREATE TABLE "peassess"."bot_material_memberships" (
    "id" SERIAL NOT NULL,
    "material_name" TEXT NOT NULL,
    "material_version" INTEGER NOT NULL,
    "problem_id" TEXT NOT NULL,
    "list_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "bot_material_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."material_lists" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,

    CONSTRAINT "material_lists_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."submission_spec" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "required_phrases" TEXT[],
    "max_length" INTEGER,
    "prefill" TEXT NOT NULL,

    CONSTRAINT "submission_spec_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_pairs" (
    "id" TEXT NOT NULL,
    "problem_id" TEXT NOT NULL,
    "group_name" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "input" TEXT NOT NULL,
    "output" TEXT NOT NULL,

    CONSTRAINT "label_prediction_pairs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_pair_memberships" (
    "id" SERIAL NOT NULL,
    "problem_id" TEXT NOT NULL,
    "group_name" TEXT NOT NULL,
    "group_version" INTEGER NOT NULL,
    "pair_name" TEXT NOT NULL,
    "pair_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "label_prediction_pair_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_groups" (
    "problem_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "hides_input" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "label_prediction_groups_pkey" PRIMARY KEY ("problem_id","name","version")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_group_memberships" (
    "id" SERIAL NOT NULL,
    "group_name" TEXT NOT NULL,
    "group_version" INTEGER NOT NULL,
    "problem_id" TEXT NOT NULL,
    "list_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "label_prediction_group_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_group_lists" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,

    CONSTRAINT "label_prediction_group_lists_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateTable
CREATE TABLE "peassess"."tests" (
    "id" TEXT NOT NULL,
    "is_complete" BOOLEAN NOT NULL DEFAULT false,
    "current_problem_id" TEXT,

    CONSTRAINT "tests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."resolved_problems" (
    "test_id" TEXT NOT NULL,
    "problem_id" TEXT NOT NULL,
    "problem_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "resolved_problems_pkey" PRIMARY KEY ("test_id","problem_id")
);

-- CreateTable
CREATE TABLE "peassess"."resolved_bot_presets" (
    "test_id" TEXT NOT NULL,
    "problem_id" TEXT NOT NULL,
    "preset_id" TEXT NOT NULL,
    "preset_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "resolved_bot_presets_pkey" PRIMARY KEY ("test_id","problem_id","preset_id")
);

-- CreateTable
CREATE TABLE "peassess"."problem_progress" (
    "id" TEXT NOT NULL,
    "test_id" TEXT NOT NULL,
    "problem_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL,
    "completed_at" TIMESTAMP(3),
    "label_prediction_batch_test_unlocked" BOOLEAN,
    "axiia_chat_id" TEXT NOT NULL,

    CONSTRAINT "problem_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."submission_snapshots" (
    "id" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "is_valid" BOOLEAN NOT NULL,
    "progress_id" TEXT NOT NULL,
    "text_id" TEXT NOT NULL,

    CONSTRAINT "submission_snapshots_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."submission_texts" (
    "id" TEXT NOT NULL,
    "sha256" BYTEA NOT NULL,
    "text" TEXT NOT NULL,
    "progress_id" TEXT NOT NULL,

    CONSTRAINT "submission_texts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."feedbacks" (
    "id" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "snapshot_id" TEXT NOT NULL,

    CONSTRAINT "feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."feedback_run_memberships" (
    "id" SERIAL NOT NULL,
    "run_id" TEXT NOT NULL,
    "feedback_id" TEXT NOT NULL,

    CONSTRAINT "feedback_run_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."feedback_runs" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "problem_id" TEXT NOT NULL,
    "problem_version" INTEGER NOT NULL,
    "submission_id" TEXT NOT NULL,
    "output" TEXT,
    "task_observation_id" TEXT,
    "upstream_request_id" TEXT,
    "upstream_errored" BOOLEAN NOT NULL DEFAULT false,
    "terminated_by_user" BOOLEAN NOT NULL DEFAULT false,
    "extracted_fields" TEXT,
    "rendered_result" TEXT,

    CONSTRAINT "feedback_runs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."chats" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL,
    "system_prompt" TEXT NOT NULL,

    CONSTRAINT "chats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."chat_requests" (
    "id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "input" TEXT NOT NULL,
    "output" TEXT,
    "streaming_id" TEXT,
    "upstream_request_id" TEXT,
    "upstream_errored" BOOLEAN NOT NULL DEFAULT false,
    "terminated_by_user" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "chat_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."problem_axiia_chats" (
    "id" TEXT NOT NULL,

    CONSTRAINT "problem_axiia_chats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."problem_custom_chats" (
    "id" TEXT NOT NULL,
    "progress_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "source_preset_id" TEXT,

    CONSTRAINT "problem_custom_chats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."label_prediction_pair_test" (
    "id" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "progress_id" TEXT NOT NULL,
    "snapshot_id" TEXT NOT NULL,
    "pair_id" TEXT NOT NULL,
    "upstream_request_id" TEXT,
    "output" TEXT,
    "extracted_output" TEXT,
    "is_correct" BOOLEAN,

    CONSTRAINT "label_prediction_pair_test_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "bot_material_memberships_problem_id_list_version_material_n_key" ON "peassess"."bot_material_memberships"("problem_id", "list_version", "material_name");

-- CreateIndex
CREATE UNIQUE INDEX "bot_material_memberships_problem_id_list_version_order_key" ON "peassess"."bot_material_memberships"("problem_id", "list_version", "order");

-- CreateIndex
CREATE UNIQUE INDEX "label_prediction_pairs_problem_id_group_name_name_version_key" ON "peassess"."label_prediction_pairs"("problem_id", "group_name", "name", "version");

-- CreateIndex
CREATE UNIQUE INDEX "label_prediction_unique_pair_among_group" ON "peassess"."label_prediction_pair_memberships"("problem_id", "group_name", "group_version", "pair_name");

-- CreateIndex
CREATE UNIQUE INDEX "label_prediction_unique_order_among_group" ON "peassess"."label_prediction_pair_memberships"("problem_id", "group_name", "group_version", "order");

-- CreateIndex
CREATE UNIQUE INDEX "label_prediction_unique_group_among_group_list" ON "peassess"."label_prediction_group_memberships"("problem_id", "list_version", "group_name");

-- CreateIndex
CREATE UNIQUE INDEX "label_prediction_unique_order_among_group_list" ON "peassess"."label_prediction_group_memberships"("problem_id", "list_version", "order");

-- CreateIndex
CREATE UNIQUE INDEX "tests_current_problem_id_key" ON "peassess"."tests"("current_problem_id");

-- CreateIndex
CREATE UNIQUE INDEX "resolved_problems_test_id_order_key" ON "peassess"."resolved_problems"("test_id", "order");

-- CreateIndex
CREATE UNIQUE INDEX "resolved_bot_presets_test_id_problem_id_order_key" ON "peassess"."resolved_bot_presets"("test_id", "problem_id", "order");

-- CreateIndex
CREATE UNIQUE INDEX "problem_progress_axiia_chat_id_key" ON "peassess"."problem_progress"("axiia_chat_id");

-- CreateIndex
CREATE UNIQUE INDEX "problem_progress_test_id_problem_id_key" ON "peassess"."problem_progress"("test_id", "problem_id");

-- CreateIndex
CREATE UNIQUE INDEX "submission_texts_progress_id_sha256_key" ON "peassess"."submission_texts"("progress_id", "sha256");

-- CreateIndex
CREATE UNIQUE INDEX "feedbacks_snapshot_id_key" ON "peassess"."feedbacks"("snapshot_id");

-- CreateIndex
CREATE INDEX "feedback_run_memberships_feedback_id_idx" ON "peassess"."feedback_run_memberships"("feedback_id");

-- CreateIndex
CREATE INDEX "label_prediction_pair_test_snapshot_id_pair_id_idx" ON "peassess"."label_prediction_pair_test"("snapshot_id", "pair_id");

-- CreateIndex
CREATE INDEX "label_prediction_pair_test_progress_id_pair_id_idx" ON "peassess"."label_prediction_pair_test"("progress_id", "pair_id");

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_axiia_metadata_fkey" FOREIGN KEY ("id", "v_axiia_metadata") REFERENCES "peassess"."axiia_metadata"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_bot_preset_list_fkey" FOREIGN KEY ("id", "v_bot_preset_list") REFERENCES "peassess"."bot_preset_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_material_list_fkey" FOREIGN KEY ("id", "v_material_list") REFERENCES "peassess"."material_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_submission_spec_fkey" FOREIGN KEY ("id", "v_submission_spec") REFERENCES "peassess"."submission_spec"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_feedback_spec_fkey" FOREIGN KEY ("id", "v_feedback_spec") REFERENCES "peassess"."feedback_spec"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_label_prediction_group_list_fkey" FOREIGN KEY ("id", "v_label_prediction_group_list") REFERENCES "peassess"."label_prediction_group_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."bot_material_memberships" ADD CONSTRAINT "bot_material_memberships_problem_id_material_name_material_fkey" FOREIGN KEY ("problem_id", "material_name", "material_version") REFERENCES "peassess"."materials"("problem_id", "name", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."bot_material_memberships" ADD CONSTRAINT "bot_material_memberships_problem_id_list_version_fkey" FOREIGN KEY ("problem_id", "list_version") REFERENCES "peassess"."material_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_pair_memberships" ADD CONSTRAINT "label_prediction_pair_memberships_problem_id_group_name_pa_fkey" FOREIGN KEY ("problem_id", "group_name", "pair_name", "pair_version") REFERENCES "peassess"."label_prediction_pairs"("problem_id", "group_name", "name", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_pair_memberships" ADD CONSTRAINT "label_prediction_pair_memberships_problem_id_group_name_gr_fkey" FOREIGN KEY ("problem_id", "group_name", "group_version") REFERENCES "peassess"."label_prediction_groups"("problem_id", "name", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_group_memberships" ADD CONSTRAINT "label_prediction_group_memberships_problem_id_group_name_g_fkey" FOREIGN KEY ("problem_id", "group_name", "group_version") REFERENCES "peassess"."label_prediction_groups"("problem_id", "name", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_group_memberships" ADD CONSTRAINT "label_prediction_group_memberships_problem_id_list_version_fkey" FOREIGN KEY ("problem_id", "list_version") REFERENCES "peassess"."label_prediction_group_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."tests" ADD CONSTRAINT "tests_current_problem_id_fkey" FOREIGN KEY ("current_problem_id") REFERENCES "peassess"."problem_progress"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."resolved_problems" ADD CONSTRAINT "resolved_problems_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "peassess"."tests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."resolved_problems" ADD CONSTRAINT "resolved_problems_problem_id_problem_version_fkey" FOREIGN KEY ("problem_id", "problem_version") REFERENCES "peassess"."problems"("id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."resolved_bot_presets" ADD CONSTRAINT "resolved_bot_presets_test_id_problem_id_fkey" FOREIGN KEY ("test_id", "problem_id") REFERENCES "peassess"."resolved_problems"("test_id", "problem_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."resolved_bot_presets" ADD CONSTRAINT "resolved_bot_presets_preset_id_preset_version_fkey" FOREIGN KEY ("preset_id", "preset_version") REFERENCES "peassess"."bot_presets"("id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problem_progress" ADD CONSTRAINT "problem_progress_test_id_problem_id_fkey" FOREIGN KEY ("test_id", "problem_id") REFERENCES "peassess"."resolved_problems"("test_id", "problem_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problem_progress" ADD CONSTRAINT "problem_progress_axiia_chat_id_fkey" FOREIGN KEY ("axiia_chat_id") REFERENCES "peassess"."problem_axiia_chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."submission_snapshots" ADD CONSTRAINT "submission_snapshots_progress_id_fkey" FOREIGN KEY ("progress_id") REFERENCES "peassess"."problem_progress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."submission_snapshots" ADD CONSTRAINT "submission_snapshots_text_id_fkey" FOREIGN KEY ("text_id") REFERENCES "peassess"."submission_texts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."submission_texts" ADD CONSTRAINT "submission_texts_progress_id_fkey" FOREIGN KEY ("progress_id") REFERENCES "peassess"."problem_progress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."feedbacks" ADD CONSTRAINT "feedbacks_snapshot_id_fkey" FOREIGN KEY ("snapshot_id") REFERENCES "peassess"."submission_snapshots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."feedback_run_memberships" ADD CONSTRAINT "feedback_run_memberships_run_id_fkey" FOREIGN KEY ("run_id") REFERENCES "peassess"."feedback_runs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."feedback_run_memberships" ADD CONSTRAINT "feedback_run_memberships_feedback_id_fkey" FOREIGN KEY ("feedback_id") REFERENCES "peassess"."feedbacks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."feedback_runs" ADD CONSTRAINT "feedback_runs_problem_id_problem_version_fkey" FOREIGN KEY ("problem_id", "problem_version") REFERENCES "peassess"."problems"("id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."feedback_runs" ADD CONSTRAINT "feedback_runs_submission_id_fkey" FOREIGN KEY ("submission_id") REFERENCES "peassess"."submission_texts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."chat_requests" ADD CONSTRAINT "chat_requests_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "peassess"."chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problem_axiia_chats" ADD CONSTRAINT "problem_axiia_chats_id_fkey" FOREIGN KEY ("id") REFERENCES "peassess"."chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problem_custom_chats" ADD CONSTRAINT "problem_custom_chats_id_fkey" FOREIGN KEY ("id") REFERENCES "peassess"."chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."problem_custom_chats" ADD CONSTRAINT "problem_custom_chats_progress_id_fkey" FOREIGN KEY ("progress_id") REFERENCES "peassess"."problem_progress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_pair_test" ADD CONSTRAINT "label_prediction_pair_test_progress_id_fkey" FOREIGN KEY ("progress_id") REFERENCES "peassess"."problem_progress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_pair_test" ADD CONSTRAINT "label_prediction_pair_test_snapshot_id_fkey" FOREIGN KEY ("snapshot_id") REFERENCES "peassess"."submission_snapshots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."label_prediction_pair_test" ADD CONSTRAINT "label_prediction_pair_test_pair_id_fkey" FOREIGN KEY ("pair_id") REFERENCES "peassess"."label_prediction_pairs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
