-- AlterTable
ALTER TABLE "peassess"."tests" ADD COLUMN     "problem_set_id" TEXT;

-- CreateTable
CREATE TABLE "peassess"."problem_sets" (
    "id" TEXT NOT NULL,
    "problems" JSONB[],

    CONSTRAINT "problem_sets_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "peassess"."tests" ADD CONSTRAINT "tests_problem_set_id_fkey" FOREIGN KEY ("problem_set_id") REFERENCES "peassess"."problem_sets"("id") ON DELETE SET NULL ON UPDATE CASCADE;
