/*
  Warnings:

  - You are about to drop the column `problem_id` on the `problem_progress` table. All the data in the column will be lost.
  - The primary key for the `resolved_problems` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `order` on the `resolved_problems` table. All the data in the column will be lost.
  - You are about to drop the column `test_id` on the `resolved_problems` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[test_id,resolved_problem_id]` on the table `problem_progress` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `resolved_problem_id` to the `problem_progress` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `resolved_problems` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/

-- Add resolved_problems.id

ALTER TABLE "peassess"."resolved_problems" 
  ADD COLUMN "id" TEXT;
  
UPDATE "peassess"."resolved_problems" 
  SET "id" = gen_random_uuid()::TEXT;

ALTER TABLE "peassess"."resolved_problems"  
  ALTER COLUMN "id" SET NOT NULL;

-- Copy resolved_problems.id to problem_progress

ALTER TABLE "peassess"."problem_progress"
  ADD COLUMN "resolved_problem_id" TEXT;

UPDATE "peassess"."problem_progress" pp
  SET "resolved_problem_id" = rp.id
  FROM "peassess"."resolved_problems" rp
  WHERE pp."test_id" = rp."test_id" 
  AND pp."problem_id" = rp."problem_id";

ALTER TABLE "peassess"."problem_progress"
  ALTER COLUMN "resolved_problem_id" SET NOT NULL,
  DROP CONSTRAINT "problem_progress_test_id_problem_id_fkey",
  DROP COLUMN "problem_id";

-- We need to drop the above fkey to drop the old primary key constraint.
ALTER TABLE "peassess"."resolved_problems" 
  DROP CONSTRAINT "resolved_problems_pkey",
  ADD CONSTRAINT "resolved_problems_pkey" PRIMARY KEY ("id");

CREATE UNIQUE INDEX "problem_progress_test_id_resolved_problem_id_key" ON "peassess"."problem_progress"("test_id", "resolved_problem_id");

ALTER TABLE "peassess"."problem_progress" ADD CONSTRAINT "problem_progress_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "peassess"."tests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "peassess"."problem_progress" ADD CONSTRAINT "problem_progress_resolved_problem_id_fkey" FOREIGN KEY ("resolved_problem_id") REFERENCES "peassess"."resolved_problems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Update tests.resolved_problems

ALTER TABLE "peassess"."tests"
  ADD COLUMN "resolved_problems" TEXT[];

UPDATE "peassess"."tests" t
SET "resolved_problems" = (
  SELECT array_agg(rp.id ORDER BY rp."order")
  FROM "peassess"."resolved_problems" rp
  WHERE rp."test_id" = t.id
  GROUP BY rp."test_id"
);

ALTER TABLE "peassess"."tests"
  ALTER COLUMN "resolved_problems" SET NOT NULL;

ALTER TABLE "peassess"."resolved_problems"
  DROP CONSTRAINT "resolved_problems_test_id_fkey",
  DROP COLUMN "test_id",
  DROP COLUMN "order";
