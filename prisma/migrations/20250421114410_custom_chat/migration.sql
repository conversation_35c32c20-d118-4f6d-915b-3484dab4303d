-- AlterTable
ALTER TABLE "peassess"."problems" ADD COLUMN     "v_custom_chat_list" INTEGER;

-- CreateTable
CREATE TABLE "peassess"."custom_chats" (
    "problem_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "system_prompt" TEXT NOT NULL,
    "initial_message" TEXT[],

    CONSTRAINT "custom_chats_pkey" PRIMARY KEY ("problem_id","title","version")
);

-- CreateTable
CREATE TABLE "peassess"."custom_chat_memberships" (
    "id" SERIAL NOT NULL,
    "chat_title" TEXT NOT NULL,
    "chat_version" INTEGER NOT NULL,
    "problem_id" TEXT NOT NULL,
    "list_version" INTEGER NOT NULL,
    "order" INTEGER NOT NULL,

    CONSTRAINT "custom_chat_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peassess"."custom_chat_lists" (
    "problem_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL,

    CONSTRAINT "custom_chat_lists_pkey" PRIMARY KEY ("problem_id","version")
);

-- CreateIndex
CREATE UNIQUE INDEX "custom_chat_unique_chat_among_list" ON "peassess"."custom_chat_memberships"("problem_id", "list_version", "chat_title");

-- CreateIndex
CREATE UNIQUE INDEX "custom_chat_unique_order_among_list" ON "peassess"."custom_chat_memberships"("problem_id", "list_version", "order");

-- AddForeignKey
ALTER TABLE "peassess"."problems" ADD CONSTRAINT "problems_id_v_custom_chat_list_fkey" FOREIGN KEY ("id", "v_custom_chat_list") REFERENCES "peassess"."custom_chat_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."custom_chat_memberships" ADD CONSTRAINT "custom_chat_memberships_problem_id_chat_title_chat_version_fkey" FOREIGN KEY ("problem_id", "chat_title", "chat_version") REFERENCES "peassess"."custom_chats"("problem_id", "title", "version") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peassess"."custom_chat_memberships" ADD CONSTRAINT "custom_chat_memberships_problem_id_list_version_fkey" FOREIGN KEY ("problem_id", "list_version") REFERENCES "peassess"."custom_chat_lists"("problem_id", "version") ON DELETE RESTRICT ON UPDATE CASCADE;
