-- AlterEnum
ALTER TYPE "D1Problem" ADD VALUE 'p5';

-- AlterEnum
ALTER TYPE "D1Stage" ADD VALUE 'p5';

-- CreateTable
CREATE TABLE "d1p5_sample_submissions" (
    "id" TEXT NOT NULL,
    "interview_id" TEXT NOT NULL,
    "sample_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "prompt" TEXT NOT NULL,
    "llm_output" TEXT NOT NULL,
    "is_correct" BOOLEAN NOT NULL,

    CONSTRAINT "d1p5_sample_submissions_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "d1p5_sample_submissions" ADD CONSTRAINT "d1p5_sample_submissions_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "d1_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
