/*
  Warnings:

  - A unique constraint covering the columns `[random_id]` on the table `v1_leetcode_interviews` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "D1Stage" AS ENUM ('intro', 'p1', 'p2', 'p3', 'p4', 'done');

-- CreateEnum
CREATE TYPE "D1Problem" AS ENUM ('p1', 'p2', 'p3', 'p4');

-- CreateTable
CREATE TABLE "D1InterviewInvitation" (
    "id" TEXT NOT NULL,
    "recipient_name" TEXT NOT NULL,
    "recipient_email" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "D1InterviewInvitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1_interviews" (
    "id" TEXT NOT NULL,
    "invitation_id" TEXT NOT NULL,
    "stage" "D1Stage" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "d1_interviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1_chats" (
    "id" TEXT NOT NULL,
    "axiia_stage" "D1Stage",
    "topic" TEXT NOT NULL,
    "instruction" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "interview_id" TEXT NOT NULL,

    CONSTRAINT "d1_chats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1_user_messages" (
    "id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "content" TEXT NOT NULL,

    CONSTRAINT "d1_user_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1_assistant_messages" (
    "id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_modified_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "used_substs" TEXT[],
    "content" TEXT NOT NULL,

    CONSTRAINT "d1_assistant_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1p2_sample_submissions" (
    "id" TEXT NOT NULL,
    "interview_id" TEXT NOT NULL,
    "sample_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "prompt" TEXT NOT NULL,
    "llm_output" TEXT NOT NULL,
    "is_correct" BOOLEAN NOT NULL,

    CONSTRAINT "d1p2_sample_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1p2_test_submissions" (
    "id" TEXT NOT NULL,
    "interview_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "prompt" TEXT NOT NULL,
    "is_complete" BOOLEAN NOT NULL,

    CONSTRAINT "d1p2_test_submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d1p2_test_submission_samples" (
    "submissionID" TEXT NOT NULL,
    "sample_id" TEXT NOT NULL,
    "llm_output" TEXT NOT NULL,
    "is_correct" BOOLEAN NOT NULL
);

-- CreateTable
CREATE TABLE "d1_submissions" (
    "interview_id" TEXT NOT NULL,
    "problem" "D1Problem" NOT NULL,
    "submission" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "d1_interviews_invitation_id_key" ON "d1_interviews"("invitation_id");

-- CreateIndex
CREATE UNIQUE INDEX "d1p2_test_submission_samples_submissionID_sample_id_key" ON "d1p2_test_submission_samples"("submissionID", "sample_id");

-- CreateIndex
CREATE UNIQUE INDEX "d1_submissions_interview_id_problem_key" ON "d1_submissions"("interview_id", "problem");

-- CreateIndex
CREATE UNIQUE INDEX "v1_leetcode_interviews_random_id_key" ON "v1_leetcode_interviews"("random_id");

-- AddForeignKey
ALTER TABLE "d1_interviews" ADD CONSTRAINT "d1_interviews_invitation_id_fkey" FOREIGN KEY ("invitation_id") REFERENCES "D1InterviewInvitation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1_chats" ADD CONSTRAINT "d1_chats_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "d1_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1_user_messages" ADD CONSTRAINT "d1_user_messages_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "d1_chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1_assistant_messages" ADD CONSTRAINT "d1_assistant_messages_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "d1_chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1p2_sample_submissions" ADD CONSTRAINT "d1p2_sample_submissions_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "d1_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1p2_test_submission_samples" ADD CONSTRAINT "d1p2_test_submission_samples_submissionID_fkey" FOREIGN KEY ("submissionID") REFERENCES "d1p2_test_submissions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d1_submissions" ADD CONSTRAINT "d1_submissions_interview_id_fkey" FOREIGN KEY ("interview_id") REFERENCES "d1_interviews"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
