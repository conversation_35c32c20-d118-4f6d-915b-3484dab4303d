-- CreateEnum
CREATE TYPE "Tag" AS ENUM ('train', 'eval', 'test');

-- CreateTable
CREATE TABLE "perspective_users" (
    "id" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "perspective_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "v1_leetcode_problems" (
    "id" SERIAL NOT NULL,
    "problem_title" TEXT NOT NULL,
    "problem_detail" TEXT NOT NULL,

    CONSTRAINT "v1_leetcode_problems_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "v1_leetcode_samples" (
    "id" SERIAL NOT NULL,
    "problem_id" INTEGER NOT NULL,
    "tag" "Tag" NOT NULL,
    "deletedAt" INTEGER,

    CONSTRAINT "v1_leetcode_samples_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "v1_leetcode_sample_versions" (
    "problem_id" INTEGER NOT NULL,
    "sample_id" INTEGER NOT NULL,
    "version" INTEGER NOT NULL,
    "input" TEXT NOT NULL,
    "output" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "perspective_users_email_key" ON "perspective_users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "v1_leetcode_sample_versions_problem_id_version_key" ON "v1_leetcode_sample_versions"("problem_id", "version");

-- AddForeignKey
ALTER TABLE "v1_leetcode_samples" ADD CONSTRAINT "v1_leetcode_samples_problem_id_fkey" FOREIGN KEY ("problem_id") REFERENCES "v1_leetcode_problems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "v1_leetcode_sample_versions" ADD CONSTRAINT "v1_leetcode_sample_versions_problem_id_fkey" FOREIGN KEY ("problem_id") REFERENCES "v1_leetcode_problems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "v1_leetcode_sample_versions" ADD CONSTRAINT "v1_leetcode_sample_versions_sample_id_fkey" FOREIGN KEY ("sample_id") REFERENCES "v1_leetcode_samples"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
