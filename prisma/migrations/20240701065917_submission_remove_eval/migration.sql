/*
  Warnings:

  - The values [eval] on the enum `Tag` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `input` to the `v1_leetcode_interview_submission_sample_results` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "Tag_new" AS ENUM ('train', 'test', 'evaluation');
ALTER TABLE "v1_leetcode_samples" ALTER COLUMN "tag" TYPE "Tag_new" USING ("tag"::text::"Tag_new");
ALTER TABLE "v1_leetcode_interview_submission_sample_results" ALTER COLUMN "sample_tag" TYPE "Tag_new" USING ("sample_tag"::text::"Tag_new");
ALTER TYPE "Tag" RENAME TO "Tag_old";
ALTER TYPE "Tag_new" RENAME TO "Tag";
DROP TYPE "Tag_old";
COMMIT;

-- AlterTable
ALTER TABLE "v1_leetcode_interview_submission_sample_results" ADD COLUMN     "input" TEXT NOT NULL;
