-- CreateTable
CREATE TABLE "d3_chats" (
    "id" TEXT NOT NULL,
    "topic" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "bot_id" TEXT NOT NULL,
    "user_id" TEXT,

    CONSTRAINT "d3_chats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d3_messages" (
    "id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "content" TEXT NOT NULL,
    "is_user" BOOLEAN NOT NULL,

    CONSTRAINT "d3_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d3_question_bots" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "system_prompt" TEXT NOT NULL,

    CONSTRAINT "d3_question_bots_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "d3_question_bot_messages" (
    "id" TEXT NOT NULL,
    "chat_id" TEXT NOT NULL,
    "source_message_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "content" TEXT NOT NULL,

    CONSTRAINT "d3_question_bot_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "axiia_users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "axiia_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "axiia_users_email_key" ON "axiia_users"("email");

-- AddForeignKey
ALTER TABLE "d3_chats" ADD CONSTRAINT "d3_chats_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "axiia_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d3_chats" ADD CONSTRAINT "d3_chats_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "d3_question_bots"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d3_messages" ADD CONSTRAINT "d3_messages_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "d3_chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d3_question_bot_messages" ADD CONSTRAINT "d3_question_bot_messages_source_message_id_fkey" FOREIGN KEY ("source_message_id") REFERENCES "d3_messages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "d3_question_bot_messages" ADD CONSTRAINT "d3_question_bot_messages_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "d3_chats"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
